<template>
  <a4-card size="small" title="门店国补审计数据">
    <a4-form
      size="small"
      ref="formRef"
      labelAlign="right"
      :model="formState"
      :rules="rules"
      :labelCol="{ span: 3, offset: 0 }"
      :wrapperCol="{span: 21, offset: 0 }"
      style="padding-top: 1rem;"
    >
      <a4-form-item
        label="门店"
        ref="storeId"
        name="storeId"
      >
        <a4-select
          allowClear
          v-model:value="formState.storeId"
          show-search
          placeholder="请选择门店"
          style="width: 200px"
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          :options="storeOptions"
          @search="handleStoreSearch"
          @change="handleStoreChange"
        ></a4-select>
      </a4-form-item>

      <a4-form-item
        label="下单时间"
        ref="timeRange"
        name="timeRange"
      >
        <a4-range-picker
          size="small"
          v-model:value="formState.timeRange"
          :disabled="downloadCenterStoreSubsidyOrdersAuditDataState.download.status === 'PENDING'"
        />
      </a4-form-item>

      <a4-form-item
        :colon="false"
        label="&nbsp;"
      >
        <a4-button
          size="small"
          type="primary"
          @click="handleSubmit"
          :disabled="downloadCenterStoreSubsidyOrdersAuditDataState.download.status === 'PENDING'"
          :loading="downloadCenterStoreSubsidyOrdersAuditDataState.download.status === 'PENDING'"
        >下载</a4-button>
      </a4-form-item>
    </a4-form>
  </a4-card>
</template>

<script lang="ts">
import { compact } from 'lodash'
import { defineComponent, ref, watch, onMounted, reactive, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Card as A4Card,
  Select as A4Select,
  SelectOption as A4SelectOption,
  Input as A4Input,
  Button as A4Button,
  RangePicker as A4RangePicker,
  Form as A4Form,
  FormItem as A4FormItem,
  message,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4Card,
    A4Input,
    A4Button,
    A4Select,
    A4SelectOption,
    A4RangePicker,
    A4Form,
    A4FormItem,
  },
  setup() {
    const store = useStore()
    const formRef = ref();    
    const storeKW = ref();
    const {
      // downloadCenterGrantPermissionState,
      downloadCenterStoreSubsidyOrdersAuditDataState,
      // searchPermissionStoreState,
    } = store.state.oms

    const {
      storesState
    } = store.state.global

    const storeOptions = computed(() => {
      const options = storesState?.allStore?.data?.data?.permissionStoreList?.map((store) => {
        if (storeKW.value) {
          const re = new RegExp(storeKW.value, 'i')
          if (re.test(store?.storeNameCn)) return { value: store?.storeNo, label: store?.storeNameCn }
        } else {
          return { value: store?.storeNo, label: store?.storeNameCn }
        }
      })
      return compact(options)
    })

    const formState = reactive({
      timeRange: [],
      storeId: undefined,
    })

    const isValidTimeRange = (e, v) => {
      if (v.length) {
        return Promise.resolve()
      } else {
        return Promise.reject('请选择需要查询的下单时间')
      }
    }

    const rules = {
      timeRange: [
        { validator: isValidTimeRange, trigger: ['blur', 'change'] }
      ],
    }

    const handleStoreSearch = (val) => {
      storeKW.value = val
    }
    
    const handleStoreChange = (val) => {
      storeKW.value = undefined
    }
    
    const handleSubmit = async () => {
      console.log('submit')
      formRef.value.validate()
        .then(async (res) => {
          // console.log('submit', res)
          const { timeRange } = res
          const [ from, end ] = timeRange || []
          const ret = await store.dispatch('oms/downloadStoreSubsidyOrdersAuditData', {
            storeId: formState?.storeId,
            fromTimeStamp: from ? from.startOf('day').valueOf() : undefined,
            endTimeStamp: end ? end.endOf('day').valueOf() : undefined,
          })

          if (ret instanceof Error || downloadCenterStoreSubsidyOrdersAuditDataState?.download?.status === 'REJECTED') {
            message.error(`${ret?.response?.data?.msg || '下载请求发送失败, 请稍后再试'}`)
          } else {
            if (ret?.code === '0') {
              message.success(`下载请求发送成功, 请至下载中心查看`)
            } else {
              message.error(`${ret?.msg || '下载请求发送失败, 请稍后再试'}`)
            }
          }
        })
        .catch((err) => {
          console.log('err', err)
        })
    }

    return {
      formRef,
      formState,
      rules,
      handleSubmit,
      // searchPermissionStoreState,
      // downloadCenterGrantPermissionState,
      downloadCenterStoreSubsidyOrdersAuditDataState,
      handleStoreSearch,
      handleStoreChange,
      storeOptions,
      storeKW,
      storesState,
    }
  }
})
</script>
