<template>
  <a4-upload-dragger
    style="width: 80%; padding: 0 16px;"
    v-model:fileList="fileList"
    :show-upload-list="false"
    name="file"
    list-type="text"
    :multiple="false"
    :action="`${ENV.VITE_API_HOST_KONG}/jdBill/upload`"
    :before-upload="beforeUpload"
    @change="handleChange"
    :withCredentials="true"
    accept=".csv"
  >
    <!-- <a4-button>
      <template #icon><UploadOutlined /></template>
      点击上传导入
    </a4-button> -->
    <p class="ant-upload-drag-icon">
      <inbox-outlined></inbox-outlined>
    </p>
    <p class="ant-upload-text">点击或者拖拽上传导入</p>
    <!-- <p class="ant-upload-hint">
      {{ fileList[fileList.length - 1] }}
    </p> -->
  </a4-upload-dragger>
</template>

<script>
import {
  UploadDragger as A4UploadDragger,
  But<PERSON> as <PERSON>4<PERSON><PERSON>on,
  message
} from 'ant-design-vue-4'
import { defineComponent, ref, watch, onMounted } from 'vue'
import { useStore } from 'vuex'
import { UploadOutlined, InboxOutlined } from '@ant-design/icons-vue';
import { ENV } from '@/utils'

export default defineComponent({
  components: {
    A4UploadDragger,
    UploadOutlined,
    InboxOutlined,
    A4Button,
  },
  setup() {
    const store = useStore()
    const fileList = ref([])
    
    const beforeUpload = (file) => {
      const isCsvFile =
        /csv/.test(file.type)

      if (!isCsvFile) {
        message.error('仅支持上传.csv文件, csv');
      }

      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        message.error('文件大小限制 5MB!');
      }

      return isCsvFile && isLt5M;
    };

    const handleChange = (data) => {
      if (data?.file?.status === 'done') {
        console.log('data?', data)
        const response = data?.file?.response || {}
        if (response.code === '0') {
          message.success('上传成功，请到下载列表获取查询结果！');
          store.commit('oms/updateGrantPermissionUploadRes', response)
        } else {
          message.error('上传失败，请重试！');
        }
      } else if (data?.file?.status === 'error') {
        message.error(data?.file?.response?.msg || '上传失败，请重试！');
      }
    }

    return {
      ENV,
      beforeUpload,
      handleChange,
      fileList,
    }
  },
})
</script>
