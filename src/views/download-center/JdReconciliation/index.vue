<template>
  <a4-result
    v-if="downloadCenterStoreInvoiceState?.storeInvoiceUpload?.res?.code === '0'"
    status="success"
    title="上传成功"
    sub-title="请至下载中心查看附件"
  >
    <template #extra>
      <a4-button size="small" key="console" type="primary" @click.prevent.stop="handleGoToDownloadList">转到下载列表</a4-button>
    </template>
  </a4-result>

  <a4-form v-else>
    <a4-form-item>
      <file-uploader></file-uploader>
    </a4-form-item>
  </a4-form>
</template>

<script>
import { Button as A4Button, FormItem as A4FormItem, Form as A4Form } from 'ant-design-vue-4'
import { defineComponent, ref, watch, onMounted } from 'vue'
import { useStore } from 'vuex'
import FileUploader from './FileUploader/index.vue'
import { Result as A4Result, } from 'ant-design-vue-4'

export default defineComponent({
  components: {
    FileUploader,
    A4<PERSON>utton,
    A4Result,
    A4FormItem,
    A4Form,
  },
  setup() {
    const store = useStore()
    const {
      downloadCenterStoreInvoiceState,
    } = store.state.oms

    const handleGoToDownloadList = () => {
      store.commit('oms/updateDownloadCenterCurrentTabKey',  'downloadList')
    }

    return {
      handleGoToDownloadList,
      downloadCenterStoreInvoiceState
    }
  }
})
</script>
