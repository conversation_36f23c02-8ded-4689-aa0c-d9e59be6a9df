<template>
  <div class="invoice-list-table">
    <!-- {{ invoiceListState?.formState }} -->
    <!-- {{ stores }} -->
    <!-- {{ invoiceListState?.data?.data }} -->
    <!-- {{ invoiceDetailState?.redraft }} -->
    <a4-table
      bordered
      size="small"
      :scroll="{
        x: true,
        // y: 420
      }"
      :columns="columns"
      :pagination="false"
      :locale="{ emptyText: '暂无数据' }"
      :data-source="invoiceListState?.data?.data?.invoiceDetails || []"
      :loading="invoiceListState.status === 'PENDING'"
      :rowKey="(record: any) => record?.id"
    >
      <template #headerCell="{ column }">
        <span class="nowrap">{{column.title}}</span>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'invoiceNo'">
          <!-- // GENERAL_DIGITAL_INVOICE:数电普票, SPECIAL_DIGITAL_INVOICE:数电专票 -->
          <span class="nowrap">
            {{ record.invoiceType
              ? JD_INVOICE_TYPE[record.invoiceType] 
                ? JD_INVOICE_TYPE[record.invoiceType] + ' | '
                : record.invoiceType + ' | '
              : ''
            }}
            {{ record.invoiceNo || '--' }}
          </span>
        </template>
        <template v-if="column.key === 'hasRedInfo'">
          <span class="nowrap">
            {{ record.hasRedInfo ? '是' : '否' }}
          </span>
        </template>
        <template v-if="column.key === 'invoiceStatus'">
          <a4-tooltip v-if="record?.failureMsg">
            <template #title>
              {{ record?.failureMsg }}
            </template>
            <span class="nowrap">
              {{
                record.invoiceStatus
                  ? JD_INVOICE_STATUS[record.invoiceStatus]
                    ? JD_INVOICE_STATUS[record.invoiceStatus]
                    : record.invoiceStatus
                  : ''
              }}
            </span>
          </a4-tooltip>
          <span class="nowrap" v-else>
            {{
              record.invoiceStatus
                ? JD_INVOICE_STATUS[record.invoiceStatus]
                  ? JD_INVOICE_STATUS[record.invoiceStatus]
                  : record.invoiceStatus
                : ''
            }}
          </span>
        </template>
        <template v-if="column.key === 'uploadState'">
          {{ record.uploadState && JD_INVOICE_UPLOAD_STATE[record.uploadState] }}
        </template>
        <template v-if="column.key === 'orderId'">
          <span class="nowrap">{{record.orderId}}</span>
        </template>
        <template v-if="column.key === 'invoiceDeadlineStamp'">
          <!-- :time="25 * 60 * 60 * 1000" -->
          <countdown
            v-if="record.invoiceDeadlineStamp && (record.invoiceDeadlineStamp - Date.now()) >= 0"
            :time="record.invoiceDeadlineStamp - Date.now()"
            format="DD HH:mm:ss"
          >
            <template #="{ resolved }">
              <span class="countdown-item">{{ resolved.DD }}</span>天
              <span class="countdown-item">{{ resolved.HH }}</span> :
              <span class="countdown-item">{{ resolved.mm }}</span> :
              <span class="countdown-item">{{ resolved.ss }}</span>
            </template>
          </countdown>
        </template>
        <template v-else-if="column.key === 'invoiceTimeStamp'">
          {{ record.invoiceTimeStamp && dayjs(record.invoiceTimeStamp).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template v-else-if="column.key === 'amountWithTax'">
          <template v-if="record?.amountWithTax">
            {{ record?.issueType === 'RED' ? `-${record?.amountWithTax}` : record?.amountWithTax }}
          </template>
        </template>
        <template v-else-if="column.key === 'amountWithoutTax'">
          <template v-if="record?.amountWithoutTax">
            {{ record?.issueType === 'RED' ? `-${record?.amountWithoutTax}` : record?.amountWithoutTax }}
          </template>
        </template>
        <template v-else-if="column.key === 'taxAmount'">
          <template v-if="record?.taxAmount">
            {{ record?.issueType === 'RED' ? `-${record?.taxAmount}` : record?.taxAmount }}
          </template>
        </template>
        <template v-else-if="column.key === 'remark'">
          {{ record.remark || '--' }}
        </template>
        <template v-else-if="column.key === 'invoiceUse_billingState'">
          {{ 
            compact([
              record.invoiceUse && JD_INVOICE_USE_TYPES[record.invoiceUse] || record.invoiceUse,
              record.billingState && JD_INVOICE_BILLING_STATE[record.billingState] || record.billingState
            ]).join('/')
          }}
        </template>
        <template v-else-if="column.key === 'operation'">
          <record-actions :id="record?.id" />
        </template>
        <template v-else>
          <!-- <span class="nowrap">{{record[column.dataIndex]}}</span> -->
        </template>
      </template>
    </a4-table>
    
    <div class="pagination" v-if="invoiceListState?.data?.data">
      <a4-pagination
        size="small"
        v-if="invoiceListState?.data?.data?.total > 0"
        :disabled="invoiceListState.status === 'PENDING'"
        v-model:current="invoiceListState.formState.page"
        :total="invoiceListState?.data?.data?.total"
        @change="handlePageChange"
        :pageSize="invoiceListState.formState.size"
        :showSizeChanger="true"
        @showSizeChange="handlePageSizeChange"
        :pageSizeOptions="[
          String(invoiceListState.formState.baseSize * 1),
          String(invoiceListState.formState.baseSize * 2),
          String(invoiceListState.formState.baseSize * 4),
          String(invoiceListState.formState.baseSize * 10),
        ]"
        :show-total="(total: any) => `共 ${total || 0} 条`"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'
import moment from 'moment';
import Countdown from 'vue3-countdown'
import {
  Popover as A4Popover,
  Table as A4Table,
  Tooltip as A4Tooltip,
  Pagination as A4Pagination,
} from 'ant-design-vue-4'
import columns from './columns'
import RecordActions from './RecordActions/index.vue'
import { invert, find, compact } from 'lodash'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime';
import updateLocale from 'dayjs/plugin/updateLocale';
import IkPrice from '@/stories/components/Price/index.vue'
import {
  JD_INVOICE_TYPE,
  JD_INVOICE_STATUS,
  JD_INVOICE_UPLOAD_STATE,
  JD_INVOICE_BILLING_STATE,
  JD_INVOICE_USE_TYPES,
} from '@/enums'

dayjs.extend(relativeTime)
dayjs.extend(updateLocale)
dayjs.locale('zh-cn')

export default defineComponent({
  components: {
    A4Table,
    A4Tooltip,
    A4Pagination,
    A4Popover,
    IkPrice,
    RecordActions,
    Countdown,
  },
  setup () {
    const store = useStore();

    const { storesState } = store.state.global;
    const { invoiceListState, invoiceDetailState } = store.state.invoiceCenter;
    
    const stores = computed(() => {
      return storesState.data
    })

    const handlePageChange = (pageNumber: number) => {
      store.dispatch('invoiceCenter/fetchInvoiceList', {
        ...invoiceListState.formState,
        page: pageNumber,
      })
    }

    const handlePageSizeChange = (current: number, size: number) => {
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        size,
      })
      store.dispatch('invoiceCenter/fetchInvoiceList', {
        ...invoiceListState.formState,
        page: 1,
        size,
      })
    }

    return {
      moment,
      dayjs,
      columns,
      handlePageChange,
      handlePageSizeChange,
      invoiceListState,
      invoiceDetailState,
      storesState,
      stores,
      find,
      JD_INVOICE_TYPE,
      JD_INVOICE_STATUS,
      JD_INVOICE_UPLOAD_STATE,
      JD_INVOICE_BILLING_STATE,
      JD_INVOICE_USE_TYPES,
      compact,
    }
  }
})
</script>

<style lang="scss" scoped>
.invoice-list-table {
  min-width: 1080px;
  margin: 0 1rem;
  h2 {
    font-size: 16px;
  }

  .pagination {
    margin: 16px 0;
  }

  .nowrap {
    // border: 1px solid #000;
    white-space: nowrap;
  }
}

:deep(.ant-table td) {
  white-space: nowrap;
}
:deep(.ant-table-header) {
  // overflow: inherit!important;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  &-80 {
    width: 80px;
  }
  &-120 {
    width: 120px;
  }
  &-150 {
    width: 150px;
  }
  &-180 {
    width: 180px;
  }
  &-200 {
    width: 200px;
  }
}
</style>