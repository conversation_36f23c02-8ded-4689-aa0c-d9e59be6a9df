export default [
  {
    title: '发票代码/号码',
    dataIndex: 'invoiceNo',
    key: 'invoiceNo',
    width: 160,
    fixed: 'left',
  },
  {
    title: '是否有红字',
    dataIndex: 'hasRedInfo',
    key: 'hasRedInfo',
    width: 120,
  },
  {
    title: '订单号',
    dataIndex: 'orderId',
    key: 'orderId',
    width: 160,
  },
  {
    title: '开票倒计时',
    dataIndex: 'invoiceDeadlineStamp',
    key: 'invoiceDeadlineStamp',
    width: 120,
  },
  {
    title: '开票日期',
    dataIndex: 'invoiceTimeStamp',
    key: 'invoiceTimeStamp',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    width: 240,
  },
  {
    title: '发票状态',
    dataIndex: 'invoiceStatus',
    key: 'invoiceStatus',
    width: 120,
  },
  {
    title: '上传状态',
    dataIndex: 'uploadState',
    key: 'uploadState',
    width: 120,
  },
  {
    title: '抵扣状态/入账状态',
    dataIndex: 'invoiceUse_billingState',
    key: 'invoiceUse_billingState',
    width: 120,
  },
  {
    title: '购方名称',
    dataIndex: 'buyerName',
    key: 'buyerName',
    width: 120,
  },
  {
    title: '购方税号',
    dataIndex: 'buyerTaxNo',
    key: 'buyerTaxNo',
    width: 120,
  },

  {
    title: '价税合计',
    dataIndex: 'amountWithTax',
    key: 'amountWithTax',
    width: 120,
  },
  {
    title: '合计金额',
    dataIndex: 'amountWithoutTax',
    key: 'amountWithoutTax',
    width: 120,
  },
  {
    title: '合计税额',
    dataIndex: 'taxAmount',
    key: 'taxAmount',
    width: 120,
  },

  {
    title: '是否发送邮箱',
    dataIndex: 'isSendMail',
    key: 'isSendMail',
    width: 120,
  },
  {
    title: '联系人邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 120,
  },
  {
    title: '原发票号码',
    dataIndex: 'originInvoiceNo',
    key: 'originInvoiceNo',
    width: 180,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 160,
  },
]