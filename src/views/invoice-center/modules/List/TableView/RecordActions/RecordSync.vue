<template>
  <!-- {{ invoiceDetailState?.syncMergeBlueInvoice?.status }} -->
  <a4-popconfirm
    title="确定要开票吗？"
    ok-text="Yes"
    cancel-text="No"
    @confirm="handleConfirm"
  >
    <a4-button
      size="small"
      style="margin: 0 4px;"
      :disabled="
        invoiceListState?.status === 'PENDING'
        || invoiceDetailState?.cancel?.status === 'PENDING'
        || invoiceDetailState?.syncMergeBlueInvoice?.status === 'PENDING'
        || invoiceDetailState?.redraft?.status === 'PENDING'
      "
      :loading="
        invoiceDetailState?.syncMergeBlueInvoice.status === 'PENDING'
        && record?.id === invoiceDetailState?.syncMergeBlueInvoice?.payload?.id
      "
    >开票</a4-button>
  </a4-popconfirm>
</template>

<script lang="ts">
import { find } from 'lodash'
import { eventBus } from '@/eventBus';
import { Button as A4Button, Popconfirm as A4Popconfirm, message } from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
    A4Popconfirm,
  },
  props: ['id'],
  setup(props) {
    const store = useStore();

    const {
      invoiceListState,
      invoiceDetailState,
    } = store.state.invoiceCenter

    const {
      userState
    } = store.state.global

    const roles = computed(() => {
      return userState?.data?.data?.roles
    })

    const record = computed(() => {
      return find(invoiceListState?.data?.data?.invoiceDetails, { id: props.id })
    })

    const handleConfirm = () => {
      store.dispatch('invoiceCenter/syncMergeBlueInvoice', {
        orderId: record.value?.orderId,
        id: record.value?.id,
      }).then((res) => {
        console.log('res', res)
        if (res instanceof Error) {
          message.error(res?.message || '开票失败')
        } else {
          if (res?.code === '0') {
            message.success('开票成功', 0.9, () => {
              eventBus.emit('invoice-center-search:form-view:submit-form');
            })
          } else {
            message.error(res?.msg || '开票失败', 0.9, () => {
              eventBus.emit('invoice-center-search:form-view:submit-form');
            })
          }
        }
      }).catch(err => {
        console.log('err', err)
        message.error(err?.message || '开票失败')
      });
    }

    return {
      record,
      // hasPermission,
      moment,
      handleConfirm,
      invoiceListState,
      invoiceDetailState,
    }
  }
})
</script>