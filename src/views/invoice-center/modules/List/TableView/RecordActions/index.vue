<template>
  <div class="actions-wrapper">
    <span v-for="action, index in uniq(actions)" key="index">
      <record-sync
        :id="id"
        v-if="
          action === 'RecordSync'
          && record?.issueType === 'BLUE'
          && record?.invoiceStatus === 'PENDING'
        "
      />

      <record-redraft
        :id="id"
        v-if="
          action === 'RecordRedraft'
          && record?.issueType === 'BLUE'
          && ['SUCCESS', 'CANCEL_FAILED'].indexOf(record?.invoiceStatus) > -1
        "
      />

      <record-view
        :id="id"
        v-if="
          action === 'RecordView'
          && record?.invoiceStatus === 'SUCCESS'
          && record?.invoicePdfView
        "
      />

      <record-cancel
        :id="id"
        v-if="
          action === 'RecordCancel'
          && record?.issueType === 'BLUE'
          && record?.invoiceStatus === 'PENDING'
        "
      />
    </span>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, watch, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { uniq, find } from 'lodash'
import RecordSync from './RecordSync.vue'
import RecordRedraft from './RecordRedraft.vue'
import RecordView from './RecordView.vue'
import RecordCancel from './RecordCancel.vue'

export default defineComponent({
  components: {
    RecordSync,
    RecordRedraft,
    RecordView,
    RecordCancel,
    // RecordRefundApproval,
  },
  props: [
    'id'
  ],
  setup (props) {
    const { id } = props    
    const actions = ref([])
    const store = useStore();

    const {
      invoiceListState
    } = store.state.invoiceCenter

    const invoiceDetails = computed(() => {
      return invoiceListState?.data?.data?.invoiceDetails || []
    })

    const record = computed(() => {
      return find(invoiceDetails.value, { id })
    })

    const getActions = (record: any) => {
      const _actions: string[] = []

      _actions.push('RecordSync')
      _actions.push('RecordRedraft')
      _actions.push('RecordView')
      _actions.push('RecordCancel')

      return _actions
    }

    onMounted(() => {
      if (invoiceListState.status === 'FULFILLED') {
        actions.value = uniq(getActions(record.value))
      }
    })

    return {
      uniq,
      actions,
      record,
      invoiceListState,
    }
  }
})
</script>
