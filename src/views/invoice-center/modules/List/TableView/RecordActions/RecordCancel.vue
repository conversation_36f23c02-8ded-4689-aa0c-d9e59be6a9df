<template>
   <!-- === {{ record }} === -->
  <a4-popconfirm
    title="确定要取消吗？"
    ok-text="Yes"
    cancel-text="No"
    @confirm="handleConfirm"
  >
    <a4-button
      size="small"
      style="margin: 0 4px;"
      :disabled="
        invoiceListState?.status === 'PENDING'
        || invoiceDetailState?.cancel?.status === 'PENDING'
        || invoiceDetailState?.redraft?.status === 'PENDING'
        || invoiceDetailState?.syncMergeBlueInvoice?.status === 'PENDING'
      "
      :loading="
        invoiceDetailState?.cancel.status === 'PENDING'
        && record?.id === invoiceDetailState?.cancel?.payload?.id
      "
    >取消</a4-button>
  </a4-popconfirm>
</template>

<script lang="ts">
import { find } from 'lodash'
import { eventBus } from '@/eventBus';
import { Button as A4Button, Popconfirm as A4Popconfirm, message } from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
    A4Popconfirm,
  },
  props: ['id'],
  setup(props) {
    const store = useStore();

    const {
      invoiceListState,
      invoiceDetailState,
    } = store.state.invoiceCenter

    const {
      userState
    } = store.state.global

    const roles = computed(() => {
      return userState?.data?.data?.roles
    })

    const record = computed(() => {
      return find(invoiceListState?.data?.data?.invoiceDetails, { id: props.id })
    })

    // const hasPermission = computed(() => {
    //   return roles.value.includes('virtual-order-promotion-coupon-refund')
    // })


    const handleConfirm = () => {
      store.dispatch('invoiceCenter/cancel', {
        orderId: record.value?.orderId,
        id: record.value?.id,
      }).then((res) => {
        console.log('res', res)
        if (res?.code === '0') {
          message.success('取消成功', 0.9, () => {
            eventBus.emit('invoice-center-search:form-view:submit-form');
          })
        } else {
          message.error(res?.msg || '取消失败', 0.9, () => {
            eventBus.emit('invoice-center-search:form-view:submit-form');
          })
        }
      }).catch(err => {
        console.log('err', err)
        message.error(err?.message || '取消失败')
      });
    }

    return {
      record,
      invoiceListState,
      invoiceDetailState,
      // hasPermission,
      moment,
      handleConfirm,
    }
  }
})
</script>