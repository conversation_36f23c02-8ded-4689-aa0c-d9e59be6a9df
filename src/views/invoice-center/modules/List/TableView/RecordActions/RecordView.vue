<template>
  <a4-button
    size="small"
    style="margin: 0 4px;"
    :disabled="
      invoiceListState?.status === 'PENDING'
      || invoiceDetailState?.cancel?.status === 'PENDING'
      || invoiceDetailState?.redraft?.status === 'PENDING'
      || invoiceDetailState?.syncMergeBlueInvoice?.status === 'PENDING'
    "
    @click="handleClick"
  >查看</a4-button>
</template>

<script lang="ts">
import { find } from 'lodash'
import { eventBus } from '@/eventBus';
import { Button as A4Button, Popconfirm as A4Popconfirm, message } from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
    A4Popconfirm,
  },
  props: ['id'],
  setup(props) {
    const store = useStore();

    const {
      invoiceListState,
      invoiceDetailState,
    } = store.state.invoiceCenter

    const {
      userState
    } = store.state.global

    const roles = computed(() => {
      return userState?.data?.data?.roles
    })

    const record = computed(() => {
      return find( invoiceListState?.data?.data?.invoiceDetails, { id: props.id })
    })

    const handleClick = () => {
      store.commit('invoiceCenter/updateActionState', {
        type: 'OrderInvoiceView',
        record: record.value,
      });
    }

    return {
      record,
      invoiceListState,
      invoiceDetailState,
      // hasPermission,
      moment,
      handleClick,
    }
  }
})
</script>