<template>
  <div
    class="list-wrapper"
  >
    <div class="main-title-wrapper">
      <h1 class="main-title">
        <slot name="mainTitle"></slot>
      </h1>
      <div class="main-title-extra">
        <slot name="mainTitleExtras"></slot>
      </div>
    </div>
    <div class="main-content">
      <slot name="mainContent"></slot>
    </div>

    <div class="bfc">
      <slot name="bfc"></slot>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'
export default defineComponent({
  setup() {
    return {
    }
  },
})
</script>

<style scoped lang="scss">
.list-wrapper {
  // overflow-y: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.main-title-wrapper {
  display: flex;
  min-width: 400px;
  padding: .5rem 0;
  margin: 0 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  align-items: center;
  flex: 0 0 auto;
}
.main-title {
  padding: 0;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  flex: 1 1 auto;
  line-height: 2rem;
}
.main-title-extras {
  font-size: 32px;
}
.main-content {
  margin: 8px 0;
  flex: 1 1 auto;
  overflow-y: auto;
}
.bfc {
  position: absolute;
  top: 0;
  left: 0;
  // border: 1px solid #000;
  // width: 100%;
  // height: 100%;
}
</style>