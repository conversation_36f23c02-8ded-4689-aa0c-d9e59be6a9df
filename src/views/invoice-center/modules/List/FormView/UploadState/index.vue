<template>
  <div class="form-item">
    <h3>上传状态</h3>
    <a4-form-item
      ref="uploadState"
      name="uploadState"
      :hasFeedback="false"
    >
      <a4-select
        allow-clear
        size="small"
        v-model:value="uploadState"
        placeholder="不限"
        :options="options"
        @change="handleChange"
        :disabled="
          invoiceListState.status === 'PENDING'
        "
      ></a4-select>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { useStore } from 'vuex'
import {
  Select as A4Select,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';
export default defineComponent({
  components: {
    A4Select,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { invoiceListState } = store.state.invoiceCenter;

    const uploadState = computed(() => {
      return invoiceListState?.formState?.uploadState
    })

    const options = ref([
      { value: '', label: '不限' },
      { value: '0', label: '待上传' },
      { value: '1', label: '上传中' },
      { value: '2', label: '上传成功' },
      { value: '3', label: '上传失败' },
    ]);

    const handleChange = (v: any) => {
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        uploadState: v,
      });
    }

    return {
      options,
      invoiceListState,
      uploadState,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>