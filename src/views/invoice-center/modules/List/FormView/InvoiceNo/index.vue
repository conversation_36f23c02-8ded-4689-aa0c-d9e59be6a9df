<template>
  <div class="form-item">
    <h3>发票号</h3>
    <a4-form-item
      ref="invoiceNo"
      name="invoiceNo"
      :hasFeedback="false"
    >
      <a4-input
        allowClear
        size="small"
        autocorrect="off"
        placeholder="发票号"
        v-model:value="invoiceNo"
        :disabled="invoiceListState.status === 'PENDING'"
        @change="handleChange"
      />
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from 'vue';
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import {
  Input as A4Input,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
export default defineComponent({
  components: {
    A4Input,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const route = useRoute();
    const store = useStore();
    const { invoiceListState } = store.state.invoiceCenter;

    const invoiceNo = computed(() => {
      return invoiceListState?.formState?.invoiceNo?.trim()
    })

    const handleChange = (e: any) => {
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        invoiceNo: e?.target?.value ? e?.target?.value?.trim() : undefined,
      })
    }

    return {
      invoiceListState,
      invoiceNo,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>