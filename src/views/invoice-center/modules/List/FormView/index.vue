<template>
  <!-- {{ invoiceListState?.formState }} -->
  <div class="form-wrapper">
    <a4-form
      ref="formRef"
      :model="invoiceListState?.formState"
      :rules="rules"
      autocomplete="off"
      class="form"
    >
      <a4-row :gutter="16">
        <a4-col :span="4">
          <order-id />
        </a4-col>
        <a4-col :span="4">
          <invoice-no />
        </a4-col>
        <a4-col :span="4">
          <buyer-name />
        </a4-col>
        <a4-col :span="4">
          <invoice-date />
        </a4-col>
        <a4-col :span="4">
          <invoice-type />
        </a4-col>
        <a4-col :span="4">
        </a4-col>
        <a4-col :span="4">
          <invoice-status />
        </a4-col>
        <a4-col :span="4">
          <upload-state />
        </a4-col>
        <a4-col :span="4">
          <invoice-use />
        </a4-col>
        <a4-col :span="4">
          <billing-state />
        </a4-col>
        <a4-col :span="4">
          <direct-invoice />
        </a4-col>
      </a4-row>
    </a4-form>
  </div>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
// import moment, { Moment } from 'moment';
import { defineComponent, ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Input as A4Input,
  Form as A4Form,
  FormItem as A4FormItem,
  Row as A4Row,
  Col as A4Col,
} from 'ant-design-vue-4'

import OrderId from './OrderId/index.vue'
import InvoiceNo from './InvoiceNo/index.vue'
import BuyerName from './BuyerName/index.vue'
import InvoiceDate from './InvoiceDate/index.vue'
import InvoiceType from './InvoiceType/index.vue'
import InvoiceStatus from './InvoiceStatus/index.vue'
import UploadState from './UploadState/index.vue'
import BillingState from './BillingState/index.vue'
import InvoiceUse from './InvoiceUse/index.vue'
import DirectInvoice from './DirectInvoice/index.vue'

import { Query_Invoice_List_Req } from '@/types/invoice-center';

export default defineComponent({
  components: {
    A4Input,
    A4Form,
    A4FormItem,
    A4Row,
    A4Col,
    OrderId,
    InvoiceNo,
    BuyerName,
    InvoiceDate,
    InvoiceType,
    InvoiceStatus,
    UploadState,
    BillingState,
    InvoiceUse,
    DirectInvoice,
  },
  setup() {
    const store = useStore();
    const { invoiceListState } = store.state.invoiceCenter;

    const formRef = ref();

    const rules = {
    }

    const orderId = computed(() => {
      return invoiceListState?.formState?.orderId?.trim()
    })
    
    const invoiceNo = computed(() => {
      return invoiceListState?.formState?.invoiceNo?.trim()
    })

    const buyerName = computed(() => {
      return invoiceListState?.formState?.buyerName?.trim()
    })

    const fromInvoiceTimeStamp = computed(() => {
      return invoiceListState?.formState?.fromInvoiceTimeStamp
    })

    const toInvoiceTimeStamp = computed(() => {
      return invoiceListState?.formState?.toInvoiceTimeStamp
    })

    const invoiceType = computed(() => {
      return invoiceListState?.formState?.invoiceType
    })

    const invoiceStatus = computed(() => {
      return invoiceListState?.formState?.invoiceStatus
    })

    const uploadState = computed(() => {
      return invoiceListState?.formState?.uploadState
    })

    const invoiceUse = computed(() => {
      return invoiceListState?.formState?.invoiceUse
    })

    const billingState = computed(() => {
      return invoiceListState?.formState?.billingState
    })

    const directInvoice = computed(() => {
      return invoiceListState?.formState?.directInvoice
    })

    const page = computed(() => invoiceListState?.formState?.page)
    const size = computed(() => invoiceListState?.formState?.size)
    const baseSize = computed(() => invoiceListState?.formState?.baseSize)

    const handleSubmitForm = async () => {
      if (!formRef.value) return
      await formRef.value
        .validate()
        .then(() => {
          const payload: Query_Invoice_List_Req = {
            orderId: orderId.value,
            invoiceNo: invoiceNo.value,
            buyerName: buyerName.value,
            fromInvoiceTimeStamp: fromInvoiceTimeStamp.value,
            toInvoiceTimeStamp: toInvoiceTimeStamp.value,
            invoiceType: invoiceType.value,
            invoiceStatus: invoiceStatus.value,
            uploadState: uploadState.value,
            invoiceUse: invoiceUse.value,
            billingState: billingState.value,
            directInvoice: directInvoice.value,

            page: page.value,
            size: size.value,
          }
          // debugger
          store.dispatch('invoiceCenter/fetchInvoiceList', payload)
        })
        .catch((error) => {
          console.log('error', error);
          return false
        });
    }

    const handleResetForm = () => {
      if (!formRef.value) return
      formRef.value.clearValidate()
      formRef.value.resetFields()
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        page: 1,
        size: baseSize.value,
      })
      handleSubmitForm()
    }

    eventBus.on('invoice-center-search:form-view:submit-form', handleSubmitForm);
    eventBus.on('invoice-center-search:form-view:reset-form', handleResetForm);

    // watch(() => orderListState.status, (newStatus) => {
    //   if (newStatus === 'RESUBMIT') {
    //     handleSubmitForm()
    //   }
    // })

    return {
      rules,
      formRef,
      invoiceListState,
      handleSubmitForm,
      handleResetForm,
    }
  },
})
</script>


<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-wrapper {
  border-bottom: 1px solid #efefef;
  margin: 0 1rem 0rem 1rem;
  padding-bottom: 1rem;

  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .form {
    min-width: 1280px;
    flex: 1 1 auto;
  }
  .form-items-wrapper {
    display: flex;
    // border: 1px solid #000;
    .form-item {
      margin-right: 16px;
      margin-bottom: .5rem;
      width: 20%;
      min-width: 220px;
      max-width: 220px;
      align-items: baseline;
      flex-direction: column;

      :deep(.ant-form-item) {
        margin: 0;
      }
    }
  }
  :deep(.ant-form-item) {
    margin-bottom: .2rem;
  }
  .form-actions-wrapper {
    align-self: center;
    flex: 0 0 auto;
    margin-left: 4rem;
  }
}
</style>