<template>
  <div class="form-item">
    <h3>是否直连</h3>
    <a4-form-item
      ref="directInvoice"
      name="directInvoice"
      :hasFeedback="false"
    >
      <a4-select
        allow-clear
        size="small"
        v-model:value="directInvoice"
        placeholder="不限"
        :options="options"
        @change="handleChange"
        :disabled="
          invoiceListState.status === 'PENDING'
        "
      ></a4-select>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { useStore } from 'vuex'
import {
  Select as A4Select,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';
export default defineComponent({
  components: {
    A4Select,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { invoiceListState } = store.state.invoiceCenter;

    const directInvoice = computed(() => {
      return invoiceListState?.formState?.directInvoice
    })

    const options = ref([
      { value: '', label: '不限' },
      { value: true, label: '是' },
      { value: false, label: '否' },
    ]);

    const handleChange = (v: any) => {
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        directInvoice: v,
      });
    }

    return {
      options,
      invoiceListState,
      directInvoice,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>