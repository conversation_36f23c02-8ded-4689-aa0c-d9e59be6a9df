<template>
  <div class="form-item">
    <h3>抵扣状态</h3>
    <a4-form-item
      ref="invoiceUse"
      name="invoiceUse"
      :hasFeedback="false"
    >
      <a4-select
        allow-clear
        size="small"
        v-model:value="invoiceUse"
        placeholder="不限"
        :options="options"
        @change="handleChange"
        :disabled="
          invoiceListState.status === 'PENDING'
        "
      ></a4-select>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { useStore } from 'vuex'
import {
  Select as A4Select,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';
export default defineComponent({
  components: {
    A4Select,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { invoiceListState } = store.state.invoiceCenter;

    const invoiceUse = computed(() => {
      return invoiceListState?.formState?.invoiceUse
    })

    const options = ref([
      { value: '', label: '不限' },
      { value: '0', label: '未使用' },
      { value: '1', label: '已申请抵扣' },
      { value: '2', label: '已申请退税' },
      { value: '3', label: '已申请代办退税' },
      { value: '4', label: '已勾选不抵扣（历史数据）' },
      { value: '5', label: '已申请代办退税（历史数据）' },
      { value: '6', label: '已申请不抵扣' },
      { value: '7', label: '内销转出口' },
      { value: '8', label: '出口转内销' },
      { value: '9', label: '准予退税' },
      { value: '10', label: '不予退税' },
      { value: '11', label: '冬奥退税' },
    ]);

    const handleChange = (v: any) => {
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        invoiceUse: v,
      });
    }

    return {
      options,
      invoiceListState,
      invoiceUse,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>