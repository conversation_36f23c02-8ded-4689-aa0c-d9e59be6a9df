<template>
  <div class="form-item">
    <h3>发票状态</h3>
    <a4-form-item
      ref="invoiceStatus"
      name="invoiceStatus"
      :hasFeedback="false"
    >
      <a4-select
        allow-clear
        size="small"
        v-model:value="invoiceStatus"
        placeholder="不限"
        :options="options"
        @change="handleChange"
        :disabled="
          invoiceListState.status === 'PENDING'
        "
      ></a4-select>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { useStore } from 'vuex'
import {
  Select as A4Select,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';
export default defineComponent({
  components: {
    A4Select,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { invoiceListState } = store.state.invoiceCenter;

    const invoiceStatus = computed(() => {
      return invoiceListState?.formState?.invoiceStatus
    })

    const options = ref([
      { value: '', label: '不限' },
      { value: 'PENDING', label: '待开票' },
      { value: 'IN_PROGRESS', label: '开票中' },
      { value: 'SUCCESS', label: '开票成功' },
      { value: 'FAIL', label: '开票失败' },
      { value: 'CANCELLING', label: '红冲中' },
      { value: 'CANCEL_FAILED', label: '红冲失败' },
      { value: 'INVALID', label: '已红冲' },
      { value: 'MANUAL_CANCELLED', label: '手动取消' }
    ]);

    const handleChange = (v: any) => {
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        invoiceStatus: v,
      });
    }

    return {
      options,
      invoiceListState,
      invoiceStatus,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>