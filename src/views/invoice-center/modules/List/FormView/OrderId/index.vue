<template>
  <div class="form-item">
    <h3>订单号</h3>
    <a4-form-item
      ref="orderId"
      name="orderId"
      :hasFeedback="false"
    >
      <a4-input
        allowClear
        size="small"
        autocorrect="off"
        placeholder="订单号"
        v-model:value="orderId"
        :disabled="invoiceListState.status === 'PENDING'"
        @change="handleChange"
      />
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from 'vue';
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import {
  Input as A4Input,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
export default defineComponent({
  components: {
    A4Input,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const route = useRoute();
    const store = useStore();
    const { invoiceListState } = store.state.invoiceCenter;

    const orderId = computed(() => {
      return invoiceListState?.formState?.orderId?.trim()
    })

    const handleChange = (e: any) => {
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        orderId: e?.target?.value ? e?.target?.value?.trim() : undefined,
      })
    }

    return {
      invoiceListState,
      orderId,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>