<template>
  <div class="form-item">
    <h3>开票时间</h3>
    <a4-form-item
      ref="invoiceDate"
      name="invoiceDate"
    >
      <a4-range-picker
        size="small"
        v-model:value="timeRange"
        format="YYYY-MM-DD"
        @change="handleChange"
        :disabled="
          invoiceListState.status === 'PENDING'
        "
      >
        <template #renderExtraFooter></template>
      </a4-range-picker>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { compact } from 'lodash'
import dayjs from 'dayjs';
import { defineComponent, computed, ref } from 'vue';
import { useStore } from 'vuex'
import {
  FormItem as A4FormItem,
  RangePicker as A4RangePicker,
} from 'ant-design-vue-4'
export default defineComponent({
  components: {
    A4FormItem,
    A4RangePicker,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { invoiceListState } = store.state.invoiceCenter;

    const timeRange = computed(() => {
      return compact([
        invoiceListState?.formState?.fromInvoiceTimeStamp
          ? dayjs(invoiceListState?.formState?.fromInvoiceTimeStamp) : undefined,
        invoiceListState?.formState?.toInvoiceTimeStamp
          ? dayjs(invoiceListState?.formState?.toInvoiceTimeStamp) : undefined,
      ])
    })

    const handleChange = (range: any) => {
      const [ from, end ] = range || []
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        fromInvoiceTimeStamp: from?.startOf('day').valueOf(),
        toInvoiceTimeStamp: end?.endOf('day').valueOf(),
        page: 1,
      });
    }

    return {
      invoiceListState,
      timeRange,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
</style>