<template>
  <a4-row
    :gutter="[20, 0]"
    style="align-items: center;"
    v-if="roles.indexOf(role) > -1"
  >
    <a4-button
      size="small"
      type="link"
      style="padding: 0 16px;"
      @click.prevent.stop="handleResetForm"
    >
      <RestOutlined style="font-size: 18px;transform: translateY(2px);" />重置
    </a4-button>

    <a4-button
      size="small"
      type="primary"
      style="margin-left: 4px;"
      @click.prevent.stop="handleSubmitForm"
      :loading="invoiceListState.status === 'PENDING'"
    >查询</a4-button>
  </a4-row>
</template>

<script lang="ts">
import {
  PieChartOutlined,
  RestOutlined,
} from '@ant-design/icons-vue';
import { eventBus } from '@/eventBus';
import { defineComponent, ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Button as A4Button,
  message
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4<PERSON><PERSON>on,
    A4Row,
    RestOutlined,
  },
  setup(props) {
    const role = ref('oms-invoice')
    const store = useStore()

    const {
      globalState,
      userState,
    } = store.state.global;

    const {
      invoiceListState,
    } = store.state.invoiceCenter;

    const roles = computed(() => userState?.data?.data?.roles ?? [])

    const handleSubmitForm = () => {
      invoiceListState.selectedRowKeys = []
      store.commit('invoiceCenter/updateInvoiceListFormState', {
        page: 1,
      })
      eventBus.emit('invoice-center-search:form-view:submit-form')
    }

    const handleResetForm = () => {
      invoiceListState.selectedRowKeys = []
      eventBus.emit('invoice-center-search:form-view:reset-form')
    }

    return {
      role,
      roles,
      invoiceListState,
      handleSubmitForm,
      handleResetForm,
    }
  }
})
</script>