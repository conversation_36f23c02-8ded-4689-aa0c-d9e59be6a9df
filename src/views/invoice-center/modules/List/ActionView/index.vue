<template>
  <div class="action-wrapper">
    <div class="action-wrapper-l">

    </div>
    <div class="action-wrapper-r">
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { Button as A4Button, message } from 'ant-design-vue-4'
import {
  PlusSquareTwoTone,
  PlusCircleTwoTone,
} from '@ant-design/icons-vue';
import { downloadBlob } from '@/utils'

export default defineComponent({
  components: {
    PlusSquareTwoTone,
    PlusCircleTwoTone,
    A4Button,
  },
  setup() {
    const store = useStore();

    const {
      userState,
    } = store.state.global

    const roles = computed(() => userState?.data?.data?.roles ?? [])

    return {
      userState,
      roles,
    }
  },
})
</script>

<style scoped lang="scss">
.action-wrapper {
  margin: .5rem 1rem;
  display: flex;
  &-l {
    flex: 1 1 auto;
    .action-button {
      margin-right: .5rem;
    }
  }
  &-r {
    flex: 0 0 auto;
    .action-button {
      margin-left: .5rem;
    }
  }
}
</style>