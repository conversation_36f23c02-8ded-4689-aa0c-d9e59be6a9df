<template>
  <ManagementLayout>
    <template #systemTitle>
      {{system.title}}
    </template>

    <template #systemMenus>
      <sider-menu
        :modules="modules"
        :selected-keys="selectedKeys"
        :open-keys="openKeys"
      />
    </template>

    <template #center>
      <layout>
        <template #list>
          <order-list v-if="userState.status === 'FULFILLED'">
            <template #mainTitle>
              {{module.title}}
            </template>
            <template #mainTitleExtras>
              <form-extras />
            </template>
            <template #mainContent>
              <template v-if="roles.indexOf(role) > -1">
                <form-view></form-view>
                <action-view></action-view>
                <table-view></table-view>
              </template>
              <template v-else>
                <a4-result
                  status="403"
                  title="403"
                  sub-title="抱歉，没有访问权限！"
                  v-if="userState.status === 'FULFILLED'"
                />
              </template>
            </template>
            <template #bfc>
              <invoice-drawer />
            </template>
          </order-list>
        </template>
      </layout>
    </template>
  </ManagementLayout>

  <template v-if="$route.meta.survey">
    <Survey />
  </template>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { defineComponent, onBeforeMount, onMounted, ref, watch, computed } from 'vue'
import {
  Button as A4Button,
  Row as A4Row,
  Col as A4Col,
  Result as A4Result,
} from 'ant-design-vue-4'
import ManagementLayout from '@/components/ManagementLayout/index.vue'
import SiderMenu from '@/components/SiderMenu/index.vue'
import InvoiceDrawer from '@/components/InvoiceDrawer/index.vue'
import Layout from './Layout.vue'
import OrderList from './modules/List/index.vue'
import FormExtras from './modules/List/FormExtras/index.vue'
import FormView from './modules/List/FormView/index.vue'
import TableView from './modules/List/TableView/index.vue'
import ActionView from './modules/List/ActionView/index.vue'
// import RefundDialog from '@/components/RefundDialog/virtual/index.vue'
import Survey from '@/components/Survey/index.vue'
import { useStore } from 'vuex'

export default defineComponent({
  components: {
    ManagementLayout,
    SiderMenu,
    InvoiceDrawer,
    Layout,
    OrderList,
    FormExtras,
    FormView,
    ActionView,
    TableView,
    // RefundDialog,
    Survey,
    A4Button,
    A4Row,
    A4Col,
    A4Result,
  },
  setup() {
    const hasLayout = ref(true)
    const store = useStore()
    const role = ref('oms-invoice')

    const {
      globalState,
      userState,
    } = store.state.global;

    const {
      actionState,
    } = store.state.virtualOrders;

    const roles = computed(() => userState?.data?.data?.roles ?? [])

    onMounted(async () => {
      store.dispatch('global/getCurrentUser')
        .then(async (_userState) => {
          await store.dispatch('global/fetchAllStores')
          roles.value.indexOf(role.value) > -1 && 
          eventBus.emit('invoice-center-search:form-view:reset-form')
        })
    })

    const openKeys = computed(() => {
      return []
    })

    return {
      role,
      roles,
      hasLayout,
      selectedKeys: ref(['oms-invoice']),
      openKeys,
      system: globalState.invoiceCenter.system,
      module: {
        key: 'oms-invoice',
        title: '发票查询管理',
      },
      modules: globalState.invoiceCenter.modules,
      userState,
      actionState,
    }
  },
})
</script>