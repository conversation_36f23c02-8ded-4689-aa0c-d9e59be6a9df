<template>
  <a4-button
    size="small"
    type="link"
    style="padding: 0 4px;"
    @click.prevent.stop="handleOpen(true)"
  >查看小票号</a4-button>

  <a4-modal
    :open="open"
    @cancel="handleOpen(false)"
    title="提示"
  >
    <template #footer>
      <a4-button @click="handleCopy(record?.receiptBarcode)">复制</a4-button>
      <a4-button @click="handleOpen(false)">取消</a4-button>
    </template>

    <div style="padding: 8px 0;">
      小票号：{{ record?.receiptBarcode }}
    </div>
  </a4-modal>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import { useStore } from 'vuex'
import {
  Button as A4Button,
  Modal as A4Modal,
  message,
} from 'ant-design-vue-4'
import useClipboard from 'vue-clipboard3'

export default defineComponent({
  components: {
    A4Button,
    A4Modal,
  },
  props: {
    record: {
      type: Object,
    }
  },
  setup(props) {
    const store = useStore();
    const open = ref(false)
    const { toClipboard } = useClipboard()

    const {
      actionState
    } = store.state.csc

    const computedRecord = computed(() => props.record)

    const handleOpen = (_open) => {
      open.value = _open
    }

    const handleCopy = (text) => {
      toClipboard(text) 
        .then(() => {
          message.success('复制成功')
        })
        .catch(err => {
          console.error(err)
          message.error('复制失败')
        })
    }

    return {
      open,
      actionState,
      computedRecord,
      handleOpen,
      handleCopy,
    }
  }
})
</script>