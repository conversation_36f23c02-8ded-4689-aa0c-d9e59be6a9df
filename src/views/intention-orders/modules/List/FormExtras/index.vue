<template>
  <a4-row
    :gutter="[20, 0]"
    style="align-items: center;"
    v-if="roles.indexOf(role) > -1"
  >
    <a4-button
      size="small"
      type="link"
      style="padding: 0 16px;"
      @click.prevent.stop="handleResetForm"
    >
      <RestOutlined style="font-size: 18px;transform: translateY(2px);" />重置
    </a4-button>

    <a4-button
      size="small"
      type="primary"
      style="margin-left: 4px;"
      @click.prevent.stop="handleSubmitForm"
      :loading="orderListState.status === 'PENDING'"
    >查询</a4-button>
  </a4-row>
</template>

<script lang="ts">
import {
  PieChartOutlined,
  RestOutlined,
} from '@ant-design/icons-vue';
import { eventBus } from '@/eventBus';
import { defineComponent, ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Button as A4Button,
  Row as A4Row,
  message
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4<PERSON>utton,
    A4Row,
    RestOutlined,
  },
  setup(props) {
    const role = ref('intention-orders')
    const store = useStore()

    const {
      globalState,
      userState,
    } = store.state.global;

    const {
      orderListState,
    } = store.state.intentionOrders;

    const roles = computed(() => userState?.data?.data?.roles ?? [])

    const handleSubmitForm = () => {
      orderListState.selectedRowKeys = []
      eventBus.emit('intention-orders-search:form-view:submit-form')
    }

    const handleResetForm = () => {
      orderListState.selectedRowKeys = []
      eventBus.emit('intention-orders-search:form-view:reset-form')
    }

    return {
      role,
      roles,
      orderListState,
      handleSubmitForm,
      handleResetForm,
    }
  }
})
</script>