<template>
  <div class="form-item">
    <h3>收货人</h3>
    <a4-form-item
      ref="customerName"
      name="customerName"
      :hasFeedback="false"
    >
      <a4-input
        size="small"
        autocorrect="off"
        placeholder="收货人"
        v-model:value="customerName"
        :disabled="orderListState.status === 'PENDING'"
        @change="handleChange"
        allowClear
      />
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from 'vue';
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import {
  Input as A4Input,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
export default defineComponent({
  components: {
    A4Input,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const route = useRoute();
    const store = useStore();
    const { orderListState } = store.state.intentionOrders

    const customerName = computed(() => {
      return orderListState?.formState?.customerName?.trim()
    })

    const handleChange = (e: any) => {
      store.commit('intentionOrders/updateOrderListFormState', {
        customerName: e?.target?.value?.trim()
      })
    }

    return {
      orderListState,
      customerName,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>