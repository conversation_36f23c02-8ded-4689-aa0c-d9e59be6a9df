<template>
  <div class="form-item">
    <h3>会员号</h3>
    <a4-form-item
      ref="familyNo"
      name="familyNo"
      :hasFeedback="false"
    >
      <a4-input
        size="small"
        autocorrect="off"
        placeholder="会员号"
        v-model:value="familyNo"
        :disabled="orderListState.status === 'PENDING'"
        @change="handleChange"
        allowClear
      />
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from 'vue';
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import {
  Input as A4Input,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
export default defineComponent({
  components: {
    A4Input,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const route = useRoute();
    const store = useStore();
    const { orderListState } = store.state.intentionOrders

    const familyNo = computed(() => {
      return orderListState?.formState?.familyNo?.trim()
    })

    const handleChange = (e: any) => {
      store.commit('intentionOrders/updateOrderListFormState', {
        familyNo: e?.target?.value?.trim()
      })
    }

    return {
      orderListState,
      familyNo,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>