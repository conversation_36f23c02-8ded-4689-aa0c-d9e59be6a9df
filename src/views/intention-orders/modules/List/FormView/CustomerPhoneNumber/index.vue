<template>
  <div class="form-item">
    <h3>收货人手机号</h3>
    <a4-form-item
      ref="customerPhoneNumber"
      name="customerPhoneNumber"
      :hasFeedback="false"
      :help="false"
    >
      <a4-input
        size="small"
        autocorrect="off"
        placeholder="11位手机号"
        maxLength="11"
        v-model:value="customerPhoneNumber"
        :disabled="
          orderListState.status === 'PENDING'
        "
        @change="handleChange"
        allowClear
      />
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { useStore } from 'vuex'
import {
  Input as A4Input,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';
export default defineComponent({
  components: {
    A4Input,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { orderListState } = store.state.intentionOrders

    const customerPhoneNumber = computed(() => {
      return orderListState.formState.customerPhoneNumber
    })

    const handleChange = (e: any) => {
      // console.log(e)
      store.commit('intentionOrders/updateOrderListFormState', {
        customerPhoneNumber: e?.target?.value?.trim(),
      });
    }

    return {
      orderListState,
      customerPhoneNumber,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>