<template>
  <div class="form-item">
    <h3>创建时间</h3>
    <a4-form-item
      ref="createTimeRange"
      name="createTimeRange"
    >
      <a4-range-picker
        allowClear
        size="small"
        v-model:value="timeRange"
        format="YYYY-MM-DD"
        @change="handleChange"
        :disabled="
          orderListState.status === 'PENDING'
        "
      >
        <template #renderExtraFooter></template>
      </a4-range-picker>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { compact } from 'lodash'
import dayjs from 'dayjs';
import { defineComponent, computed, ref } from 'vue';
import { useStore } from 'vuex'
import {
  FormItem as A4FormItem,
  RangePicker as A4RangePicker,
} from 'ant-design-vue-4'
export default defineComponent({
  components: {
    A4FormItem,
    A4RangePicker,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { orderListState } = store.state.intentionOrders;

    const timeRange = computed(() => {
      return compact([
        orderListState?.formState?.startTimeStamp
          ? dayjs(orderListState?.formState?.startTimeStamp) : undefined,
        orderListState?.formState?.endTimeStamp
          ? dayjs(orderListState?.formState?.endTimeStamp) : undefined,
      ])
    })

    const handleChange = (range: any) => {
      const [ start, end ] = range || []
      store.commit('intentionOrders/updateOrderListFormState', {
        startTimeStamp: start?.startOf('day').valueOf(),
        endTimeStamp: end?.endOf('day').valueOf(),
        page: 1,
      });
    }

    return {
      orderListState,
      timeRange,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>