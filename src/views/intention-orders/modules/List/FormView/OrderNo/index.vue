<template>
  <div class="form-item">
    <h3>订单号</h3>
    <a4-form-item
      ref="orderNo"
      name="orderNo"
      :hasFeedback="false"
    >
      <a4-input
        size="small"
        autocorrect="off"
        placeholder="订单号"
        v-model:value="orderNo"
        :disabled="orderListState.status === 'PENDING'"
        @change="handleChange"
        allowClear
      />
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from 'vue';
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import {
  Input as A4Input,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
export default defineComponent({
  components: {
    A4Input,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const route = useRoute();
    const store = useStore();
    const { orderListState } = store.state.intentionOrders

    const orderNo = computed(() => {
      return orderListState?.formState?.vid?.trim()
    })

    const handleChange = (e: any) => {
      store.commit('intentionOrders/updateOrderListFormState', {
        orderNo: e?.target?.value?.trim()
      })
    }

    return {
      orderListState,
      orderNo,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>