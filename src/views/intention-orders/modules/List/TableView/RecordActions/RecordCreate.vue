<template>
  <template v-if="['INIT', 'FAIL'].includes(orderStatus)">
    <a4-popconfirm
      v-if="['CLICK_COLLECT_STORE'].includes(record?.serviceType)"
      title="确定要创建订单吗?"
      ok-text="Yes"
      cancel-text="No"
      @confirm="handleDistribution"
      @cancel="cancel"
      :disabled="
        orderDetailState?.distribution.status === 'PENDING'
        || orderDetailState?.status === 'PENDING'
      "
    >
      <a4-button
        size="small"
        style="padding: 0 4px; margin: 0 4px;"
        :disabled="
          orderDetailState?.distribution.status === 'PENDING'
        "
        :loading="
          orderDetailState?.distribution.status === 'PENDING'
          && orderDetailState?.distribution?.params?.orderNo === record?.orderNo
        "
      >创建订单</a4-button>
    </a4-popconfirm>
    <template v-else-if="['HOME_DELIVERY'].includes(record?.serviceType)">
      <a4-button
        size="small"
        style="padding: 0 4px; margin: 0 4px;"
        :disabled="
          orderDetailState?.distribution.status === 'PENDING'
        "
        :loading="
          orderDetailState?.distribution.status === 'PENDING'
          && orderDetailState?.distribution?.params?.orderNo === record?.orderNo
        "
        @click="handleDistribution"
      >创建订单</a4-button>
    </template>
  </template>
</template>

<script type="ts">
import { find } from 'lodash'
import { eventBus } from '@/eventBus';
import { Button as A4Button, Popconfirm as A4Popconfirm, message } from 'ant-design-vue-4';
import { createVNode, defineComponent, toRaw, computed } from 'vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  components: {
    A4Button,
    A4Popconfirm,
  },
  props: ['orderStatus', 'record', 'orderNo'],
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      orderDetailState,
      orderListState,
    } = store.state.intentionOrders

    const orderListData = computed(() => {
      return orderListState?.data?.data?.data || []
    })

    const currentRecord = computed(() => {
      return find(orderListData.value, { orderNo: props.orderNo })
    })

    // const { orderSource } = props.record

    const handleDistribution = () => {
      const {
        roles
      } = userState?.data?.data ?? {}

      if (['CLICK_COLLECT_STORE'].includes(currentRecord.value?.serviceType)) {
        store.dispatch('intentionOrders/distribution', {
          orderNo: currentRecord.value?.orderNo,
        }).then((res) => {
          // 
          if (res.code === '0') {
            message.success('创建成功！', 0.9, () => {
              eventBus.emit('intention-orders-search:form-view:submit-form');
            })
          } else {
            message.error(res.message || '系统异常，创建失败！', 0.9, () => {
              eventBus.emit('intention-orders-search:form-view:submit-form');
            })
          }
        }).catch((err) => {
          // message.error(err?.message)
          // debugger
        })
        // 
      } else if (['HOME_DELIVERY'].includes(currentRecord.value?.serviceType)) {
        store.commit('intentionOrders/updateActionState', {
          type: 'DeliveryConfirm',
          record: currentRecord.value,
        });
      }
    }

    return {
      orderListState,
      handleDistribution,
      // actionState,
      orderDetailState,
      currentRecord,
    }
  }
})
</script>
