<template>
  <a4-button
    size="small"
    style="padding: 0 4px; margin: 0 4px;"
    @click.prevent.stop="handleClick"
    type="link"
  >查看</a4-button>
</template>

<script type="ts">
import { find } from 'lodash'
import { eventBus } from '@/eventBus';
import { Button as A4Button, message } from 'ant-design-vue-4';
import { createVNode, defineComponent, toRaw, computed } from 'vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
  },
  props: {
    record: {
      type: Object,
    },
    orderNo: {
      type: String,
    }
  },
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      orderDetailState,
      orderListState,
    } = store.state.intentionOrders

    const orderListData = computed(() => {
      return orderListState?.data?.data?.data || []
    })

    const currentRecord = computed(() => {
      return find(orderListData.value, { orderNo: props.orderNo })
    })

    // const { orderSource } = props.record

    const handleClick = () => {
      const {
        roles
      } = userState?.data?.data ?? {}
      
      store.commit('intentionOrders/updateActionState', {
        type: 'OrderIntentionView',
        record: currentRecord.value,
      })
    }

    return {
      moment,
      handleClick,
      // actionState,
      orderDetailState,
    }
  }
})
</script>
