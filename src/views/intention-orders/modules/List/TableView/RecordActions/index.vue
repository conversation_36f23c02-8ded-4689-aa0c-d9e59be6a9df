<template>
  <div class="actions-wrapper">
    <span v-for="action, index in uniq(actions)" key="index">
      <record-view
        :orderNo="record?.orderNo"
        :orderStatus="orderStatus"
        :record="record"
        v-if="action === 'RecordView'"
      />

      <record-create
        :orderNo="record?.orderNo"
        :orderStatus="orderStatus"
        :record="record"
        v-if="action === 'RecordCreate'"
      />
      
      <record-cancel
        :orderNo="record?.orderNo"
        :orderStatus="orderStatus"
        :record="record"
        v-if="action === 'RecordCancel'"
      />
    </span>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, watch, ref } from 'vue'
import { useStore } from 'vuex'
import { uniq, find } from 'lodash'

import RecordCreate from './RecordCreate.vue'
import RecordView from './RecordView.vue'
import RecordCancel from './RecordCancel.vue'
// import RecordAudit from './RecordAudit.vue'
// import RecordComplete from './RecordComplete.vue'

export default defineComponent({
  components: {
    RecordCreate,
    RecordView,
    RecordCancel,
    // RecordAudit,
    // RecordComplete,
  },
  props: ['record', 'orderStatus'],
  setup (props) {
    const { record } = props
    // const record = ref(null)
    const actions = ref([])
    const store = useStore();
    const {
      orderListState
    } = store.state.intentionOrders;

    const getActions = (record: any) => {
      const _actions: string[] = []

      // _actions.push('RecordRefund')

      _actions.push('RecordView')

      // _actions.push('RecordCreate')
      // _actions.push('RecordComplete')
      
      // if (record?.canBeCancelled) {
      //   _actions.push('RecordCancel')
      // } else {
      //   _actions.push('RecordCreate')
      // }
      _actions.push('RecordCancel')
      _actions.push('RecordCreate')

      return _actions
    }

    onMounted(() => {
      if (orderListState.status === 'FULFILLED') {
        actions.value = uniq(getActions(record))
      }
    })

    return {
      uniq,
      actions,
      record,
      orderListState,
    }
  }
})
</script>
