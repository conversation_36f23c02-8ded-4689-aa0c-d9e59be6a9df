<template>
  <!-- {{ orderListState?.data?.data?.data }} -->
  <a4-popconfirm
    v-if="currentRecord?.canBeCancelled"
    title="确定要取消意向单吗?"
    ok-text="Yes"
    cancel-text="No"
    @confirm="handleCancelOrder"
    :disabled="
      orderDetailState?.cancel.status === 'PENDING'
      || orderDetailState?.status === 'PENDING'
    "
  >
    <a4-button
      size="small"
      style="padding: 0 4px; margin: 0 4px;"
      danger
    >取消意向单</a4-button>
  </a4-popconfirm>
</template>

<script type="ts">
import { eventBus } from '@/eventBus';
import { find } from 'lodash'
import {
  Button as A4Button,
  Popconfirm as A4Popconfirm,
  message
} from 'ant-design-vue-4';
import { createVNode, defineComponent, toRaw, computed } from 'vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
    A4Popconfirm,
  },
  props: {
    record: {
      type: Object,
    },
    orderNo: {
      type: String,
    }
  },
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      orderListState,
      orderDetailState,
    } = store.state.intentionOrders

    // const { orderSource } = props.record

    const orderListData = computed(() => {
      return orderListState?.data?.data?.data || []
    })

    const currentRecord = computed(() => {
      return find(orderListData.value, { orderNo: props.orderNo })
    })

    const handleCancelOrder = () => {
      const {
        roles
      } = userState?.data?.data ?? {}

      store.dispatch('intentionOrders/cancelOrder', {
        orderNo: currentRecord.value?.orderNo,
      }).then((res) => {
        // 
        if (res.code === '0') {
          message.success('取消成功！', 0.9, () => {
            eventBus.emit('intention-orders-search:form-view:submit-form');
          })
        } else {
          message.error(res.message || '系统异常，创建失败！', 0.9, () => {
            eventBus.emit('intention-orders-search:form-view:submit-form');
          })
        }
      }).catch((err) => {
        // message.error(err?.message)
        // debugger
      })
    }

    return {
      moment,
      orderListData,
      currentRecord,
      // actionState,
      orderListState,
      orderDetailState,
      handleCancelOrder,
    }
  }
})
</script>
