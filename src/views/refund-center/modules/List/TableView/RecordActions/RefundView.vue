<template>
  <a4-button
    size="small"
    type="link"
    style="padding: 0 4px;"
    @click.prevent.stop="handleClick"
  >查看</a4-button>
</template>

<script lang="ts">
import { createVNode, defineComponent, toRaw } from 'vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'
import { Button as A4Button, message } from 'ant-design-vue-4';

export default defineComponent({
  components: {
    A4Button
  },
  props: {
    record: {
      type: Object,
    }
  },
  setup(props) {
    const store = useStore();
    const {
      actionState
    } = store.state.refundCenter

    const {
      userState
    } = store.state.global
    
    const handleClick = () => {
      const {
        roles
      } = userState?.data?.data ?? {}

      store.commit('refundCenter/updateRcActionState', {
        type: 'OrderRefundView',
        record: props.record,
      })
    }

    return {
      handleClick
    }
  }
})
</script>