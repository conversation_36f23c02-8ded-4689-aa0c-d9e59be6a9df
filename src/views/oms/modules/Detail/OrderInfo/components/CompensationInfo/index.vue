<template>
  <section class="section-card">
    <a4-card
      title="补偿信息"
      size="small"
    >
      <a4-table
        size="small"
        :scroll="{ x: true }"
        :columns="columns"
        :data-source="
          orderDetailState?.data?.priceProtectionRecords || []
        "
        :pagination="false"
        :locale="{ emptyText: '暂无数据' }"
        :loading="false"
        :rowKey="(record) => record.key"
      >
        <template #headerCell="{ column }">
          <span style="white-space: nowrap;">{{ column.title }}</span>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'refundAmount'">
            <ik-price :price="record.refundAmount"></ik-price>
          </template>
          <template v-else-if="column.key === 'compareStoreNo'">
            {{ find(stores, { storeNo: record.compareStoreNo })?.storeNameCn || record.compareStoreNo }}
          </template>
          <template v-else-if="column.key === 'status'">
            {{ record?.status }}
          </template>
          <template v-else-if="column.key === 'operation'">
          </template>
          <template v-else>
            {{ record[column.key] }}
          </template>
        </template>
      </a4-table>
    </a4-card>
  </section>
</template>

<script>
import { find } from 'lodash'
import { createVNode, defineComponent, toRaw, computed } from 'vue'
import { useStore } from 'vuex'
import  moment from 'moment'
import {
  Table as A4Table,
  Tooltip as A4Tooltip,
  Card as A4Card,
  message
} from 'ant-design-vue-4'
import IkPrice from '@/stories/components/Price/index.vue'
// import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
// import { compact } from 'lodash'

const columns = [
  {
    title: '补差金额',
    dataIndex: 'refundAmount',
    key: 'refundAmount',
    width: 80,
  },
  {
    title: '退款状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '比价渠道',
    dataIndex: 'compareStoreNo',
    key: 'compareStoreNo',
    width: 120,
  },
  {
    title: '退款申请时间',
    dataIndex: 'applyDate',
    key: 'applyDate',
    width: 100,
  },
  {
    title: '完成时间',
    dataIndex: 'completeDate',
    key: 'completeDate',
    width: 100,
  },
  {
    title: '申请人',
    dataIndex: 'applier',
    key: 'applier',
    width: 80,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 100,
  },
];

export default defineComponent({
  components: {
    A4Table,
    A4Tooltip,
    A4Card,
    IkPrice,
  },
  setup() {
    const store = useStore()
    const { orderDetailState, actionState } = store.state.oms

    const {
      userState
    } = store.state.global

    const { searchPermissionStoreState } = store.state.oms;

    const stores = computed(() => {
      return searchPermissionStoreState?.data?.data?.permissionStoreList || []
    })

    return {
      find,
      columns,
      moment,
      orderDetailState,
      actionState,
      searchPermissionStoreState,
      stores,
    }
  },
})
</script>

<style lang="scss" scoped>
.section-card {
  padding-top: 16px;
  // border-bottom: 1px solid #E2E2E2;
  
  :deep(.ant-card-head) {
    border-bottom: 0;
  }
  :deep(.ant-card-body) {
    padding: 0 12px 12px;
  }
  :deep(.ant-form-item) {
    margin-bottom: .2rem;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  &-80 {
    width: 80px;
  }
  &-120 {
    width: 120px;
  }
  &-150 {
    width: 150px;
  }
  &-180 {
    width: 180px;
  }
  &-200 {
    width: 200px;
  }
}

.subsidy-words {
  display: flex;
  color: #f00;
}
</style>