<template>
  <a4-drawer
    :open="open"
    class="retrieval-data"
    root-class-name="root-retrieval-data"
    :width="800"
    :bodyStyle="{ padding: 0 }"
    style="color: red"
    title="Retrieval Data"
    placement="right"
    @close="handleClose"
  >
    <a4-spin :spinning="retrievalDataState.status === 'PENDING'">
      <div style="height: calc(100vh - 57px);">
        <codemirror
          v-model="code"
          placeholder="Retrieval Data"
          :autofocus="true"
          :indent-with-tab="true"
          :tab-size="2"
          :extensions="extensions"
          @ready="handleReady"
          @change="log('change', $event)"
          @focus="log('focus', $event)"
          @blur="log('blur', $event)"
          :style="{ height: '100%' }"
        />
      </div>
    </a4-spin>
  </a4-drawer>
</template>

<script lang="ts">
import { defineComponent, computed, shallowRef, ref } from 'vue'
import { useStore } from 'vuex'
import {
  Spin as A4Spin,
  Drawer as A4Drawer,
} from 'ant-design-vue-4';
// import VueJsonPretty from 'vue-json-pretty';
// import 'vue-json-pretty/lib/styles.css';

import { Codemirror } from 'vue-codemirror'
import { json } from '@codemirror/lang-json'
import { oneDark } from '@codemirror/theme-one-dark'
import { EditorState } from '@codemirror/state'

export default defineComponent({
  components: {
    // VueJsonPretty,
    Codemirror,
    A4Spin,
    A4Drawer,
  },
  setup() {
    const extensions = [
      json(), oneDark, EditorState.readOnly.of(true)
    ]

    const view = shallowRef()
    const store = useStore()

    const {
      orderDetailState,
      retrievalDataState,
    } = store.state.oms

    const orderDetailData = computed(() => {
      return orderDetailState?.data
    })

    const retrievalData = computed(() => {
      return retrievalDataState?.data
    })

    const code = computed(() => {
      return JSON.stringify(retrievalData?.value, null, 2)
    })

    const handleReady = (payload) => {
      view.value = payload.view
    }

    const open = computed(() => {
      return !!retrievalDataState.status
    })

    const handleClose = () => {
      store.commit('oms/resetRetrievalDataState')
    }

    return {
      orderDetailData,
      retrievalDataState,
      code,
      extensions,
      handleReady,
      log: console.log,
      open,
      handleClose,
    }
  },
})
</script>

<style lang="scss" scoped>
.retrieval-data {

}
</style>