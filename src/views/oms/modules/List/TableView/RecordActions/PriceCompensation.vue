<template>
  <a4-button
    v-if="!isIbOrder && roles.includes('price-protection-apply')"
    size="small"
    type="link"
    style="padding: 0 4px;"
    @click.prevent.stop="handleClick"
  >价格补偿</a4-button>
</template>

<script type="ts">
import { createVNode, defineComponent, toRaw, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { compact } from 'lodash'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'
import { Button as A4Button, message } from 'ant-design-vue-4';

export default defineComponent({
  components: {
    A4Button
  },
  props: {
    record: {
      type: Object,
    }
  },
  setup(props) {
    const bool = ref(false)
    const record = props.record
    const store = useStore();

    const {
      actionState
    } = store.state.oms

    const {
      userState
    } = store.state.global

    const isIbOrder = computed(() => {
      return props?.record?.isIbOrder
    })

    const roles = computed(() => {
      return userState?.data?.data?.roles || []
    })

    const handleClick = () => {
      store.commit('priceCompensation/updateActionState', {
        type: 'OrderCompensationRefund',
        record: props.record,
      })
    }

    return {
      bool,
      moment,
      compact,
      handleClick,
      actionState,
      isIbOrder,
      roles,
    }
  }
})
</script>