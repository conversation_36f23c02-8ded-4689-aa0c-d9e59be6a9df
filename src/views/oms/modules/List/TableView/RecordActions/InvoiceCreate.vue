<template>
  <a4-button
    size="small"
    type="link"
    style="padding: 0 4px;"
    @click.prevent.stop="handleClick"
    v-if="['RAW_JD'].indexOf(record?.orderChannel) > -1"
  >开票</a4-button>
  <a href="https://ovims.ikeait.cn/web/business/home" target="_blank" v-else>开票</a>
</template>

<script lang="ts">
import { createVNode, defineComponent, toRaw, ref } from 'vue'
import { useStore } from 'vuex'
import { compact } from 'lodash'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'
import { Button as A4Button, message } from 'ant-design-vue-4';

export default defineComponent({
  components: {
    A4Button
  },
  props: {
    record: {
      type: Object,
    },
    vid: {
      type: String,
    }
  },
  setup(props) {
    const bool = ref(false)
    const record = props.record

    const store = useStore();
    const {
      actionState
    } = store.state.oms

    const {
      userState
    } = store.state.global

    const handleClick = () => {
      const {
        roles
      } = userState?.data?.data ?? {}

      store.commit('invoiceCenter/updateActionState', {
        type: 'OrderInvoiceCreate',
        record: props.record,
      });
    }
    
    return {
      bool,
      moment,
      compact,
      handleClick,
      actionState,
    }
  }
})
</script>