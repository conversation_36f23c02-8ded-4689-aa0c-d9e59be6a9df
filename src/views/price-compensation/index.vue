<template>
  <management-layout>
    <template #systemTitle>
      {{system.title}}
    </template>

    <template #systemMenus>
      <sider-menu
        :modules="modules"
        :selected-keys="selectedKeys"
        :open-keys="openKeys"
      />
    </template>

    <template #center>
      <layout>
        <template #list>
          <order-list v-if="userState.status==='FULFILLED'">
            <template #mainTitle>
              {{module.title}}
            </template>
            <template #mainTitleExtras>
              <form-extras />
            </template>
            <template #mainContent>
              <template v-if="hasPermission">
                <form-view ref="formViewRef"></form-view>
                <action-view></action-view>
                <table-view></table-view>
              </template>
              <template v-else>
                <a4-result status="403" title="403" sub-title="抱歉，没有访问权限！" v-if="userState.status === 'FULFILLED'" />
              </template>
            </template>
            <template #bfc>
              <compensation-dialog
                :show="
                  ['OrderCompensationView', 'OrderCompensationAudit'].includes(actionState.type)
                "
              />
            </template>
          </order-list>
        </template>
      </layout>
    </template>
  </management-layout>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { defineComponent, onBeforeMount, onMounted, ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Button as A4Button,
  Row as A4Row,
  Col as A4Col,
  Result as A4Result,
  Menu as A4Menu,
  SubMenu as A4SubMenu,
  MenuItem as A4MenuItem,
} from 'ant-design-vue-4'
import ManagementLayout from '@/components/ManagementLayout/index.vue'
import SiderMenu from '@/components/SiderMenu/index.vue'
import Layout from './Layout.vue'
import OrderList from './modules/List/index.vue'
import FormExtras from './modules/List/FormExtras/index.vue'
import FormView from './modules/List/FormView/index.vue'
import ActionView from './modules/List/ActionView/index.vue'
import TableView from './modules/List/TableView/index.vue'
import CompensationDialog from '@/components/CompensationDialog/index.vue'
import { useInit } from '@/composables/price-compensation/useInit';

export default defineComponent({
  name: 'PriceCompensation',
  components: {
    A4Button,
    A4Row,
    A4Col,
    A4Result,
    A4Menu,
    A4SubMenu,
    A4MenuItem,
    ManagementLayout,
    SiderMenu,
    Layout,
    OrderList,
    FormExtras,
    FormView,
    ActionView,
    TableView,
    CompensationDialog,
  },
  setup(props) {
    const {
      role,
      roles,
      selectedKeys,
      openKeys,
      system,
      module,
      modules,
      userState,
      actionState,
      globalState,
      hasPermission,
    } = useInit(props)

    return {
      role,
      roles,
      selectedKeys,
      openKeys,
      system,
      module,
      modules,
      userState,
      actionState,
      globalState,
      hasPermission,
    }
  }
})
</script>
