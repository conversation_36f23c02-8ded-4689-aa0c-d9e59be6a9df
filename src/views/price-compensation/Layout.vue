<template>
  <slot name="list" />
</template>

<script>
import { defineComponent, onMounted, ref } from 'vue'
export default defineComponent({
  setup() {
    const tab = ref('order-list')
    onMounted(() => {
    })
    return {
      tab,
    }
  },
})
</script>


<style scoped lang="scss">
.master-data-tabs {
  // height: 100%;
  // border: 1px solid #000;
  display: flex;
  flex-direction: column;
  // border: 1px solid #000;
  flex: 0 0 auto;
  // sass 样式穿透
  > :deep(.ant-tabs-bar) {
    display: block;
    padding: 0;
    margin: 0;
    background-color: #F8F8F8;
    .ant-tabs-nav-container {
      height: 32px!important;
    }
    .ant-tabs-nav {
      .ant-tabs-tab {
        background-color: #EFEFEF;
        color: #999;
        font-size: 12px;
        height: 32px!important;
        line-height: 32px!important;
        &-active {
          background-color: #22599E;
          color: #fff;
          height: inherit;
          
          .ant-tabs-close-x {
            color: #fff;
            &:hover {
              color: #fff;
            }
          }
        }
      }
    }
    .ant-tabs-ink-bar {
      background-color: transparent;
    }
    .ant-tabs-extra-content {
      display: none;
    }
  }
  > :deep(.ant-tabs-content) {
    flex: 1 1 auto;
    overflow: hidden;
    position: relative;
    // border: 10px solid #000;
    > .ant-tabs-tabpane {
      // border: 10px solid #ff0;
      height: 100%;
      position: absolute;
      padding: 0!important;
    }
  }
}
</style>