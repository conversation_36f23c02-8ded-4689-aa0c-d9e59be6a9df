<template>
  <div class="action-wrapper">
    <div class="action-wrapper-l">
    </div>
    <div class="action-wrapper-r">
      <a4-button
        size="small"
        class="action-button"
        style="padding: 0 16px;"
        :disabled="downloadState?.status === 'PENDING'"
        :loading="downloadState?.status === 'PENDING'"
        @click="handleDownload"
      >
        {{`下载`}}
      </a4-button>
    </div>
  </div>
</template>

<script lang="ts">
import dayjs from 'dayjs'
import { defineComponent, ref, watch, toRaw } from 'vue'
import { Button as A4Button } from 'ant-design-vue-4'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import {
  PlusSquareTwoTone,
  PlusCircleTwoTone,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue-4';
// import { downloadBlob } from '@/utils'

export default defineComponent({
  components: {
    PlusSquareTwoTone,
    PlusCircleTwoTone,
    A4Button,
  },
  setup(props) {
    const store = useStore();
    const router = useRouter()

    const {
      orderListState,
      downloadState,
    } = store.state.priceCompensation;

    const {
      userState,
    } = store.state.global

    const handleDownload = () => {
      const params = {
        ...orderListState?.formState
      }

      delete params.page
      delete params.curPage
      delete params.baseSize
      delete params.pageSize

      if (params?.applyDateStartTimeStamp) {
        params.applyDateStart = dayjs(params?.applyDateStartTimeStamp).format('YYYY-MM-DD')
        delete params?.applyDateStartTimeStamp
      }
      if (params?.applyDateEndTimeStamp) {
        params.applyDateEnd = dayjs(params?.applyDateEndTimeStamp).format('YYYY-MM-DD')
        delete params?.applyDateEndTimeStamp
      }
      if (params?.completeDateStartTimeStamp) {
        params.completeDateStart = dayjs(params?.completeDateStartTimeStamp).format('YYYY-MM-DD')
        delete params?.completeDateStartTimeStamp
      }
      if (params?.completeDateEndTimeStamp) {
        params.completeDateEnd = dayjs(params?.completeDateEndTimeStamp).format('YYYY-MM-DD')
        delete params?.completeDateEndTimeStamp
      }
      
      store.dispatch('priceCompensation/download', params).then((res) => {
        console.log(res)
        if (res?.data) {
          if (res?.data?.code === '0') {
            message.success('已添加任务到下载中心')
          } else {
            message.error(res?.data?.msg)
          }
        }
        // downloadBlob(res, `attachment; filename=refund_info_${new Date().valueOf()}.xlsx`)
      })
    }
    
    return {
      orderListState,
      userState,
      downloadState,
      roles: userState?.data?.data?.roles,
      handleDownload,
    }
  },
})
</script>

<style scoped lang="scss">
.action-wrapper {
  margin: 1rem;
  display: flex;
  &-l {
    flex: 1 1 auto;
    .action-button {
      margin-right: .5rem;
    }
  }
  &-r {
    flex: 0 0 auto;
    .action-button {
      margin-left: .5rem;
    }
  }
}
</style>