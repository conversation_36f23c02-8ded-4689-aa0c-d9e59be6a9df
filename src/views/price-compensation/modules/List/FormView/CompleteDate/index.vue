<template>
  <div class="form-item">
    <h3>退款完成时间</h3>
    <a4-form-item
      ref="completeDate"
      name="completeDate"
    >
      <a4-range-picker
        size="small"
        v-model:value="timeRange"
        format="YYYY-MM-DD"
        @change="handleChange"
        :disabled="
          orderListState.status === 'PENDING'
        "
      >
        <template #renderExtraFooter></template>
      </a4-range-picker>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { compact } from 'lodash'
import dayjs from 'dayjs';
import { defineComponent, computed, ref } from 'vue';
import { useStore } from 'vuex'
import {
  FormItem as A4FormItem,
  RangePicker as A4RangePicker,
} from 'ant-design-vue-4'
export default defineComponent({
  components: {
    A4FormItem,
    A4RangePicker,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { orderListState } = store.state.priceCompensation;

    const timeRange = computed(() => {
      return compact([
        orderListState?.formState?.completeDateStartTimeStamp
          ? dayjs(orderListState?.formState?.completeDateStartTimeStamp) : undefined,
        orderListState?.formState?.completeDateEndTimeStamp
          ? dayjs(orderListState?.formState?.completeDateEndTimeStamp) : undefined,
      ])
    })

    const handleChange = (range: any) => {
      const [ from, end ] = range || []
      store.commit('priceCompensation/updateOrderListFormState', {
        completeDateStartTimeStamp: from?.startOf('day').valueOf(),
        completeDateEndTimeStamp: end?.endOf('day').valueOf(),
        page: 1,
      });
    }

    return {
      orderListState,
      timeRange,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
</style>