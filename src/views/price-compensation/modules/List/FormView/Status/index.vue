<template>
  <div class="form-item">
    <h3>退款状态</h3>
    <a4-form-item
      ref="orderStatus"
      name="orderStatus"
      :hasFeedback="false"
    >
      <a4-select
        size="small"
        v-model:value="status"
        placeholder="不限"
        :options="options"
        @change="handleChange"
        :disabled="
          orderListState.status === 'PENDING'
        "
      ></a4-select>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { useStore } from 'vuex'
import {
  Select as A4Select,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';
export default defineComponent({
  components: {
    A4Select,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { orderListState } = store.state.priceCompensation;

    const status = computed(() => {
      return orderListState?.formState?.status
    })

    const options = ref([
      { value: '', label: '不限' },
      { value: 'REFUND_SUCCESS', label: '退款成功' },
      { value: 'REFUND_FAILED', label: '退款失败' },
      { value: 'WAITING_APPROVE', label: '待审核' },
      { value: 'REFUND_PROGRESS', label: '退款处理中' },
      { value: 'COMPLETE', label: '补偿完成' },
      { value: 'APPROVED', label: '审核通过' },
    ]);

    const handleChange = (v: any) => {
      store.commit('priceCompensation/updateOrderListFormState', {
        status: v,
      });
    }

    return {
      options,
      orderListState,
      status,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>