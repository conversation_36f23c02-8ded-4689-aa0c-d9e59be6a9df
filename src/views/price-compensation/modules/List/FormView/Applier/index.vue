<template>
  <div class="form-item">
    <h3>申请人</h3>
    <a4-form-item
      ref="applier"
      name="applier"
      :hasFeedback="false"
      :help="false"
    >
      <a4-input
        size="small"
        autocorrect="off"
        placeholder="申请人"
        v-model:value="applier"
        :disabled="
          orderListState.status === 'PENDING'
        "
        @change="handleChange"
      />
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { useStore } from 'vuex'
import {
  Input as A4Input,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';
export default defineComponent({
  components: {
    A4Input,
    A4FormItem,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();
    const { orderListState } = store.state.priceCompensation;

    const applier = computed(() => {
      return orderListState?.formState?.applier
    })

    const handleChange = (e: any) => {
      store.commit('priceCompensation/updateOrderListFormState', {
        applier: e?.target?.value?.trim(),
      });
    }

    return {
      orderListState,
      applier,
      handleChange,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>