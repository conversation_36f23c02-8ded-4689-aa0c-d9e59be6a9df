<template>
  <!-- === {{ searchPermissionStoreState }} === -->
   <!-- {{ options }} -->
  <div class="form-item">
    <h3>订单门店</h3>
    <a4-form-item
      ref="orderStoreNo"
      name="orderStoreNo"
      :hasFeedback="false"
      :help="false"
    >
      <a4-select
        v-model:value="orderStoreNo"
        size="small"
        allowClear
        placeholder="订单门店"
        style="width: 100%;"
        :options="options"
        option-label-prop="label"
        :disabled="
          orderListState.status === 'PENDING' ||
          searchPermissionStoreState.status === 'PENDING'
        "
        @change="handleChange"
      >
      </a4-select>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { useStore } from 'vuex'
import {
  Input as A4Input,
  FormItem as A4FormItem,
  Select as A4Select,
} from 'ant-design-vue-4';
export default defineComponent({
  components: {
    A4Input,
    A4FormItem,
    A4Select,
  },
  // emits: ['change'],
  setup() {
    const store = useStore();

    const {
      searchPermissionStoreState,
    } = store.state.oms;

    const { orderListState } = store.state.priceCompensation;

    const orderStoreNo = computed(() => {
      return orderListState?.formState?.orderStoreNo
    })

    const options = computed(() => {
      return (searchPermissionStoreState?.data?.data?.permissionStoreList || []).map((store: any) => {
        return {
          label: store.storeNameCn || store.storeNo,
          value: store.storeNo,
        }
      })
    })

    const handleChange = (v: any) => {
      store.commit('priceCompensation/updateOrderListFormState', {
        orderStoreNo: v,
      });
    }

    return {
      searchPermissionStoreState,
      orderListState,
      orderStoreNo,
      handleChange,
      options,
    };
  },
});
</script>

<style scoped lang="scss">
h3 {
  font-size: 14px;
  font-weight: 400;
  display: block;
  line-height: 1.25rem;
  margin-bottom: 0;
}
.form-item {
  :deep(.ant-form-item-explain) {
    display: none;
  }
}
</style>