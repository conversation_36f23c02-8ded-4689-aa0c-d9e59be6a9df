<template>
  <!-- === {{ orderListState?.formState }} === -->
  <div class="form-wrapper">
    <a4-form
      ref="formRef"
      :model="orderListState.formState"
      :rules="rules"
      autocomplete="off"
      class="form"
    >
      <div class="form-items-wrapper">
        <vid />

        <contact-phone-no />

        <applier />

        <compare-store-no />

        <order-store-no />

        <auditor />

        <status />

        <apply-date />

        <complete-date />
      </div>
    </a4-form>
  </div>
</template>

<script lang="ts">
import dayjs from 'dayjs';
import { eventBus } from '@/eventBus';
import { defineComponent, ref, UnwrapRef, reactive, watch, toRaw, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Form as A4Form,
} from 'ant-design-vue-4'

import Vid from './Vid/index.vue';
import ContactPhoneNo from './ContactPhoneNo/index.vue';
import Applier from './Applier/index.vue';
import Auditor from './Auditor/index.vue';
import CompareStoreNo from './CompareStoreNo/index.vue';
import OrderStoreNo from './OrderStoreNo/index.vue';
import Status from './Status/index.vue';
import ApplyDate from './ApplyDate/index.vue';
import CompleteDate from './CompleteDate/index.vue';

import { Query_Price_Compensation_OrderList_Req } from '@/types/price-compensation';

export default defineComponent({
  name: 'FormViewComponent',
  components: {
    A4Form,
    Vid,
    ContactPhoneNo,
    Applier,
    Auditor,
    CompareStoreNo,
    OrderStoreNo,
    Status,
    ApplyDate,
    CompleteDate,
  },
  setup() {
    const store = useStore()
    const {
      orderListState,
    } = store.state.priceCompensation

    const formRef = ref()

    const rules = {}

    const vid = computed(() => {
      return orderListState?.formState?.vid?.trim()
    })

    const contactPhoneNo = computed(() => {
      return orderListState?.formState?.contactPhoneNo
    })

    const applier = computed(() => {
      return orderListState?.formState?.applier
    })

    const auditor = computed(() => {
      return orderListState?.formState?.auditor
    })

    const compareStoreNo = computed(() => {
      return orderListState?.formState?.compareStoreNo
    })

    const orderStoreNo = computed(() => {
      return orderListState?.formState?.orderStoreNo
    })

    const status = computed(() => {
      return orderListState?.formState?.status
    })

    const applyDateStart = computed(() => {
      if (orderListState?.formState?.applyDateStartTimeStamp) {
        return dayjs(orderListState?.formState?.applyDateStartTimeStamp).format('YYYY-MM-DD')
      }
      return
    })

    const applyDateEnd = computed(() => {
      if (orderListState?.formState?.applyDateEndTimeStamp) {
        return dayjs(orderListState?.formState?.applyDateEndTimeStamp).format('YYYY-MM-DD')
      }
      return
    })

    const completeDateStart = computed(() => {
      if (orderListState?.formState?.completeDateStartTimeStamp) {
        return dayjs(orderListState?.formState?.completeDateStartTimeStamp).format('YYYY-MM-DD')
      }
      return
    })

    const completeDateEnd = computed(() => {
      if (orderListState?.formState?.completeDateEndTimeStamp) {
        return dayjs(orderListState?.formState?.completeDateEndTimeStamp).format('YYYY-MM-DD')
      }
      return
    })

    const curPage = computed(() => orderListState?.formState?.curPage)
    const pageSize = computed(() => orderListState?.formState?.pageSize)

    const handleSubmitForm = async () => {
      if (!formRef.value) return
      await formRef.value
        .validate()
          .then(() => {
            const payload: Query_Price_Compensation_OrderList_Req = {
              // "contactPhoneNo":"17717072460",
              // "applier":null,
              // "compareStoreNo":"549",
              // "orderStoreNo":"549",
              // "auditor":null,
              // "status":null,
              // "applyDate":"2025-02-25",
              // "completeDate":"2025-02-25",

              vid: vid.value ? vid.value : undefined,
              contactPhoneNo: contactPhoneNo.value ? contactPhoneNo.value : undefined,
              applier: applier.value ? applier.value : undefined,
              auditor: auditor.value ? auditor.value : undefined,
              compareStoreNo: compareStoreNo.value ? compareStoreNo.value : undefined,
              orderStoreNo: orderStoreNo.value ? orderStoreNo.value : undefined,
              status: status.value ? status.value : undefined,
              applyDateStart: applyDateStart.value,
              applyDateEnd: applyDateEnd.value,
              completeDateStart: completeDateStart.value,
              completeDateEnd: completeDateEnd.value,
              curPage: curPage.value,
              pageSize: pageSize.value,
            }

            store.dispatch('priceCompensation/fetchOrderList', payload)
          })
          .catch((error) => {
            console.log('error', error);
            return false
          })
    }

    const handleResetForm = () => {
      if (!formRef.value) return
      formRef.value.clearValidate()
      formRef.value.resetFields()
      store.commit('priceCompensation/resetOrderListFormState')
      handleSubmitForm()
    }

    eventBus.on('price-compensation-search:form-view:submit-form', handleSubmitForm);
    eventBus.on('price-compensation-search:form-view:reset-form', handleResetForm);

    return {
      dayjs,
      rules,
      formRef,
      orderListState,
      handleSubmitForm,
      handleResetForm,
    }
  }
})
</script>


<style scoped lang="scss">
.form-wrapper {
  border-bottom: 1px solid #efefef;
  margin: 0 1rem 1rem 1rem;
  padding-bottom: 1rem;

  .form-items-wrapper {
    width: 100%;
    max-width: 1285px;
    min-width: 900px;
    display: flex;
    flex-wrap: wrap;
    // margin-bottom: 1rem;

    .form-item {
      flex: 0 0 20%;
      min-width: 180px;
      padding-right: 1rem;
      margin-bottom: 4px;
      &-margin-bottom {
        margin-bottom: 1rem;
      }

      h3 {
        font-size: 14px;
        font-weight: 400;
        display: block;
        line-height: 1rem;
      }
    }

    :deep(.ant-form-item) {
      // border: 1px solid #000;
      margin: 0;
    }
  }
}

.checkbox-wrapper {
  border: 1px solid #d9d9d9;
  padding: .5rem 1rem;
  border-radius: 4px;
  // margin-bottom: 1rem;
  width: 310px;
}

.nowrap {
  white-space: nowrap;
}
</style>