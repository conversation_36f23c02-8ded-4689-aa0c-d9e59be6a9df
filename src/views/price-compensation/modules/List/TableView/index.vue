<template>
  <!-- {{ stores }} -->
  <div class="order-list-table">
    <!-- {{ orderListState?.data?.data?.totalNum }} -->
    <!-- {{ stores }} -->
    <!-- == {{ orderListState?.data?.data?.records }} == -->
    <a4-table
      bordered
      size="small"
      :scroll="{
        x: true,
        // y: 420
      }"
      :columns="columns"
      :pagination="false"
      :locale="{ emptyText: '暂无数据' }"
      :data-source="orderListState?.data?.data?.records || []"
      :loading="orderListState.status === 'PENDING'"
      :rowKey="record => record?.priceProtectionId?.toString()"
    >
      <template #headerCell="{ column }">
        <span class="nowrap">{{column.title}}</span>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'orderNo'">
          <span class="nowrap">{{record.orderNo}}</span>
        </template>
        <template v-else-if="column.key === 'orderStoreNo'">
          {{ find(stores, { storeNo: record.orderStoreNo }) ? find(stores, { storeNo: record.orderStoreNo })?.storeNameCn : record.orderStoreNo }}
        </template>
        <template v-else-if="column.key === 'compareStoreNo'">
          {{ find(stores, { storeNo: record.compareStoreNo }) ? find(stores, { storeNo: record.compareStoreNo })?.storeNameCn : record.compareStoreNo }}
        </template>
        <template v-else-if="column.key === 'refundAmount'">
          <ik-price v-if="record.refundAmount" :price="record.refundAmount" />
        </template>
        <template v-else-if="column.key === 'operation'">
          <div style="min-width: 60px;">
            <record-actions :record="record" />
          </div>
        </template>
        <template v-else>
          <span class="nowrap">{{record[column.dataIndex]}}</span>
        </template>
      </template>
    </a4-table>
    
    <div class="pagination" v-if="orderListState?.data?.data">
      <a4-pagination
        size="small"
        v-if="orderListState?.data?.data?.totalNum > 0"
        :disabled="orderListState.status === 'PENDING'"
        v-model:current="orderListState.formState.curPage"
        :total="orderListState?.data?.data?.totalNum"
        @change="handlePageChange"
        :pageSize="orderListState.formState.pageSize"
        :showSizeChanger="true"
        @showSizeChange="handlePageSizeChange"
        :pageSizeOptions="[
          String(orderListState.formState.baseSize * 1),
          String(orderListState.formState.baseSize * 2),
          String(orderListState.formState.baseSize * 4),
          String(orderListState.formState.baseSize * 10),
        ]"
        :show-total="(total: any) => `共 ${total || 0} 条`"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'
import moment from 'moment';
import {
  Table as A4Table,
  Tooltip as A4Tooltip,
  Pagination as A4Pagination,
} from 'ant-design-vue-4'
import columns from './columns'
import RecordActions from './RecordActions/index.vue'
import { invert, find } from 'lodash'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime';
import updateLocale from 'dayjs/plugin/updateLocale';
import IkPrice from '@/stories/components/Price/index.vue'

dayjs.extend(relativeTime)
dayjs.extend(updateLocale)
dayjs.locale('zh-cn')

export default defineComponent({
  components: {
    A4Table,
    A4Tooltip,
    A4Pagination,
    IkPrice,
    RecordActions,
  },
  setup () {
    const store = useStore();

    // const { storesState } = store.state.global;
    const { searchPermissionStoreState } = store.state.oms;
    const { orderListState } = store.state.priceCompensation;
    
    const stores = computed(() => {
      return searchPermissionStoreState?.data?.data?.permissionStoreList || []
    })

    const handlePageChange = (pageNumber: number) => {
      store.dispatch('priceCompensation/fetchOrderList', {
        ...orderListState.formState,
        curPage: pageNumber,
      })
    }

    const handlePageSizeChange = (current: number, size: number) => {
      store.commit('priceCompensation/updateOrderListFormState', {
        pageSize: size,
      })
      store.dispatch('priceCompensation/fetchOrderList', {
        ...orderListState.formState,
        curPage: 1,
        pageSize: size,
      })
    }

    return {
      moment,
      dayjs,
      columns,
      handlePageChange,
      handlePageSizeChange,
      orderListState,
      stores,
      find,
      searchPermissionStoreState,
    }
  }
})
</script>

<style lang="scss" scoped>
.order-list-table {
  margin: 0 1rem;
  h2 {
    font-size: 16px;
  }

  .pagination {
    margin: 16px 0;
  }

  .nowrap {
    // border: 1px solid #000;
    white-space: nowrap;
  }
}

:deep(.ant-table td) {
  white-space: nowrap;
}
:deep(.ant-table-header) {
  // overflow: inherit!important;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  &-80 {
    width: 80px;
  }
  &-120 {
    width: 120px;
  }
  &-150 {
    width: 150px;
  }
  &-180 {
    width: 180px;
  }
  &-200 {
    width: 200px;
  }
}
</style>