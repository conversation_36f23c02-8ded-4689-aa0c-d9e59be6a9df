<template>
  <a4-button
    v-if="['退款成功', '退款失败', '待审核', '退款处理中', '审核驳回', '补偿完成'].includes(record?.status)"
    size="small"
    style="padding: 0 4px; margin: 0 4px;"
    @click.prevent.stop="handleClick"
    :disabled="
      orderDetailState?.updatePriceCompensationRecord.status === 'PENDING'
    "
  >查看</a4-button>
</template>

<script lang="ts">
import { Button as A4Button, message } from 'ant-design-vue-4';
import { createVNode, defineComponent, toRaw } from 'vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
  },
  props: {
    record: {
      type: Object,
    }
  },
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      orderDetailState,
    } = store.state.priceCompensation

    // const { orderSource } = props.record

    const handleClick = () => {
      const {
        roles
      } = userState?.data?.data ?? {}
      
      store.commit('priceCompensation/updateActionState', {
        type: 'OrderCompensationView',
        record: props.record,
      })
    }

    return {
      moment,
      handleClick,
      // actionState,
      orderDetailState,
    }
  }
})
</script>
