<template>
  <!-- {{orderDetailState?.updatePriceCompensationRecord?.params}} -->
  <a4-button
    v-if="['退款失败'].includes(record?.status)"
    type="primary"
    size="small"
    style="padding: 0 4px; margin: 0 4px;"
    @click.prevent.stop="handleClick"
    :disabled="
      orderDetailState?.updatePriceCompensationRecord.status === 'PENDING'
    "
    :loading="
      orderDetailState?.updatePriceCompensationRecord.status === 'PENDING'
      && orderDetailState?.updatePriceCompensationRecord?.params?.priceProtectionId?.toString() === record?.priceProtectionId?.toString()
      && orderDetailState?.updatePriceCompensationRecord?.params?.status === 'REFUND'
    "
  >退款</a4-button>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { Button as A4Button, message } from 'ant-design-vue-4';
import { createVNode, defineComponent, toRaw } from 'vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
  },
  props: {
    record: {
      type: Object,
    }
  },
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      orderDetailState,
    } = store.state.priceCompensation

    const handleClick = () => {
      const {
        roles
      } = userState?.data?.data ?? {}
      
      store.dispatch('priceCompensation/updatePriceCompensationRecord', {
        priceProtectionId: props?.record?.priceProtectionId?.toString(),
        status: 'REFUND',
      }).then((res) => {
        // 
        if (res.code === '0') {
          message.success('退款已完成！', 0.9, () => {
            eventBus.emit('price-compensation-search:form-view:submit-form');
          })
        } else {
          message.error(res.message || '系统异常，请重试！', 0.9, () => {
            eventBus.emit('price-compensation-search:form-view:submit-form');
          })
        }
      }).catch((err) => {
        // message.error(err?.message)
        // debugger
      })

    }

    return {
      moment,
      handleClick,
      orderDetailState,
      // actionState,
    }
  }
})
</script>
