<template>
  <!-- {{orderDetailState?.updatePriceCompensationRecord?.params}} -->
  <a4-popconfirm
    title="确定已完成吗？"
    ok-text="Yes"
    cancel-text="No"
    @confirm="handleComplete"
  >
    <a4-button
      v-if="['退款失败'].includes(record?.status)"
      size="small"
      style="padding: 0 4px; margin: 0 4px;"
      :disabled="
        orderDetailState?.updatePriceCompensationRecord.status === 'PENDING'
      "
      :loading="
        orderDetailState?.updatePriceCompensationRecord.status === 'PENDING'
        && orderDetailState?.updatePriceCompensationRecord?.params?.priceProtectionId?.toString() === record?.priceProtectionId?.toString()
        && orderDetailState?.updatePriceCompensationRecord?.params?.status === 'COMPLETE'
      "
    >已完成</a4-button>
  </a4-popconfirm>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import {
  Button as A4Button,
  Popconfirm as A4Popconfirm,
  message
} from 'ant-design-vue-4';
import { createVNode, defineComponent, toRaw } from 'vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
    A4Popconfirm,
  },
  props: {
    record: {
      type: Object,
    }
  },
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      orderDetailState,
    } = store.state.priceCompensation

    const handleComplete = () => {
      const {
        roles
      } = userState?.data?.data ?? {}
      
      // debugger
      
      store.dispatch('priceCompensation/updatePriceCompensationRecord', {
        priceProtectionId: props?.record?.priceProtectionId?.toString(),
        status: 'COMPLETE',
      }).then((res) => {
        // 
        if (res.code === '0') {
          message.success('价格保护已完成！', 0.9, () => {
            eventBus.emit('price-compensation-search:form-view:submit-form');
          })
        } else {
          message.error(res.message || '系统异常，请重试！', 0.9, () => {
            eventBus.emit('price-compensation-search:form-view:submit-form');
          })
        }
      }).catch((err) => {
        // message.error(err?.message)
        // debugger
      })

    }

    return {
      moment,
      handleComplete,
      orderDetailState,
      // actionState,
    }
  }
})
</script>
