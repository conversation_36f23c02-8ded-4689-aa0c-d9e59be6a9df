<template>
  <div class="actions-wrapper">
    <span v-for="action, index in uniq(actions)" key="index">
      <record-refund :record="record" v-if="action === 'RecordRefund'" />

      <record-view :record="record" v-if="action === 'RecordView'" />

      <record-audit :record="record" v-if="action === 'RecordAudit'" />

      <record-complete :record="record" v-if="action === 'RecordComplete'" />
    </span>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, watch, ref, computed } from 'vue'
import { useStore } from 'vuex'
import { uniq, find } from 'lodash'

import RecordRefund from './RecordRefund.vue'
import RecordView from './RecordView.vue'
import RecordAudit from './RecordAudit.vue'
import RecordComplete from './RecordComplete.vue'

export default defineComponent({
  components: {
    RecordRefund,
    RecordView,
    RecordAudit,
    RecordComplete,
  },
  props: ['record'],
  setup (props) {
    const { record } = props
    // const record = ref(null)
    const actions = ref([])
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      orderListState
    } = store.state.priceCompensation;

    const roles = computed(() => {
      return userState?.data?.data?.roles || []
    })

    const getActions = (record: any) => {
      const _actions: string[] = []

      _actions.push('RecordRefund')

      _actions.push('RecordView')

      if (roles.value.includes('price-protection-approve')) {
        _actions.push('RecordAudit')
      }
     
      _actions.push('RecordComplete')

      return _actions
    }

    onMounted(() => {
      if (orderListState.status === 'FULFILLED') {
        actions.value = uniq(getActions(record))
      }
    })

    return {
      uniq,
      actions,
      record,
      orderListState,
      roles,
    }
  }
})
</script>
