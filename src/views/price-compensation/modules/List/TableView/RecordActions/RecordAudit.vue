<template>
  <!-- {{orderDetailState?.updatePriceCompensationRecord?.params}} -->
  <a4-button
    v-if="['待审核'].includes(record?.status)"
    size="small"
    style="padding: 0 4px; margin: 0 4px;"
    @click.prevent.stop="handleAudit"
    :disabled="
      orderDetailState?.updatePriceCompensationRecord.status === 'PENDING'
    "
    :loading="
      orderDetailState?.updatePriceCompensationRecord.status === 'PENDING'
      && orderDetailState?.updatePriceCompensationRecord?.params?.priceProtectionId?.toString() === record?.priceProtectionId?.toString()
      && orderDetailState?.updatePriceCompensationRecord?.params?.status === 'APPROVED'
    "
  >审核</a4-button>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { Button as A4Button, Popconfirm as A4Popconfirm, message } from 'ant-design-vue-4';
import { createVNode, defineComponent, toRaw } from 'vue'
import { useStore } from 'vuex'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import moment from 'moment'

export default defineComponent({
  components: {
    A4Button,
    A4Popconfirm,
  },
  props: {
    record: {
      type: Object,
    }
  },
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      orderDetailState,
    } = store.state.priceCompensation

    const handleAudit = () => {
      const {
        roles
      } = userState?.data?.data ?? {}

      store.commit('priceCompensation/updateActionState', {
        type: 'OrderCompensationAudit',
        record: props.record,
      })

      // store.dispatch('priceCompensation/updatePriceCompensationRecord', {
      //   priceProtectionId: props?.record?.priceProtectionId?.toString(),
      //   status: 'APPROVED',
      // }).then((res) => {
      //   // 
      //   if (res.code === '0') {
      //     message.success('审核完成！', 0.9, () => {
      //       eventBus.emit('price-compensation-search:form-view:submit-form');
      //     })
      //   } else {
      //     message.error(res.message || '系统异常，审核失败！', 0.9, () => {
      //       eventBus.emit('price-compensation-search:form-view:submit-form');
      //     })
      //   }
      // }).catch((err) => {
      //   // message.error(err?.message)
      //   // debugger
      // })

    }

    const confirm = (e: MouseEvent) => {
      console.log(e);
      // message.success('Click on Yes');
      // handleApprove()
    };

    const cancel = (e: MouseEvent) => {
      console.log(e);
      // message.error('Click on No');
    };

    return {
      moment,
      orderDetailState,
      // actionState,
      confirm,
      cancel,
      handleAudit,
    }
  }
})
</script>
