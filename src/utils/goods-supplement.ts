/**
 * 商品补差相关工具函数
 */

// 占位项 ID 常量
export const PLACEHOLDER_ITEM_ID = -1;

/**
 * 检查两个商品是否为同一商品
 */
export const isSameItem = (item1: any, item2: any): boolean => {
  if (!item1 || !item2) return false;
  
  return item1.productId === item2.productId || 
         item1.ikeaItemNo === item2.ikeaItemNo;
};

/**
 * 检查列表中是否存在占位项
 */
export const hasPlaceholderItem = (list: any[]): boolean => {
  return list.some(item => item?.id === PLACEHOLDER_ITEM_ID);
};

/**
 * 检查商品是否已存在于列表中
 */
export const isItemInList = (item: any, list: any[]): boolean => {
  return list.some(listItem => isSameItem(item, listItem));
};

/**
 * 获取商品的唯一标识符
 */
export const getItemIdentifier = (item: any): string => {
  return item?.ikeaItemNo || item?.productId || '';
};
