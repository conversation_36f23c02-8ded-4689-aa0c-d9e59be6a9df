/**
 * 商品补差工具函数测试
 * 这个文件可以用于验证工具函数的正确性
 */

import {
  PLACEHOLDER_ITEM_ID,
  isSameItem,
  hasPlaceholderItem,
  isItemInList,
  getItemIdentifier,
} from '../goods-supplement';

// 模拟商品数据
const mockItem1 = {
  productId: '12345',
  ikeaItemNo: 'ITEM001',
  productName: 'Test Product 1',
};

const mockItem2 = {
  productId: '67890',
  ikeaItemNo: 'ITEM002',
  productName: 'Test Product 2',
};

const mockItem1Duplicate = {
  productId: '12345',
  ikeaItemNo: 'ITEM001',
  productName: 'Test Product 1 (Different Name)',
};

// 测试用例（可以在浏览器控制台中运行）
console.log('=== 商品补差工具函数测试 ===');

// 测试 isSameItem
console.log('1. 测试 isSameItem:');
console.log('相同商品:', isSameItem(mockItem1, mockItem1Duplicate)); // 应该返回 true
console.log('不同商品:', isSameItem(mockItem1, mockItem2)); // 应该返回 false
console.log('空值处理:', isSameItem(null, mockItem1)); // 应该返回 false

// 测试 hasPlaceholderItem
console.log('2. 测试 hasPlaceholderItem:');
const listWithPlaceholder = [mockItem1, { id: PLACEHOLDER_ITEM_ID }];
const listWithoutPlaceholder = [mockItem1, mockItem2];
console.log('有占位项:', hasPlaceholderItem(listWithPlaceholder)); // 应该返回 true
console.log('无占位项:', hasPlaceholderItem(listWithoutPlaceholder)); // 应该返回 false

// 测试 isItemInList
console.log('3. 测试 isItemInList:');
const itemList = [mockItem1, mockItem2];
console.log('商品在列表中:', isItemInList(mockItem1, itemList)); // 应该返回 true
console.log('商品不在列表中:', isItemInList({ productId: 'NOT_EXIST' }, itemList)); // 应该返回 false

// 测试 getItemIdentifier
console.log('4. 测试 getItemIdentifier:');
console.log('获取标识符:', getItemIdentifier(mockItem1)); // 应该返回 'ITEM001'
console.log('空值处理:', getItemIdentifier(null)); // 应该返回 ''

console.log('=== 测试完成 ===');
