// import { compact } from 'lodash'
import { invoiceCenter } from '@/service'
import { message } from 'ant-design-vue-4'
import dayjs from 'dayjs'

// const isValidPhoneNumber = (rule: any, value: any) => {
//   if (!value || /^[1-9]\d{10}$/.test(value)) {
//     return Promise.resolve()
//   }
//   message.error(rule.message)
//   return Promise.reject(rule.message)
// }

const getDefaultState = (route: any = {}) => {
  const state = {
    status: '',
    query: {},
    formState: {
      // fromTimeStamp: dayjs().startOf('day').subtract(180, 'day').valueOf(),
      // endTimeStamp: dayjs().endOf('day').valueOf(),
      baseSize: 10,
      size: 10,
      page: 1,
    },
    // rules: {
    //   registerPhoneNumber: [
    //     { validator: isValidPhoneNumber, message: '请输入有效的会员手机号(11位数字)', trigger: [] },
    //   ],
    //   customerPhoneNumber: [
    //     { validator: isValidPhoneNumber, message: '请输入有效的收货手机号(11位数字)', trigger: [] },
    //   ],
    //   vid: [
    //   ],
    // },
    selectedRowKeys: [],
    data: {
      code: '',
      msg: '',
      result: undefined,
      totalCount: undefined,
    }
  }
  return state
}

export default {
  state: getDefaultState(),
  mutations: {
    // resetOrderListFormState(state: any, { route = {} }) {
    //   state.formState = getDefaultState(route).formState
    // },
    updateInvoiceListStateStatus(state: any, status: string) {
      state.status = status
    },
    // updateOrderListStateSelectedRowKeys(state: any, selectedRowKeys: any) {
    //   state.selectedRowKeys = selectedRowKeys
    // },
    updateInvoiceListFormState(state: any, formState: any) {
      state.formState = { ...state.formState, ...formState }
    },
    resetInvoiceListData(state: any) {
      const defaultState = getDefaultState()
      state.status = defaultState.status
      state.data = defaultState.data
    }
  },
  actions: {
    async fetchInvoiceList(context: any, payload: any) {
      const {
        state,
        commit,
      } = context
      commit('updateInvoiceListStateStatus', 'PENDING')
      return invoiceCenter.invoicesService.queryInvoiceList(payload)
        .then((res) => {
          if (res instanceof Error) {
            message.error(res.message)
            commit('updateInvoiceListStateStatus', 'REJECTED')
          } else {
            if (res?.data?.code === '0') {
              commit('resetInvoiceListData') 
              commit('updateInvoiceListStateStatus', 'FULFILLED')
              state.data = res.data
            } else {
              message.error(`[${res?.data?.code}]${res?.data?.msg}`)
              commit('updateInvoiceListStateStatus', 'REJECTED')
              state.data = res.data
            }
          }
          return res
        })
        .catch(err => {
          console.log(err)
          message.error(`系统异常，请稍后再试`)
          commit('updateInvoiceListStateStatus', 'REJECTED')
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    }
  }
}