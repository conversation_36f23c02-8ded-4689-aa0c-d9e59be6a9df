const getDefaultState = () => {
  return {
    type: '',
    record: undefined,
  }
}

export default {
  state: getDefaultState(),
  mutations: {
    updateActionState(state: any, newState: any) {
      state.type = newState.type
      state.record = newState.record
    },
    resetActionState(state: any) {
      const defaultState = getDefaultState()
      state.type = defaultState.type
      state.record = defaultState.record
      // state.edit = defaultState.edit
    }
  },
  actions: {
  }
}