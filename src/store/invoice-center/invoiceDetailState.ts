import { invoiceCenter } from '../../service'
import {
  Query_Invoice_Center_Detail_Req,
} from '@/types/invoice-center'
// import paymentSummaryData from './paymentSummaryData'
import { ENV } from '../../utils'
import pad from 'pad'
import { compact } from 'lodash'
import { message } from 'ant-design-vue-4'
import {
  PLACEHOLDER_ITEM_ID,
  isSameItem,
  hasPlaceholderItem,
  isItemInList,
} from '@/utils/goods-supplement'

const getDefaultState = () => {
  const state = {
    status: '',
    query: {},
    actionState: undefined,
    data: {
      code: '',
      data: null,
    },
    manualInvoicing: {
      status: '',
      formState: {
        // mock data
        // buyerName: 'ABC Company',
        // buyerAddress: '123 Main St, City, Country',
        // bankName: 'ABC Bank',
        // bankAccount: '****************',
        // contactPhone: '***********',
        // mailAccount: '<EMAIL>',

        isShowBuyerBank: false,
        isShowSellerBank: false,
      },
      selectedItemLine: undefined,
      data: {
      },
    },
    syncMergeBlueInvoice: {
      status: '',
      data: {
        code: '',
        data: null,
      },
    },
    redraft: {
      status: '',
      data: {
        code: '',
        data: null,
      },
    },
    cancel: {
      status: '',
      data: {
        code: '',
        data: null,
      },
    },
    companyTaxInfo: {
      status: '',
      data: {
      },
    },
    goodsSupplement: {
      status: '',
      data: {
        code: '',
        data: null,
      },
    }
  }
  return state
}

export default {
  state: getDefaultState(),
  mutations: {
    RESET_INVOICE_DETAIL(state: any) {
      const defaultState = getDefaultState()
      state.status = defaultState.status
      state.data = defaultState.data
      state.manualInvoicing = defaultState.manualInvoicing
      state.syncMergeBlueInvoice = defaultState.syncMergeBlueInvoice
      state.redraft = defaultState.redraft
      state.companyTaxInfo = defaultState.companyTaxInfo
      state.goodsSupplement = defaultState.goodsSupplement
    },
    SET_STATUS(state: { status: string }, status: string): void {
      state.status = status
    },
    SET_ACTION_STATE(state: any, actionState: any) {
      state.actionState = actionState
    },
    SET_MANUAL_INVOICING_STATUS(state: { manualInvoicing: { status: string } }, status: string): void {
      state.manualInvoicing.status = status
    },
    SET_SYNC_BLUE_INVOICE_STATUS(state: { syncMergeBlueInvoice: { status: string } }, status: string): void {
      state.syncMergeBlueInvoice.status = status
    },
    SET_SYNC_BLUE_INVOICE_PAYLOAD(state: { syncMergeBlueInvoice: { payload: any } }, payload: any): void {
      state.syncMergeBlueInvoice.payload = payload
    },
    SET_INVOICE_REDRAFT_STATUS(state: { redraft: { status: string } }, status: string): void {
      state.redraft.status = status
    },
    SET_INVOICE_REDRAFT_PAYLOAD(state: { redraft: { payload: any } }, payload: any): void {
      state.redraft.payload = payload
    },
    SET_INVOICE_CANCEL_STATUS(state: { cancel: { status: string } }, status: string): void {
      state.cancel.status = status
    },
    SET_INVOICE_CANCEL_PAYLOAD(state: { cancel: { payload: any } }, payload: any): void {
      state.cancel.payload = payload
    },
    SET_SELECTED_ITEM_LINE(state: any, itemLine: any) {
      state.manualInvoicing.selectedItemLine = itemLine
    },
    SET_COMPANY_TAX_INFO_STATUS(state: { companyTaxInfo: { status: string } }, status: string): void {
      state.companyTaxInfo.status = status
    },
    SET_GOODS_SUPPLEMENT_STATUS(state: { goodsSupplement: { status: string } }, status: string): void {
      state.goodsSupplement.status = status
    },
    updateInvoiceManualFormState(state: any, formState: any) {
      state.manualInvoicing.formState = { ...state.manualInvoicing.formState, ...formState }
    },
  },
  actions: {
    fetchInvoiceDetail(context: any, payload: Query_Invoice_Center_Detail_Req) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      const invoiceTitleType = payload?.invoiceTitleType

      const handleData = (data: any, invoiceTitleType: string) => {          
        commit('updateInvoiceManualFormState', {
          invoiceTitleType: invoiceTitleType || 'PERSON',
          invoiceType: 'GENERAL_DIGITAL_INVOICE',
          clientSystem: 'JD_MANUAL',
          orderId: data.orderId,
          iSellId: data.iSellId,
          buyerName: data.buyerName,
          bankName: data.bankName,
          buyerTaxNo: data.buyerTaxNo,
          buyerAddress: data.buyerAddress,
          bankAccount: data.bankAccount,
          contactPhone: data.phone,
          mailAccount: data.email,
          itemLineList: data.itemLineList,
          itemLineRemovedList: [],
          itemLineAvailableList: data.itemLineList,
          remark: data.remark,
          orderCompletedTimeStamp: data.orderCompletedTimeStamp,
          goodsSupplementOrder: data.goodsSupplementOrder,
        })
      }

      commit('SET_STATUS', 'PENDING')
      return invoiceCenter.invoicesService.queryInvoiceDetail(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_STATUS', 'REJECTED')
            state.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_STATUS', 'FULFILLED')
              if (res?.data?.data) {
                handleData(res?.data?.data, invoiceTitleType)
              }
              state.data = res.data
            } else {
              commit('SET_STATUS', 'REJECTED')
              state.data = res.data
            }

            // mock data
            // state.data = data
            return state.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    getCompanyTaxInfo(context: any, payload: any) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      commit('SET_COMPANY_TAX_INFO_STATUS', 'PENDING')
      return invoiceCenter.invoicesService.getCompanyTaxInfo(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_COMPANY_TAX_INFO_STATUS', 'REJECTED')
            state.companyTaxInfo.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_COMPANY_TAX_INFO_STATUS', 'FULFILLED')
              if (res?.data?.data) {
                // handleData(res?.data?.data)
              }
              state.companyTaxInfo.data = res.data
            } else {
              commit('SET_COMPANY_TAX_INFO_STATUS', 'REJECTED')
              state.companyTaxInfo.data = res.data
            }

            // mock data
            // state.data = data
            return state.companyTaxInfo.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    manualInvoicing(context: any, payload: any) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      commit('SET_MANUAL_INVOICING_STATUS', 'PENDING')
      return invoiceCenter.invoicesService.manualInvoicing(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_MANUAL_INVOICING_STATUS', 'REJECTED')
            state.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_MANUAL_INVOICING_STATUS', 'FULFILLED')
              if (res?.data?.data) {
                // handleData(res?.data?.data)
              }
              state.data = res.data
            } else {
              commit('SET_MANUAL_INVOICING_STATUS', 'REJECTED')
              state.data = res.data
            }

            // mock data
            // state.data = data
            return state.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_MANUAL_INVOICING_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    syncMergeBlueInvoice(context: any, payload: any) {
      const {
        state,
        commit,
      } = context
      const {
        actionState
      } = state

      commit('SET_SYNC_BLUE_INVOICE_STATUS', 'PENDING')
      commit('SET_SYNC_BLUE_INVOICE_PAYLOAD', payload)
      return invoiceCenter.invoicesService.syncMergeBlueInvoice(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_SYNC_BLUE_INVOICE_STATUS', 'REJECTED')
            commit('SET_SYNC_BLUE_INVOICE_PAYLOAD', undefined)
            state.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_SYNC_BLUE_INVOICE_STATUS', 'FULFILLED')
              commit('SET_SYNC_BLUE_INVOICE_PAYLOAD', undefined)
              if (res?.data?.data) {
                // handleData(res?.data?.data)
              }
              state.data = res.data
            } else {
              commit('SET_SYNC_BLUE_INVOICE_STATUS', 'REJECTED')
              commit('SET_SYNC_BLUE_INVOICE_PAYLOAD', undefined)
              state.data = res.data
            }

            // mock data
            // state.data = data
            return state.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_SYNC_BLUE_INVOICE_STATUS', 'REJECTED')
          commit('SET_SYNC_BLUE_INVOICE_PAYLOAD', undefined)
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },

    redraft(context: any, payload: any) {
      const {
        state,
        commit,
      } = context
      const {
        actionState
      } = state

      commit('SET_INVOICE_REDRAFT_STATUS', 'PENDING')
      commit('SET_INVOICE_REDRAFT_PAYLOAD', payload)
      return invoiceCenter.invoicesService.redraft(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_INVOICE_REDRAFT_STATUS', 'REJECTED')
            commit('SET_INVOICE_REDRAFT_PAYLOAD', undefined)
            state.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_INVOICE_REDRAFT_STATUS', 'FULFILLED')
              commit('SET_INVOICE_REDRAFT_PAYLOAD', undefined)
              if (res?.data?.data) {
                // handleData(res?.data?.data)
              }
              state.data = res.data
            } else {
              commit('SET_INVOICE_REDRAFT_STATUS', 'REJECTED')
              commit('SET_INVOICE_REDRAFT_PAYLOAD', undefined)
              state.data = res.data
            }

            // mock data
            // state.data = data
            return state.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_INVOICE_REDRAFT_STATUS', 'REJECTED')
          commit('SET_INVOICE_REDRAFT_PAYLOAD', undefined)
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    cancel(context: any, payload: any) {
      const {
        state,
        commit,
      } = context
      const {
        actionState
      } = state

      commit('SET_INVOICE_CANCEL_STATUS', 'PENDING')
      commit('SET_INVOICE_CANCEL_PAYLOAD', payload)
      return invoiceCenter.invoicesService.cancel(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_INVOICE_CANCEL_STATUS', 'REJECTED')
            commit('SET_INVOICE_CANCEL_PAYLOAD', undefined)
            state.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_INVOICE_CANCEL_STATUS', 'FULFILLED')
              commit('SET_INVOICE_CANCEL_PAYLOAD', undefined)
              if (res?.data?.data) {
                // handleData(res?.data?.data)
              }
              state.data = res.data
            } else {
              commit('SET_INVOICE_CANCEL_STATUS', 'REJECTED')
              commit('SET_INVOICE_CANCEL_PAYLOAD', undefined)
              state.data = res.data
            }

            // mock data
            // state.data = data
            return state.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_INVOICE_CANCEL_STATUS', 'REJECTED')
          commit('SET_INVOICE_CANCEL_PAYLOAD', undefined)
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    queryGoodsSupplementItem(context: any, payload: any) {
      const {
        state,
        commit,
      } = context

      commit('SET_GOODS_SUPPLEMENT_STATUS', 'PENDING')
      return invoiceCenter.invoicesService.queryGoodsSupplementItem(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            message.error(res.message)
            commit('SET_GOODS_SUPPLEMENT_STATUS', 'REJECTED')
            state.goodsSupplement.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_GOODS_SUPPLEMENT_STATUS', 'FULFILLED')
              state.goodsSupplement.data = res.data

              // 如果查询到商品数据，更新到 itemLineSelected
              if (res?.data?.data) {
                const itemLineData = res.data.data
                itemLineData.isGoodsSupplement = true

                // 获取现有的数据
                const currentList = state.manualInvoicing.formState.itemLineAvailableList || []
                const currentSelected = state.manualInvoicing.formState.itemLineSelected
                const currentRemovedList = state.manualInvoicing.formState.itemLineRemovedList || []

                // 检查商品是否已经存在
                const isAlreadySelected = currentSelected && isSameItem(currentSelected, itemLineData)
                const isInAvailableList = isItemInList(itemLineData, currentList)

                if (isAlreadySelected || isInAvailableList) {
                  message.info('该商品已存在，无需重复添加')
                  return res
                }

                // 更新 itemLineRemovedList（添加新查询的商品）
                const updatedRemovedList = [...currentRemovedList, itemLineData]

                // 检查是否需要占位项（基于 itemLineRemovedList 是否有数据）
                const hasSelectedItem = hasPlaceholderItem(currentList)
                const shouldHaveSelectedItem = updatedRemovedList.length > 0

                let updatedList = [...currentList]
                if (shouldHaveSelectedItem && !hasSelectedItem) {
                  // 需要占位项但不存在，添加
                  updatedList.push({ "id": PLACEHOLDER_ITEM_ID })
                } else if (!shouldHaveSelectedItem && hasSelectedItem) {
                  // 不需要占位项但存在，删除
                  updatedList = updatedList.filter((item: any) => item.id !== PLACEHOLDER_ITEM_ID)
                }

                commit('updateInvoiceManualFormState', {
                  itemLineSelected: itemLineData,
                  itemLineAvailableList: updatedList,
                  itemLineRemovedList: updatedRemovedList
                })
                // message.warn('查询成功，已添加商品')
              } else {
                message.error('未找到相关商品')
              }
            } else {
              message.error(`[${res?.data?.code}]${res?.data?.msg}`)
              commit('SET_GOODS_SUPPLEMENT_STATUS', 'REJECTED')
              state.goodsSupplement.data = res.data
            }
            return res
          }
        })
        .catch((err: Error) => {
          console.log(err)
          message.error('系统异常，请稍后再试')
          commit('SET_GOODS_SUPPLEMENT_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('queryGoodsSupplementItem finally.....', state)
        })
    }
  }
}