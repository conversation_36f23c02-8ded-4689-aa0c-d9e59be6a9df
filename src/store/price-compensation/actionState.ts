const getDefaultState = () => {
  return {
    // status: '',
    // action: '',
    type: '',
    record: undefined,
    // formState: {},
    // 
    // edit: {
    //   status: '',
    //   formState: {},
    //   query: {},
    // },
  }
}

export default {
  state: getDefaultState(),
  mutations: {
    updateActionState(state: any, newState: any) {
      state.type = newState.type
      state.record = newState.record
    },
    resetActionState(state: any) {
      const defaultState = getDefaultState()
      state.type = defaultState.type
      state.record = defaultState.record
      // state.edit = defaultState.edit
    }
  },
  actions: {
  }
}