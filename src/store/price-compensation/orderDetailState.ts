import { priceCompensationOrders } from '../../service'
import {
  Query_Price_Compensation_Order_Detail_Req,
  Price_Compare_Req,
} from '@/types/price-compensation'
// import paymentSummaryData from './paymentSummaryData'
import { ENV } from '../../utils'
import pad from 'pad'
import { compact } from 'lodash'
import { message } from 'ant-design-vue-4'

const getDefaultState = () => {
  const state = {
    status: '',
    query: {},
    actionState: undefined,
    compensationApply: {
      status: '',
      formState: {
      },
      data: {
      },
    },
    priceCompare: {
      status: '',
      formState: {
      },
      data: {
      },
      validateResult: null,
    },
    refund: {
      status: '',
      data: {
      }
    },
    updatePriceCompensationRecord: {
      status: '',
      params: {},
      data: {
      }
    },
    history: {
      status: '',
      data: {
      }
    },
    data: {
      code: '',
      data: null,
    }
  }
  return state
}

export default {
  state: getDefaultState(),
  mutations: {
    SET_STATUS(state: { status: string }, status: string): void {
      state.status = status
    },
    SET_ACTION_STATE(state: any, actionState: any) {
      state.actionState = actionState
    },
    SET_COMPENSATION_APPLY_STATUS(state: { compensationApply: { status: string } }, status: string): void {
      state.compensationApply.status = status
    },
    SET_PRICE_COMPARE_STATUS(state: { priceCompare: { status: string } }, status: string): void {
      state.priceCompare.status = status
    },
    SET_REFUND_STATUS(state: { refund: { status: string } }, status: string): void {
      state.refund.status = status
    },
    SET_UPDATE_PRICE_COMPENSATION_RECORD_STATUS(state: { updatePriceCompensationRecord: { status: string } }, status: string): void {
      state.updatePriceCompensationRecord.status = status
    },
    SET_UPDATE_PRICE_COMPENSATION_RECORD_PARAMS(state: { updatePriceCompensationRecord: { params: object } }, params: object): void {
      state.updatePriceCompensationRecord.params = params
    },
    SET_FETCH_COMPENSATION_HISTORY_STATUS(state: { history: { status: string } }, status: string): void {
      state.history.status = status
    },
    updateCompensationApplyFormState(state: any, formState: any) {
      state.compensationApply.formState = { ...state.compensationApply.formState, ...formState }
    },
    updateCompensationPriceCompareFormState(state: any, formState: any) {
      state.priceCompare.formState = { ...state.priceCompare.formState, ...formState }
    },
    resetCompensationApplyFormState: (state: any) => {
      const defaultState = getDefaultState()
      state.compensationApply.status = defaultState.compensationApply.status
      state.compensationApply.formState = defaultState.compensationApply.formState
    },
    resetCompensationPriceCompareFormState: (state: any) => {
      const defaultState = getDefaultState()
      state.priceCompare.status = defaultState.priceCompare.status
      state.priceCompare.formState = defaultState.priceCompare.formState
    },
    resetCompensationPriceCompareData: (state: any) => {
      const defaultState = getDefaultState()
      state.priceCompare.data = defaultState.priceCompare.data
    },
    updateCompensationPriceCompareFormValidateResult(state: any, validateResult: any) {
      state.priceCompare.validateResult = { ...state.priceCompare.validateResult, ...validateResult }
    },
    resetCompensationPriceCompareFormValidateResult: (state: any) => {
      state.priceCompare.validateResult = null
    }
  },
  actions: {
    fetchOrderDetailInfo(context: any, payload: Query_Price_Compensation_Order_Detail_Req) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      const handleData = (data: any) => {  
        // debugger            
        commit('updateCompensationApplyFormState', {
          vid: data.vid,
          contactPhone: data.contactPhone,
          contactName: data.contactName,
          payDueTime: data.payDueTime,
          applyTime: data.applyTime,
          grossAmount: data.grossAmount,
          storeList: data.storeList,
          compareStore: data.compareStore,
        })

        commit('updateCompensationPriceCompareFormState', {
          itemDetails: data.itemDetails || [],
          paymentDetails: data.paymentDetails || [],
          priceCanProtect: data.totalProtectAmount,
          // paymentDetails: data.paymentDetails,
          paymentGatewayReferenceList: data.paymentDetails.map((item: any) => {
            return {
              paymentGatewayReferenceId: item.paymentGatewayReferenceId,
              refundAmount: item.refundAmount,
            }
          }),
        })
      }

      commit('SET_STATUS', 'PENDING')
      return priceCompensationOrders.ordersService.queryOrderDetailInfo(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_STATUS', 'REJECTED')
            state.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_STATUS', 'FULFILLED')
              if (res?.data?.data) {
                handleData(res?.data?.data)
              }
            } else {
              commit('SET_STATUS', 'REJECTED')
            }
            state.data = res.data
            // mock data
            // state.data = data
            return state.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    fetchOrderDetail(context: any, payload: Query_Price_Compensation_Order_Detail_Req) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      const handleData = (data: any) => {                  
        commit('updateCompensationApplyFormState', {
          vid: data.vid,
          contactPhone: data.contactPhone,
          contactName: data.contactName,
          payDueTime: data.payDueTime,
          applyTime: data.applyTime,
          grossAmount: data.grossAmount,
          storeList: data.storeList,
          // paymentGatewayReferenceList:paymentDetails
        })
      }

      commit('SET_STATUS', 'PENDING')
      return priceCompensationOrders.ordersService.queryOrderDetail(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_STATUS', 'REJECTED')
            state.data = res
            return res
          } else {
            if (res?.data?.code === '0') {
              commit('SET_STATUS', 'FULFILLED')
              if (res?.data?.data) {
                handleData(res?.data?.data)
              }
              state.data = res.data
            } else {
              commit('SET_STATUS', 'REJECTED')
              state.data = res.data
            }

            // mock data
            // state.data = data
            return state.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    priceCompare(context: any, payload: Price_Compare_Req) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      const handleData = (data: any) => {              
        commit('updateCompensationPriceCompareFormState', {
          itemDetails: data.itemDetails || [],
          paymentDetails: data.paymentDetails || [],
          priceCanProtect: data.priceCanProtect,
          // paymentDetails: data.paymentDetails,
          paymentGatewayReferenceList: data.paymentDetails.map((item: any) => {
            return {
              paymentGatewayReferenceId: item.paymentGatewayReferenceId,
              refundAmount: item.refundAmount || item.canRefundAmount,
            }
          }),
        })
      }

      commit('SET_PRICE_COMPARE_STATUS', 'PENDING')
      return priceCompensationOrders.ordersService.priceCompare(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_PRICE_COMPARE_STATUS', 'REJECTED')
            state.priceCompare.data = res
            return res
          } else {
            commit('SET_PRICE_COMPARE_STATUS', 'FULFILLED')
            if (res?.data?.data) {
              handleData(res?.data?.data)
            }
            state.priceCompare.data = res.data
            // mock data
            // state.data = data
            return state.priceCompare.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_PRICE_COMPARE_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    refund(context: any, payload: any) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      commit('SET_REFUND_STATUS', 'PENDING')
      return priceCompensationOrders.ordersService.refund(payload)
      .then((res: any) => {
        if (res instanceof Error) {
          commit('SET_REFUND_STATUS', 'REJECTED')
          state.refund.data = res
          return res
        } else {
          commit('SET_REFUND_STATUS', 'FULFILLED')
          if (res?.data?.data) {
            // handleData(res?.data?.data)
          }
          state.refund.data = res.data
          // mock data
          // state.data = data
          return state.refund.data
        }
      })
      .catch((err: Error) => {
        console.log(err)
        commit('SET_REFUND_STATUS', 'REJECTED')
        return err
      })
      .finally(() => {
        // console.log('finally.....', state)
      })
    },
    updatePriceCompensationRecord(context: any, payload: any) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      commit('SET_UPDATE_PRICE_COMPENSATION_RECORD_STATUS', 'PENDING')
      commit('SET_UPDATE_PRICE_COMPENSATION_RECORD_PARAMS', payload)
      
      return priceCompensationOrders.ordersService.update(payload)
      .then((res: any) => {
        commit('SET_UPDATE_PRICE_COMPENSATION_RECORD_PARAMS', null)
        if (res instanceof Error) {
          commit('SET_UPDATE_PRICE_COMPENSATION_RECORD_STATUS', 'REJECTED')
          state.updatePriceCompensationRecord.data = res
          return res
        } else {
          // res.data.status = payload?.status
          // if (res?.data?.code) res.data.status = payload?.status
          commit('SET_UPDATE_PRICE_COMPENSATION_RECORD_STATUS', 'FULFILLED')
          if (res?.data?.data) {
            // handleData(res?.data?.data)
          }
          state.updatePriceCompensationRecord.data = res.data
          // mock data
          // state.data = data
          return state.updatePriceCompensationRecord.data
        }
      })
      .catch((err: Error) => {
        console.log(err)
        commit('SET_UPDATE_PRICE_COMPENSATION_RECORD_PARAMS', null)
        commit('SET_UPDATE_PRICE_COMPENSATION_RECORD_STATUS', 'REJECTED')
        return err
      })
      .finally(() => {
        // console.log('finally.....', state)
      })
    },
    fetchCompensationHistory(context: any, payload: any) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      commit('SET_FETCH_COMPENSATION_HISTORY_STATUS', 'PENDING')
      return priceCompensationOrders.ordersService.queryOrderLists(payload)
        .then((res) => {
          if (res instanceof Error) {
            message.error(res.message)
            commit('SET_FETCH_COMPENSATION_HISTORY_STATUS', 'REJECTED')
          } else {
            if (res?.data?.code === '0') {
              commit('SET_FETCH_COMPENSATION_HISTORY_STATUS', 'FULFILLED')
              state.history.data = res.data
            } else {
              message.error(`[${res?.data?.code}]${res?.data?.msg}`)
              commit('SET_FETCH_COMPENSATION_HISTORY_STATUS', 'REJECTED')
              state.history.data = res.data
            }
          }
          return res
        })
        .catch(err => {
          console.log(err)
          message.error(`系统异常，请稍后再试`)
          commit('SET_FETCH_COMPENSATION_HISTORY_STATUS', 'REJECTED')
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    }
  }
}