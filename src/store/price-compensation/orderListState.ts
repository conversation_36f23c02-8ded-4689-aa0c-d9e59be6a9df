import { priceCompensationOrders } from '../../service'
import { message } from 'ant-design-vue-4'

const getDefaultState = () => {
  const state = {
    status: '',
    query: {},
    formState: {
      curPage: 1,
      pageSize: 10,
      baseSize: 10,
    },
    rules: {},
    selectedRowKeys: [],
    data: {
      code: '',
      data: null,
    }
  }
  return state
}

export default {
  state: getDefaultState(),
  mutations: {
    updateOrderListStateStatus(state: any, status: string) {
      state.status = status
    },
    updateOrderListFormState(state: any, formState: any) {
      state.formState = { ...state.formState, ...formState }
    },
    resetOrderListFormState(state: any) {
      state.formState = getDefaultState().formState
    }
  },
  actions: {
    async fetchOrderList(context: any, payload: any) {
      const {
        state,
        commit,
      } = context

      commit('updateOrderListStateStatus', 'PENDING')
      return priceCompensationOrders.ordersService.queryOrderLists(payload)
        .then((res) => {
          if (res instanceof Error) {
            message.error(res.message)
            commit('updateOrderListStateStatus', 'REJECTED')
          } else {
            if (res?.data?.code === '0') {
              commit('updateOrderListStateStatus', 'FULFILLED')
              state.data = res.data
            } else {
              message.error(`[${res?.data?.code}]${res?.data?.msg}`)
              commit('updateOrderListStateStatus', 'REJECTED')
              state.data = res.data
            }
          }
          return res
        })
        .catch(err => {
          console.log(err)
          message.error(`系统异常，请稍后再试`)
          commit('updateOrderListStateStatus', 'REJECTED')
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    }
  }
}