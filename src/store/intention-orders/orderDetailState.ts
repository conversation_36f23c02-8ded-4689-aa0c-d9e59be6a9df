import { intentionOrders } from '../../service'
import {
  Query_Intention_Order_Detail_Req,
  Distribution_Req,
  CancelOrder_Req,
} from '@/types/intention-orders'
// import paymentSummaryData from './paymentSummaryData'
import { ENV } from '../../utils'
import pad from 'pad'
import { compact } from 'lodash'
import { message } from 'ant-design-vue-4'

const getDefaultState = () => {
  const state = {
    status: '',
    query: {},
    actionState: undefined,
    data: {
      code: '',
      data: null,
    },
    distribution: {
      status: '',
      params: {},
      formState: {
      },
      data: {
      },
    },
    cancel: {
      status: '',
      params: {},
      formState: {
      },
      data: {
      },
    },
    preCreateDA: {
      status: '',
      rules: {},
      formState: {
      },
    },
  }
  return state
}

export default {
  state: getDefaultState(),
  mutations: {
    SET_STATUS(state: { status: string }, status: string): void {
      state.status = status
    },
    SET_PRE_CREATE_DA_STATUS(state: { preCreateDA: { status: string } }, status: string): void {
      state.preCreateDA.status = status
    },
    SET_ACTION_STATE(state: any, actionState: any) {
      state.actionState = actionState
    },
    SET_DISTRIBUTION_STATUS(state: { distribution: { status: string } }, status: string): void {
      state.distribution.status = status
    },
    SET_DISTRIBUTION_PARAMS(state: { distribution: { params: object } }, params: object): void {
      state.distribution.params = params
    },
    SET_CANCELORDER_STATUS(state: { cancel: { status: string } }, status: string): void {
      state.cancel.status = status
    },
    SET_CANCELORDER_PARAMS(state: { cancel: { params: object } }, params: object): void {
      state.cancel.params = params
    },
    updateDistributionFormState(state: any, formState: any) {
      state.distribution.formState = { ...state.distribution.formState, ...formState }
    },
    resetDistributionFormState: (state: any) => {
      const defaultState = getDefaultState()
      state.distribution.status = defaultState.distribution.status
      state.distribution.formState = defaultState.distribution.formState
    },
    updatePreCreateDAFormState(state: any, formState: any) {
      state.preCreateDA.formState = { ...state.preCreateDA.formState, ...formState }
    },
    resetPreCreateDAFormState: (state: any) => {
      const defaultState = getDefaultState()
      state.preCreateDA.status = defaultState.preCreateDA.status
      state.preCreateDA.formState = defaultState.preCreateDA.formState
    },
  },
  actions: {
    fetchOrderDetail(context: any, payload: Query_Intention_Order_Detail_Req) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      commit('SET_STATUS', 'PENDING')
      return intentionOrders.ordersService.queryOrderDetail(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_STATUS', 'REJECTED')
            state.data = res
            return res
          } else {
            commit('SET_STATUS', 'FULFILLED')
            // if (res?.data?.data) {
            //   handleData(res?.data?.data)
            // }
            state.data = res.data
            // mock data
            // state.data = data
            return state.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    distribution(context: any, payload: Distribution_Req) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      commit('SET_DISTRIBUTION_STATUS', 'PENDING')
      commit('SET_DISTRIBUTION_PARAMS', payload)
      return intentionOrders.ordersService.distribution(payload)
        .then((res: any) => {
          commit('SET_DISTRIBUTION_PARAMS', null)
          if (res instanceof Error) {
            commit('SET_DISTRIBUTION_STATUS', 'REJECTED')
            state.distribution.data = res
            return res
          } else {
            commit('SET_DISTRIBUTION_STATUS', 'FULFILLED')
            if (res?.data?.data) {
              // handleData(res?.data?.data)
            }
            state.distribution.data = res.data
            // mock data
            // state.data = data
            return state.distribution.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_DISTRIBUTION_PARAMS', null)
          commit('SET_DISTRIBUTION_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    cancelOrder(context: any, payload: CancelOrder_Req) {
      const {
        state,
        commit,
      } = context

      const {
        actionState
      } = state

      commit('SET_CANCELORDER_STATUS', 'PENDING')
      commit('SET_CANCELORDER_PARAMS', payload)
      return intentionOrders.ordersService.cancelOrder(payload)
        .then((res: any) => {
          commit('SET_CANCELORDER_PARAMS', null)
          if (res instanceof Error) {
            commit('SET_CANCELORDER_STATUS', 'REJECTED')
            state.distribution.data = res
            return res
          } else {
            commit('SET_CANCELORDER_STATUS', 'FULFILLED')
            if (res?.data?.data) {
              // handleData(res?.data?.data)
            }
            state.distribution.data = res.data
            // mock data
            // state.data = data
            return state.distribution.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_CANCELORDER_PARAMS', null)
          commit('SET_CANCELORDER_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    },
    fetchPreCreateDeliveryArrangement(context: any, payload: any) {
      const {
        state,
        commit,
      } = context
      // debugger

      const {
        actionState
      } = state

      // debugger

      const handleData = (data: any) => {
        commit('updatePreCreateDAFormState', {
          deliveryDetail: data?.deliveryDetail,
          solutionId: data?.solutionId,
          deliveryArrangementId: data?.deliveryArrangementId,
          deliveryMessage: data?.deliveryMessage,
        })
      }

      commit('SET_PRE_CREATE_DA_STATUS', 'PENDING')
      return intentionOrders.ordersService.fetchPreCreateDeliveryArrangement(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            commit('SET_PRE_CREATE_DA_STATUS', 'REJECTED')
            state.preCreateDA.data = res
            return res
          } else {
            commit('SET_PRE_CREATE_DA_STATUS', 'FULFILLED')
            if (res?.data?.data) {
              handleData(res?.data?.data)
            }
            state.preCreateDA.data = res.data
            // mock data
            // state.data = data
            return state.preCreateDA.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          commit('SET_PRE_CREATE_DA_STATUS', 'REJECTED')
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    }
  }
}