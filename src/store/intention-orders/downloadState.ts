import { intentionOrders } from '../../service'
import { Download_Req } from '@/types/intention-orders'

const getDefaultState = () => {
  const state = {
    status: '',
    query: {},
    formState: {
      page: 1,
      size: 10,
    },
    data: {
      code: '',
      data: null,
    }
  }
  return state
}

export default {
  state: getDefaultState(),
  mutations: {
  },
  actions: {
    download(context: any, payload: Download_Req) {
      const {
        state
      } = context
      
      state.status = 'PENDING'
      return intentionOrders.ordersService.download(payload)
        .then((res) => {
          // console.log(res)
          state.status = 'FULFILLED'
          state.data = res.data
          // mock data
          // state.data = data
          return res
        })
        .catch(err => {
          console.log(err)
          state.status = 'REJECTED'
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    }
  }
}