import { oms } from '../../service'

const getDefaultState = () => {
  const state = {
    download: {
      status: '',
      query: {
      },
      data: {
        code: '',
        data: null,
      }
    },
  }
  return state
}

export default {
  state: getDefaultState(),
  mutations: {
  },
  actions: {
    downloadStoreSubsidyOrders(context: any, payload: any) {
      const {
        state
      } = context

      state.download.status = 'PENDING'

      return oms.downloadCenterService.downloadStoreSubsidyOrders(payload)
        .then((res: any) => {
          if (res instanceof Error) {
            state.download.status = 'REJECTED'
            return res
          } else {
            if (res.data.code === '0') {
              state.download.status = 'FULFILLED'
            } else {
              state.download.status = 'REJECTED'
            }
            state.download.data = res.data
            return res.data
          }
        })
        .catch((err: Error) => {
          console.log(err)
          state.download.status = 'REJECTED'
          return err
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    }
  }
}