const getDefaultState = () => {
  const state = {
    storeInvoiceUpload: {
      res: {},
    }
  }
  return state
}

export default {
  state: getDefaultState(),
  mutations: {
    updateStoreInvoiceUploadRes(state: any, res: any) {
      state.storeInvoiceUpload.res = { ...state.storeInvoiceUpload.res, ...res}
    },
    resetStoreInvoiceUploadRes(state: any) {
      state.storeInvoiceUpload.res = {}
    }
  },
  actions: {
  }
}