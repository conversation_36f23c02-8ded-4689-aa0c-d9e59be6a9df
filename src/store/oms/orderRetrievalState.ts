import { oms } from '../../service'

const getDefaultState = () => {
  return {
    status: '',
    query: {},
    data: {}
  }
}

export default {
  state: getDefaultState(),
  mutations: {
    resetRetrievalDataState(state: any) {
      const defaultState = getDefaultState()
      state.status = defaultState.status
      state.query = defaultState.query
      state.data = defaultState.data
    }
  },
  actions: {
    getRetrievalData(context: any, payload: any) {
      const {
        state
      } = context
      
      state.status = 'PENDING'

      return oms.ordersService.getRetrievalData(payload?.vid)
        .then((res) => {
          state.status = 'FULFILLED'
          state.data = res.data
          // state.data.options = res?.data?.data?.result?.map((item: any) => { 
          //   return {
          //     value: item
          //   }
          // })
          return res
        })
        .catch(err => {
          console.log(err)
          state.status = 'REJECTED'
        })
        .finally(() => {
          // console.log('finally.....', state)
        })
    }
  }
}