<template>
  <a4-table
    size="small"
    :dataSource="items || []"
    :columns="columns"
    :pagination="false"
  >
    <template #headerCell="{ column }">
      <span style="white-space: nowrap;">{{ column.title }}</span>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'itemPicture'">
        <a4-image v-if="record.itemPicture" :src="record.itemPicture" style="width: 48px; height: 48px;" />
      </template>
      <template v-if="column.key === 'salesPrice'">
        <ik-price v-if="record.salesPrice" :price="record.salesPrice" />
      </template>
      <template v-if="column.key === 'netAmount'">
        <ik-price v-if="record.netAmount" :price="record.netAmount" />
      </template>
    </template>
  </a4-table>
</template>

<script lang="ts">
import { defineComponent, ref, UnwrapRef, reactive, watch, onMounted, computed, toRaw } from 'vue'
import { useStore } from 'vuex'
import {  
  Table as A4Table,
  Image as A4Image,
} from 'ant-design-vue-4';
import IkPrice from '@/stories/components/Price/index.vue'
import columns from './columns';

export default {
  props: ['items'],
  components: {
    A4Table,
    A4Image,
    IkPrice,
  },
  setup(props) {
    return {
      columns,
    }
  }
}
</script>

<style scoped lang="scss">

</style>
