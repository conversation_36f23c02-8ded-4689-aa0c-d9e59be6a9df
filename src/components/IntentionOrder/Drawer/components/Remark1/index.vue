<template>
  <a4-form-item
    label="终端用户公司"
  >
    {{ actionState?.record?.remark1 || '--' }}
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref, UnwrapRef, watch, computed, toRaw } from 'vue'
import { useStore } from 'vuex'
import {  
  Table as A4Table,
  Image as A4Image,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';
import IkPrice from '@/stories/components/Price/index.vue'

export default {
  props: ['items', 'actionState'],
  components: {
    A4FormItem,
  },
  setup(props) {
    return {
    }
  }
}
</script>

<style scoped lang="scss">

</style>
