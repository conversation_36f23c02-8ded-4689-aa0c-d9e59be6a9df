<template>
  <a4-drawer
    width="720"
    placement="right"
    :closable="true"
    :open="open"
    title="订单详情"
    :destroyOnClose="true"
    @afterOpenChange="handleOpenChange"
    @close="handleClose"
  >
    <a4-spin :spinning="orderDetailState.status === 'PENDING'">
      <!-- {{ orderDetail }} -->
      <a4-result
        v-if="
          orderDetailState.status === 'REJECTED' &&
          orderDetailState?.data?.code !== '0'
        "
        status="error"
        title="系统异常"
        sub-title="订单信息读取失败"
      />

      <a4-form class="intention-order-detail-form" v-else>
        <h3>基本信息</h3>
        <a4-row>
          <a4-col :span="12">
            <a4-form-item
              label="订单号"
              v-if="['DONE'].includes(orderDetail?.orderStatus)"
            >
              {{ orderDetail?.orderNO }}
            </a4-form-item>

            <a4-form-item
              label="意向订单号"
              v-else
            >
              {{ orderDetail?.orderNO }}
            </a4-form-item>
          </a4-col>
          <a4-col :span="12">
            <a4-form-item
              label="订单状态"
            >
              <a4-tag :class="`order-status order-status--${orderDetail?.orderStatus?.toLowerCase()}`">
                {{
                  INTENTION_ORDER_STATUS[orderDetail?.orderStatus]
                    || orderDetail?.orderStatus
                }}
              </a4-tag>
            </a4-form-item>
          </a4-col>
        </a4-row>
        <a4-row>
          <a4-col :span="12">
            <a4-form-item
              label="下单时间"
            >
              {{ orderDetail?.applyTime }}
            </a4-form-item>
          </a4-col>
          <a4-col :span="12">
            <a4-form-item
              label="会员号"
            >
              {{ orderDetail?.deliveryContactInfo?.personalInfo?.familyNO || '--' }}
            </a4-form-item>
          </a4-col>
        </a4-row>
        <a4-divider />

        <template v-if="['CLICK_COLLECT_STORE'].indexOf(serviceType) > -1">
          <h3>自提信息</h3>
          <a4-row>
            <a4-col :span="12">
              <a4-form-item
                label="公司名称"
              >
                {{ orderDetail?.deliveryContactInfo?.personalInfo?.firstName || '--' }}
              </a4-form-item>
            </a4-col>
            <a4-col :span="12">
              <a4-form-item
                label="联系电话"
              >
                {{ orderDetail?.deliveryContactInfo?.personalInfo?.mobile || '--' }}
              </a4-form-item>
            </a4-col>
          </a4-row>
          <a4-row>
            <a4-col :span="12">
              <remark1 :actionState="actionState" />
            </a4-col>
            <a4-col :span="12">
              <remark2 :actionState="actionState" />
            </a4-col>
          </a4-row>
          <a4-divider />
        </template>
        <template v-else-if="['HOME_DELIVERY'].indexOf(serviceType) > -1">
          <h3>收货人信息</h3>
          <a4-row>
            <a4-col :span="12">
              <a4-form-item
                label="物流单号"
              >
                {{ shipmentIds }}
              </a4-form-item>
            </a4-col>
            <a4-col :span="12">
              <a4-form-item
                label="物流公司"
              >
                {{ carrierName }}
              </a4-form-item>
            </a4-col>
          </a4-row>
          <a4-row>
            <a4-col :span="12">
              <a4-form-item
                label="收货人"
              >
                {{ orderDetail?.deliveryContactInfo?.personalInfo?.firstName }}
              </a4-form-item>
            </a4-col>
            <a4-col :span="12">
              <a4-form-item
                label="联系电话"
              >
                {{ orderDetail?.deliveryContactInfo?.personalInfo?.mobile || '--' }}
              </a4-form-item>
            </a4-col>
          </a4-row>
          <a4-row>
            <a4-col :span="12">
              <a4-form-item
                label="收货地址"
              >
                {{ 
                  compact([ orderDetail?.deliveryContactInfo?.deliveryAddressDetails?.addressLine1,
                    orderDetail?.deliveryContactInfo?.deliveryAddressDetails?.addressLine2,
                    orderDetail?.deliveryContactInfo?.deliveryAddressDetails?.addressLine3,
                  ]).join('')
                }}
              </a4-form-item>
            </a4-col>
            <a4-col :span="12">
              <remark1 :actionState="actionState" />
            </a4-col>
          </a4-row>
          <a4-row>
            <a4-col :span="12">
              <remark2 :actionState="actionState" />
            </a4-col>
          </a4-row>
          <a4-divider />
        </template>

        <h3>订单金额</h3>
        <a4-row v-if="orderDetail?.orderType === 'INTENTION'">
          <a4-col :span="24">
            <a4-form-item
              label="订单金额"
            >
              <a4-tag color="orange">商品总额、优惠、实付以订单创建后实际结算为准</a4-tag>
            </a4-form-item>
          </a4-col>
        </a4-row>
        <a4-row v-else-if="orderDetail?.orderType === 'OFFLINE'">
          <a4-col :span="12">
            <a4-form-item
              label="商品总额"
            >
              <ik-price
                v-if="orderDetail?.priceInfo?.goodsAmount"
                :price="orderDetail?.priceInfo?.goodsAmount"
              />
            </a4-form-item>
          </a4-col>
          <a4-col :span="12">
            <a4-form-item
              label="优惠总计"
            >
              <ik-price
                v-if="orderDetail?.priceInfo?.totalDiscounts"
                :price="orderDetail?.priceInfo?.totalDiscounts"
              />
            </a4-form-item>
          </a4-col>
          <a4-col :span="12">
            <a4-form-item
              label="运费和提货费"
            >
            <ik-price
              v-if="orderDetail?.priceInfo?.deliveryAmount"
              :price="orderDetail?.priceInfo?.deliveryAmount"
            />
            </a4-form-item>
          </a4-col>
          <a4-col :span="12">
            <a4-form-item
              label="实付"
            >
            <ik-price
              v-if="orderDetail?.priceInfo?.totalAmount"
              :price="orderDetail?.priceInfo?.totalAmount"
            />
            </a4-form-item>
          </a4-col>
        </a4-row>

        <a4-divider />
        <h3>商品信息</h3>
        <a4-row>
          <a4-col :span="24">
            <!-- {{ orderDetail?.itemInfos }} -->
            <prod-items :items="orderDetail?.itemInfos" />
          </a4-col>
        </a4-row>

      </a4-form>
    </a4-spin>
  </a4-drawer>
</template>

<script lang="ts">
import { compact } from 'lodash'
import { defineComponent, ref, UnwrapRef, reactive, watch, onMounted, computed, toRaw } from 'vue'
import { useStore } from 'vuex'
import { useInit } from '@/composables/intention-orders/order-detail/useInit';

import {  
  Tabs as A4Tabs,
  TabPane as A4TabPane,
  Drawer as A4Drawer,
  // Modal as A4Modal,
  Form as A4Form,
  FormItem as A4FormItem,
  Divider as A4Divider,
  Row as A4Row,
  Col as A4Col,
  Flex as A4Flex,
  Spin as A4Spin,
  Result as A4Result,
  Tag as A4Tag,
} from 'ant-design-vue-4';
import IkPrice from '@/stories/components/Price/index.vue'
import { INTENTION_ORDER_STATUS } from '@/enums/index';
import ProdItems from './components/ProdItems/index.vue'
import Remark1 from './components/Remark1/index.vue'
import Remark2 from './components/Remark2/index.vue'

// import ApplyForm from './ApplyForm/index.vue'
// import CompensationHistory from './CompensationHistory/index.vue'
// import PriceCompensationApplyFormActions from './components/FormActions/index.vue'

export default defineComponent({
  // props: ['open'],
  components: {
    A4Tabs,
    A4TabPane,
    A4Drawer,
    A4Form,
    A4FormItem,
    A4Divider,
    A4Row,
    A4Col,
    A4Spin,
    A4Flex,
    A4Tag,
    A4Result,
    IkPrice,
    ProdItems,
    Remark1,
    Remark2,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const {
      open,
      record,
      handleOpenChange,
      handleClose,
      orderDetailState,
      orderDetail,
      actionState,
      serviceType,
    } = useInit(props)

    const shipmentIds = computed(() => {
      return orderDetail.value?.shipmentIds?.length
        ? orderDetail.value?.shipmentIds?.join('、')
        : '暂无'
    })

    const carrierName = computed(() => {
      return orderDetail.value?.carrierName || '暂无'
    })

    return {
      compact,
      open,
      record,
      handleOpenChange,
      handleClose,
      orderDetailState,
      orderDetail,
      INTENTION_ORDER_STATUS,
      actionState,
      serviceType,
      shipmentIds,
      carrierName,
    }
  }
})
</script>

<style lang="scss" scoped>
.intention-order-detail-form {
  h3 {
    font-size: 1rem;
  }

  :deep(.ant-form-item) {
    margin-bottom: 0;
  }
  :deep(.ant-form-item-control-input) {
    // display: none;
    // border: 1px solid #000 !important;
  }
}

.order-status {
  &--init {
    color: #fff;
    border: 1px solid #f50;
    background-color: #f50;
  }
  &--delivered, &--processing, &--done, &--completed {
    color: #fff;
    border: 1px solid #87d068;
    background-color: #87d068;
  }
  &--failed {
    color: #fff;
    border: 1px solid #f00;
    background-color: #f00;
  }
}
</style>
