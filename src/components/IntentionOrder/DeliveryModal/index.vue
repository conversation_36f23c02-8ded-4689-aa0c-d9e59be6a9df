<template>
  <a4-modal
    title="确认配送信息"
    class="a4-modal"
    size="small"
    :open="show"
    :width="600"
    :destroyOnClose="true"
    :bodyStyle="{ margin:0, padding:0 }"
    :tarBarGutter="0"
    @cancel="handleCancel"
  >
    <template #footer>
      <delivery-confirm-form-actions
        ref="formActionsRef"
        :actionState="actionState"
      />
    </template>
    <div class="form-wrapper">
      <a4-spin :spinning="orderDetailState?.preCreateDA?.status === 'PENDING'">
        <delivery-confirm-form
          ref="formViewRef"
        />
      </a4-spin>
    </div>
  </a4-modal>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, watch } from 'vue'
import { useStore } from 'vuex'

import {  
  Modal as A4Modal,
  Form as A4Form,
  FormItem as A4FormItem,
  Spin as A4Spin,
  message,
} from 'ant-design-vue-4';

import { useInit } from '@/composables/intention-orders/delivery-confirm/useInit';

import DeliveryConfirmForm from './DeliveryConfirmForm/index.vue'
import DeliveryConfirmFormActions from './components/FormActions/index.vue'

export default defineComponent({
  components: {
    A4Modal,
    A4Form,
    A4FormItem,
    A4Spin,
    DeliveryConfirmForm,
    DeliveryConfirmFormActions,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const {
      handleCancel
    } = useInit()

    const store = useStore();
    const {
      actionState,
      orderDetailState,
    } = store.state.intentionOrders;

    const preCreateDAData = computed(() => {
      return orderDetailState?.preCreateDA?.data?.data || {}
    })

    watch(() => props.show, async (_show) => {
      if (_show) {
        store.commit('intentionOrders/SET_ACTION_STATE', actionState)
        const params = {
          orderNo: actionState?.record?.orderNo,
        }
        await store.dispatch('intentionOrders/fetchPreCreateDeliveryArrangement', params)
          .then((detail) => {
            console.log('detail:', detail)
            if (detail instanceof Error) {
              message.error(detail?.response?.data?.msg || detail?.response?.data)
            }
          })
      }
    })

    return {
      actionState,
      orderDetailState,
      preCreateDAData,
      handleCancel,
    }
  },
})
</script>