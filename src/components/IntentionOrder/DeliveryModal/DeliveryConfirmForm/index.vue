<template>
  <a4-form
    ref="formRef"
    autocomplete="off"
    class="delivery-confirm-form"
    :model="orderDetailState?.preCreateDA?.formState"
    :rules="orderDetailState?.preCreateDA?.rules"
  >
    <div style="margin: 1rem;">
      <a4-form-item
        label="意向单号"
        ref="orderNo"
        name="orderNo"
      >
        {{ actionState?.record?.orderNo || '--' }}
      </a4-form-item>

      <delivery-info />
    </div>
  </a4-form>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed } from 'vue'
import { useStore } from 'vuex'

import {  
  Modal as A4Modal,
  Form as A4Form,
  FormItem as A4FormItem,
  Input as A4Input,
} from 'ant-design-vue-4';

import { INTENTION_ORDER_TRANSPORT_METHOD } from '@/enums'

import { useForm } from '@/composables/intention-orders/delivery-confirm/useForm';

import DeliveryInfo from '../components/DeliveryInfo/index.vue'

export default defineComponent({
  components: {
    A4Modal,
    A4Form,
    A4FormItem,
    A4Input,
    DeliveryInfo,
  },
  setup() {
    const store = useStore();

    const {
      formRef
    } = useForm()

    const {
      actionState,
      orderDetailState,
    } = store.state.intentionOrders;

    const preCreateDAFormState = computed(() => {
      return orderDetailState?.preCreateDA?.formState
    })

    return {
      formRef,
      preCreateDAFormState,
      actionState,
      orderDetailState,
      INTENTION_ORDER_TRANSPORT_METHOD,
    }
  },
})
</script>

<style lang="scss" scoped>
.delivery-confirm-form {
  :deep(.ant-form-item) {
    // padding: 0 12px;
    margin-bottom: 8px;
  }
}
</style>