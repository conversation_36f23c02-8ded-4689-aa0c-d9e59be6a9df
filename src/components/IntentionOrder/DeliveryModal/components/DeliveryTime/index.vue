<template>
  <!-- {{ timeWindowId }} -->
  <a4-form-item
    label="送达时间"
    v-if="transportMethod === 'TRUCK'"
    :ref="`timeWindow-of-${deliveryId}`"
    :name="`timeWindow-of-${deliveryId}`"
  >
    <a4-form-item-rest>
      <a4-select
        size="small"
        style="width: 160px; margin-right:8px;"
        :options="dateWindowOptions"
        @change="handleDateWindowChange"
        allowClear
        placeholder="请选择日期"
        v-model:value="dateWindow[deliveryId]"
      />

      <a4-select
        size="small"
        style="width: 180px;"
        :options="timeWindowOptions"
        @change="handleTimeWindowChange"
        v-if="dateWindow[deliveryId]"
        allowClear
        placeholder="请选择时间"
        v-model:value="timeWindowId[deliveryId]"
      />
    </a4-form-item-rest>

    <a4-input
      size="small"
      type="hidden"
      style="margin-top: 8px;"
      :value="timeWindowId[deliveryId]"
    ></a4-input>
  </a4-form-item>

  <a4-form-item
    label="送达时间"
    :ref="`timeWindow-of-${deliveryId}`"
    :name="`timeWindow-of-${deliveryId}`"
    v-if="transportMethod === 'PARCEL'"
  >
    <a4-form-item-rest>
      {{ deliveryTimeForParcel }}
    </a4-form-item-rest>
  </a4-form-item>
</template>

<script lang="ts">
import { find, keys, values, each } from 'lodash'
import dayjs from 'dayjs'
import { defineComponent, computed, onMounted } from 'vue'
import { useStore } from 'vuex'

import {  
  Form as A4Form,
  FormItem as A4FormItem,
  FormItemRest as A4FormItemRest,
  Select as A4Select,
  Input as A4Input,
} from 'ant-design-vue-4';

import localeData from 'dayjs/plugin/localeData'
dayjs.locale('zh-cn')
dayjs.extend(localeData)

export default defineComponent({
  props: ['deliveryId'],
  components: {
    // A4Modal,
    A4Form,
    A4FormItem,
    A4FormItemRest,
    A4Select,
    A4Input,
  },
  setup(props) {
    const store = useStore();

    const {
      actionState,
      orderDetailState,
    } = store.state.intentionOrders;

    const preCreateDAFormState = computed(() => {
      return orderDetailState?.preCreateDA?.formState
    })

    const delivery = computed(() => {
      return preCreateDAFormState.value?.deliveryDetail[props.deliveryId]
    })

    const transportMethod = computed(() => {
      return delivery.value?.transportMethod
    })

    const dateWindow = computed(() => {
      const win = preCreateDAFormState.value?.dateWindow || {}
      // return win[props.deliveryId]
      return win
    })

    const timeWindowId = computed(() => {
      const win = preCreateDAFormState.value?.timeWindowId || {}
      // return win[props.deliveryId]
      return win
    })

    const dateWindowOptions = computed(() => {
      return keys(delivery.value?.timeWindows).map((win) => {
        return {
          value: win,
          label: `${ dayjs().localeData().weekdays(dayjs(win)) } ${dayjs(win).format('MM月DD日')}`
        }
      })
    })

    const timeWindowOptions = computed(() => {
      if (dateWindow.value && dateWindow.value[props.deliveryId]) {
        const dateWin = delivery.value?.timeWindows[dateWindow.value[props.deliveryId]]
        const ret: any[] = []
        each(dateWin, (win) => {
          ret.push({
            value: win.timeWindowId,
            label: `${win.from} - ${win.to}`,
          })
        })
        return ret
      }
    })

    const deliveryTimeForParcel = computed(() => {
      if (transportMethod.value === 'PARCEL') {
        const [ dateWin ] = keys(delivery.value?.timeWindows)
        const [ [ timeWin ] ] = values(delivery.value?.timeWindows)
        return `预计${dayjs().localeData().weekdays(dayjs(dateWin))} ${dayjs(dateWin).format('MM月DD日')} ${timeWin.from} - ${timeWin.to}`
      }
    })

    const handleDateWindowChange = (dw) => {
      const _dateWindow = dateWindow.value || {}
      const _timeWindowId = timeWindowId.value || {}
      _dateWindow[props.deliveryId] = dw
      _timeWindowId[props.deliveryId] = undefined
      store.commit('intentionOrders/updatePreCreateDAFormState', {
        dateWindow: _dateWindow,
        timeWindowId: _timeWindowId,
      })
    }

    const handleTimeWindowChange = (twId) => {
      const _timeWindowId = timeWindowId.value || {}
      _timeWindowId[props.deliveryId] = twId

      store.commit('intentionOrders/updatePreCreateDAFormState', {
        timeWindowId: _timeWindowId,
      })
    }

    const isValidTimeWin = (e, v) => {
      if (transportMethod.value === 'TRUCK') {
        if (!timeWindowId.value[props.deliveryId]) {
          return Promise.reject('请选择送达时间')
        } else {
          return Promise.resolve()
        }
      } else {
        return Promise.resolve()
      }
    }

    onMounted(() => {
      //  { validator: isValidRefundAmount, trigger: ['blur', 'change'] }
      orderDetailState.preCreateDA.rules[`timeWindow-of-${props.deliveryId}`] = [
        // { required: true, message: '请选择送达时间' },
        { validator: isValidTimeWin, trigger: ['blur', 'change'] }
      ]
    })

    return {
      delivery,
      transportMethod,
      actionState,
      orderDetailState,
      preCreateDAFormState,
      dateWindow,
      timeWindowId,
      deliveryTimeForParcel,
      dateWindowOptions,
      timeWindowOptions,
      handleDateWindowChange,
      handleTimeWindowChange,
    }
  },
})
</script>