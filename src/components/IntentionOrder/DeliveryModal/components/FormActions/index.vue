<template>
  <a4-button
    size="small"
    @click="handleCancel"
    :disabled="
      preCreateDA?.status === 'PENDING'
      || distribution?.status === 'PENDING'
    "
  >取消</a4-button>

  <a4-button
    size="small"
    type="primary"
    @click="handleSubmit"
    :disabled="
      preCreateDA?.status === 'PENDING'
      || preCreateDA?.status === 'REJECTED'
      || distribution?.status === 'PENDING'
    "
    :loading="
      distribution?.status === 'PENDING'
    "
  >下单</a4-button>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'

import {  
  Button as A4Button,
} from 'ant-design-vue-4';

export default defineComponent({
  components: {
    A4Button,
  },
  props: {
    actionState: {
      type: Object,
    }
  },
  setup(props) {
    const store = useStore();

    const {
      orderDetailState,
      actionState,
    } = store.state.intentionOrders;

    const preCreateDA = computed(() => {
      return orderDetailState?.preCreateDA
    })

    const distribution = computed(() => {
      return orderDetailState?.distribution
    })

    const handleCancel = () => {
      // store.commit('intentionOrders/resetActionState')
      eventBus.emit('intention-order:delivery-confirm-form:cancel-form')
    }

    const handleSubmit = () => {
      // debugger
      eventBus.emit('intention-order:delivery-confirm-form:submit-form');
    }

    return {
      preCreateDA,
      distribution,
      actionState,
      orderDetailState,
      handleCancel,
      handleSubmit,
    }
  }
})
</script>