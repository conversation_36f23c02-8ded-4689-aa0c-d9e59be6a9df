<template>
  <template v-for="delivery, key in deliveryDetail" v-bind:key="key">
    <a4-card
      size="small"
      :bordered="true"
      class="delivery-info-card"
    >
      <a4-form-item label="配送类型">
        {{
          INTENTION_ORDER_TRANSPORT_METHOD[delivery?.transportMethod]
            || delivery?.transportMethod
            || '--'
        }}
      </a4-form-item>

      <a4-form-item label="包含商品">
        {{ delivery.itemCodes?.join(', ') }}
      </a4-form-item>

      <delivery-time :deliveryId="key" />

      <a4-alert
        style="margin-top: 12px;"
        show-icon
        type="warning"
        v-if="
          preCreateDAFormState?.deliveryMessage
          && delivery?.transportMethod === 'TRUCK'
        "
      >
        <template #message>
          <span style="color: #ff0000;">
            {{ preCreateDAFormState?.deliveryMessage }}
          </span>
        </template>
      </a4-alert>

    </a4-card>
  </template>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'

import {
  Card as A4Card,
  Alert as A4Alert,
  FormItem as A4FormItem,
} from 'ant-design-vue-4';

import { INTENTION_ORDER_TRANSPORT_METHOD } from '@/enums'
import DeliveryTime from '../DeliveryTime/index.vue'

export default defineComponent({
  components: {
    A4FormItem,
    A4Card,
    A4Alert,
    DeliveryTime,
  },
  setup() {
    const store = useStore();

    const {
      orderDetailState,
    } = store.state.intentionOrders;

    const preCreateDAFormState = computed(() => {
      return orderDetailState?.preCreateDA?.formState || {}
    })

    const deliveryDetail = computed(() => {
      return preCreateDAFormState.value?.deliveryDetail || {}
    })

    return {
      preCreateDAFormState,
      deliveryDetail,
      INTENTION_ORDER_TRANSPORT_METHOD,
    }
  },
})
</script>

<style lang="scss" scoped>
.delivery-info-card {
  margin-bottom: 12px;
}
</style>


