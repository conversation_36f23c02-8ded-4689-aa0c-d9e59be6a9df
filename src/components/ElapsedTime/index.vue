<template>
  <span>{{ displayText }}</span>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'

const props = defineProps({
  startTimestamp: {
    type: [Number, String],
    required: true
  }
})

const now = ref(Date.now())
let timer = null

onMounted(() => {
  timer = setInterval(() => {
    now.value = Date.now()
  }, 60 * 1000)
})

onBeforeUnmount(() => {
  clearInterval(timer)
})

const normalizedStart = computed(() => {
  const ts = Number(props.startTimestamp)
  return ts < 1e12 ? ts * 1000 : ts
})

const elapsedMs = computed(() => now.value - normalizedStart.value)

function padZero(num) {
  return String(num).padStart(2, '0')
}

const displayText = computed(() => {
  if (elapsedMs.value < 0) return '刚刚'

  const totalMinutes = Math.floor(elapsedMs.value / 60000)
  const totalHours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60

  if (totalHours >= 48) {
    const days = Math.floor(totalHours / 24)
    const hours = totalHours % 24
    return `${padZero(days)}天${padZero(hours)}时${padZero(minutes)}分`
  }

  return `${padZero(totalHours)}时${padZero(minutes)}分`
})

watch(() => props.startTimestamp, () => {
  now.value = Date.now()
})
</script>