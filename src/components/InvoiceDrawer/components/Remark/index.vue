<template>
  <a4-form-item
    class="form-item"
    label="备注"
    ref="remark"
    name="remark"
    style="align-items: baseline;"
    :labelCol="{ span: 24 }"
  >
    <div class="form-item-line">
      <a4-textarea
        size="small"
        style="width: 640px;"
        placeholder="请输入备注，不超过500字"
        :auto-size="{ minRows: 3, maxRows: 5 }"
        @change="handleRemarkChange"
        v-model:value="remark"
      />
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Radio as A4Radio,
  RadioGroup as A4RadioGroup,
  Checkbox as A4Checkbox,
  Textarea as A4Textarea,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Radio,
    A4RadioGroup,
    A4Checkbox,
    A4Textarea,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const remark = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.remark
    })

    const handleRemarkChange = (e) => {
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        remark: e?.target?.value?.trim()
      })
    }

    return {
      remark,
      handleRemarkChange,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    // border: 1px solid #000;
    margin-bottom: 0;
    align-items: initial;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-label) {
      padding-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      // display: none;
    }
  }
}
</style>