<template>
  <a4-form-item
    class="form-item"
    label="抬头"
    ref="buyerName"
    name="buyerName"
    :labelCol="{ span: 6 }"
    style="align-items: baseline; padding: 0 1rem 0 0;"
  >
    <div class="form-item-line">
      <a4-auto-complete
        allowClear
        size="small"
        :options="options"
        v-model:value="buyerName"
        @change="handleChange"
        @select="handleSelect"
        @clear="handleClear"
        :filter-option="filterOption"
      />
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Input as A4Input,
  AutoComplete as A4AutoComplete,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Input,
    A4AutoComplete,
  },
  setup(props) {
    const options = ref([])
    const store = useStore()

    const {
      invoiceDetailState,
    } = store.state.invoiceCenter

    const buyerName = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.buyerName?.trim()
    })

    const invoiceTitleType = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.invoiceTitleType;
    })

    const filterOption = (input, option) => {
      return option.value.toUpperCase().indexOf(input.toUpperCase()) >= 0;
    };

    const handleChange = (v) => {
      if (invoiceTitleType.value === 'ENTERPRISE') {
        store.commit('invoiceCenter/updateInvoiceManualFormState', {
          buyerName: v?.trim(),
        })
        store.dispatch('invoiceCenter/getCompanyTaxInfo', {
          companyName: encodeURIComponent(v?.trim()),
        }).then(res => {
          if (res?.code === '0') {
            if (res?.data?.length) {
              options.value = res?.data.map(item => {
                return {
                  value: item.companyName,
                }
              })
            }
          }
        }).catch(err => {
          console.error(err)
        })
      } else {
        store.commit('invoiceCenter/updateInvoiceManualFormState', {
          buyerName: v?.trim(),
        })
      }
    }

    const handleSelect = async (text) => {
      await store.dispatch('invoiceCenter/getCompanyTaxInfo', {
        companyName: encodeURIComponent(text?.trim()),
      }).then(res => {
        if (res?.code === '0') {
          if (res?.data?.length) {
            const data = res?.data[0] || {}
            store.commit('invoiceCenter/updateInvoiceManualFormState', {
              buyerName: data?.companyName,
              buyerTaxNo: data?.companyTaxCode,
              buyerAddress: data?.registerAddress,
              contactPhone: data?.phone,
              bankName: data?.bankName,
              bankAccount: data?.bankAccount,
            })
          }
        }
      }).catch(err => {
        console.error(err)
      })
    }
  
    const handleClear = () => {
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        buyerName: '',
      })
    }

    return {
      options,
      buyerName,
      invoiceTitleType,
      filterOption,
      handleChange,
      handleSelect,
      handleClear,
      invoiceDetailState,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    margin-bottom: .5rem;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    // :deep(.ant-form-item-explain) {
    //   display: none;
    // }
  }
}
</style>