<template>
  <a4-flex
    justify="end"
  >
    <a4-button
      type="primary"
      @click.prevent.stop="handleManual"
      :disabled="
        invoiceDetailState.status === 'PENDING'
        || invoiceDetailState.manualInvoicing.status === 'PENDING'
      "
      :loading="
        invoiceDetailState.manualInvoicing.status === 'PENDING'
      "
      v-if="actionState?.type === 'OrderInvoiceCreate'"
    >开具发票</a4-button>


    <a4-button
      :disabled="
        invoiceDetailState.status === 'PENDING'
        || invoiceDetailState.manualInvoicing.status === 'PENDING'
      "
      v-if="actionState?.type === 'OrderInvoiceView'"
      @click.prevent.stop="handleClose"
    >关闭</a4-button>

  </a4-flex>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { defineComponent, ref } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Input as A4Input,
  Button as A4Button,
  Flex as A4Flex,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Input,
    A4Button,
    A4Flex,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState,
      actionState
    } = store.state.invoiceCenter

    const handleManual = () => {
      eventBus.emit('invoice-center:invoice-manual-form:submit-form')
    }

    const handleClose = () => {
      eventBus.emit('invoice-center-detail-drawer:close-drawer')
    }

    return {
      actionState,
      invoiceDetailState,
      handleManual,
      handleClose,
    }
  }
})
</script>

<style scoped lang="scss">

</style>