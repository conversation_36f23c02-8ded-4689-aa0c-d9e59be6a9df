<template>
  <!-- == {{ invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList }} == -->
  <!-- == {{ itemLineSelected }} == -->
  <a4-form-item
    class="form-item"
    style="align-items: baseline;"
  >
    <div class="form-item-line">
      <a4-form-item-rest>
        <a4-table
          size="small"
          :bordered="true"
          :pagination="false"
          :rowKey="record => record.productId"
          :columns="columns"
          :dataSource="itemLineAvailableList"
          :rowSelection="rowSelection"
        >
          <template #title>
            <a4-flex justify="space-between">
              <prod-item-filter />
              <batch-remove />
            </a4-flex>
          </template>
          <template #footer v-if="isGoodsSupplementOrder">
            <goods-supplement />
          </template>
          <template #headerCell="{ column }">
            <span style="white-space: nowrap;">
              {{ column.title }}
            </span>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'productId'">
              <template v-if="record.id === -1">
                <record-selector :record="record" />
              </template>
              <template v-else>
                <div>
                  <a4-tag>{{ record?.productId }}</a4-tag>
                  <a4-tag color="#87d068" v-if="record?.isGoodsSupplement">补差</a4-tag>
                </div>
              </template>
            </template>
            <template v-if="column.key === 'productName'">
              <template v-if="record.id === -1">
                <a4-tooltip>
                  <template #title>{{ itemLineSelected?.productName }}</template>
                  <div
                    class="product-name"
                  >{{ itemLineSelected?.productName }}</div>
                </a4-tooltip>
              </template>
              <template v-else>
                <product-name :productId="record?.productId" :index="index" />
              </template>
            </template>

            <template v-if="column.key === 'specs'">
              <template v-if="record.id === -1">
                {{ itemLineSelected?.specs }}
              </template>
              <template v-else>
                <product-specs :productId="record?.productId" />
              </template>
            </template>

            <template v-if="column.key === 'unit'">
              <template v-if="record.id === -1">
                {{ itemLineSelected?.unit }}
              </template>
              <template v-else>
                <product-unit :productId="record?.productId" />
              </template>
            </template>

            <template v-if="column.key === 'quantity'">
              <template v-if="record.id === -1">
                {{ itemLineSelected?.quantity }}
              </template>
              <template v-else>
                <invoice-quantity :productId="record?.productId" />
              </template>
            </template>

            <template v-if="column.key === 'price'">
              <template v-if="record.id === -1">
                {{ itemLineSelected?.price }}
              </template>
              <template v-else>
                <!-- {{ record.price }} -->
                <product-price :productId="record?.productId" />
              </template>
            </template>

            <template v-if="column.key === 'amount'">
              <template v-if="record.id === -1">
                {{ itemLineSelected?.amount }}
              </template>
              <template v-else>
                <product-amount :productId="record?.productId" />
              </template>
            </template>

            <template v-if="column.key === 'discountAmount'">
              <template v-if="record.id === -1">
                {{ itemLineSelected?.discountAmount }}
              </template>
              <template v-else>
                <discount-amount :productId="record?.productId" />
              </template>
            </template>

            <template v-if="column.key === 'taxRate'">
              <template v-if="record.id === -1">
                {{ itemLineSelected?.taxRate }}
              </template>
              <template v-else>
                {{ record.taxRate }}
              </template>
            </template>
            
            <template v-if="column.key === 'actions'">
              <record-add :record="record"
                v-if="record.id === -1 && itemLineSelected"
              />

              <record-remove v-if="record.id !== -1" :record="record" />
            </template>
          </template>
        </a4-table>
      </a4-form-item-rest>
    </div>
  </a4-form-item>
  <a4-form-item
    class="item-line-available-list"
    ref="itemLineAvailableList"
    name="itemLineAvailableList"
  >
    <a4-input
      type="hidden"
      :value="JSON.stringify(itemLineAvailableList)"
      style="position: absolute;"
    ></a4-input>
  </a4-form-item>
</template>

<script lang="ts">
import { filter, keys, map } from 'lodash'
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  Tag as A4Tag,
  Tooltip as A4Tooltip,
  FormItem as A4FormItem,
  FormItemRest as A4FormItemRest,
  Table as A4Table,
  Flex as A4Flex,
  Textarea as A4Textarea,
  Input as A4Input,
  // Radio as A4Radio,
  // RadioGroup as A4RadioGroup,
  // Checkbox as A4Checkbox,
} from 'ant-design-vue-4'

import ProdItemFilter from '../ProdItemFilter/index.vue'
import BatchRemove from '../BatchRemove/index.vue'
import columns from './columns'
import RecordAdd from './components/RecordAdd/index.vue'
import RecordRemove from './components/RecordRemove/index.vue'
import RecordSelector from './components/RecordSelector/index.vue'
import ProductName from './components/ProductName/index.vue'
import InvoiceQuantity from './components/InvoiceQuantity/index.vue'
import ProductPrice from './components/ProductPrice/index.vue'
import ProductAmount from './components/ProductAmount/index.vue'
import DiscountAmount from './components/DiscountAmount/index.vue'
import ProductSpecs from './components/ProductSpecs/index.vue'
import ProductUnit from './components/ProductUnit/index.vue'
import GoodsSupplement from './components/GoodsSupplement/index.vue'

export default defineComponent({
  components: {
    A4FormItem,
    A4Table,
    A4Flex,
    A4Textarea,
    A4Input,
    A4FormItemRest,
    A4Tooltip,
    A4Tag,
    ProdItemFilter,
    BatchRemove,
    RecordAdd,
    RecordRemove,
    RecordSelector,
    ProductName,
    InvoiceQuantity,
    ProductPrice,
    ProductAmount,
    DiscountAmount,
    ProductSpecs,
    ProductUnit,
    GoodsSupplement,
    // A4Radio,
    // A4RadioGroup,
    // A4Checkbox,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const isGoodsSupplementOrder = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.goodsSupplementOrder
    })

    const itemLineList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineList || []
    })

    const itemLineAvailableList = computed(() => {
      return filter(invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || [], (itemLine) => {
        return new RegExp(filterText.value, 'i').test(itemLine.productId) || itemLine.id === -1
      })
    })

    const itemLineRemovedList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineRemovedList || []
    })

    const itemLineSelected = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineSelected
    })

    const filterText = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.filterText
    })

    const selectedRecords = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.selectedRecords || {}
    })

    const dataSource = computed(() => {
      return itemLineAvailableList.value
    })

    const rowSelection = {
      selectedRowKeys: computed(() => {
        return keys(selectedRecords.value)
      }),
      onChange: (selectedRowKeys, selectedRows) => {
        // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      },
      onSelectAll: (selected, selectedRows, changeRows) => {
        if (selected) {
          // console.log(map(itemLineAvailableList.value, (itemLine) => itemLine.productId))
          // store.commit('invoiceCenter/updateInvoiceManualFormState', {
          //   selectedRecords: map(itemLineAvailableList.value, (itemLine) => itemLine.productId)
          // })
          itemLineAvailableList.value.forEach((itemLine) => {
            if (itemLine.productId) selectedRecords.value[itemLine.productId] = itemLine
          })
          store.commit('invoiceCenter/updateInvoiceManualFormState', {
            selectedRecords: selectedRecords.value
          })
        } else {
          store.commit('invoiceCenter/updateInvoiceManualFormState', {
            selectedRecords: {}
          })
        }
      },
      onSelect: (record, selected, selectedRows) => {
        if (selected) {
          selectedRecords.value[record.productId] = record
        } else {
          delete selectedRecords.value[record.productId]
        }
        store.commit('invoiceCenter/updateInvoiceManualFormState', {
          selectedRecords: selectedRecords.value
        })
      },
      getCheckboxProps: (record) => ({
        // disabled: record.name === 'Disabled User', // Column configuration not to be checked
        // name: record.name,
        disabled: record.id === -1 || record?.canInvoiceQuantity === 0,
      }),
    };

    return {
      keys,
      filterText,
      // value,
      dataSource,
      itemLineList,
      itemLineSelected,
      itemLineRemovedList,
      itemLineAvailableList,
      columns,
      rowSelection,
      selectedRecords,
      invoiceDetailState,
      isGoodsSupplementOrder,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    margin-bottom: 0px;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      // display: none;
    }
  }
}

.product-name {
  width: 220px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.item-line-available-list {
  :deep(.ant-form-item-control-input) {
    padding: 4px 0;
    min-height: 0;
  }
}
</style>