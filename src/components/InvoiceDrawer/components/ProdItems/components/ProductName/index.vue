<template>
  <a4-input
    size="small"
    v-model:value="productName"
    @change="handleChange"
    :class="`${classNames({ 'has-error': hasError })}`"
    :disabled="canInvoiceQuantity === 0"
  />
</template>

<script lang="ts">
import classNames from 'classnames'
import { find, findIndex } from 'lodash'
import {
  Button as A4Button,
  Input as A4Input,
  message
} from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
import { remove } from 'lodash'

export default defineComponent({
  components: {
    A4Button,
    A4Input,
  },
  props: ['productId'],
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const itemLineAvailableList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || []
    })

    const index = computed(() => {
      return findIndex(itemLineAvailableList.value, { productId: props.productId })
    })

    const itemLine = computed(() => {
      return find(itemLineAvailableList.value, { productId: props.productId })
    })

    const productName = computed(() => {
      return itemLine.value?.productName
    })

    const canInvoiceQuantity = computed(() => {
      return itemLine.value?.canInvoiceQuantity
    })

    const hasError = computed(() => {
      return !itemLine.value?.productName
    })

    const handleChange = (e) => {
      itemLine.value.productName = e.target.value
      itemLine.value.hasError = hasError.value || itemLine.value?.amount < itemLine.value?.discountAmount
      itemLineAvailableList.value[index.value] = itemLine.value
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        itemLineAvailableList: itemLineAvailableList.value
      })
    }

    return {
      classNames,
      hasError,
      index,
      canInvoiceQuantity,
      productName,
      itemLineAvailableList,
      handleChange,
    }
  }
})
</script>

<style scoped lang="scss">
.has-error {
  border-color: #f00;
  :deep(.ant-input) {
    color: #f00;
  }
}
</style>