<template>
  <!-- {{ invoiceDetailState?.manualInvoicing?.formState }} -->
  <a4-select
    size="small"
    @change="handleChange"
    placeholder="选择商品代码添加"
    allowClear
    :value="itemLineSelected?.productId"
    style="min-width: 180px;"
  >
    <a4-select-option
      v-for="itemLine in itemLineRemovedList"
      :key="itemLine.productId"
      v-model:value="itemLine.productId"
    >
      {{ itemLine.productId }}
    </a4-select-option>
  </a4-select>
</template>

<script lang="ts">
import {
  Button as A4Button,
  Select as A4Select,
  SelectOption as A4SelectOption,
  message
} from 'ant-design-vue-4';
import { computed, defineComponent, watch } from 'vue'
import { useStore } from 'vuex'
import { remove, find, filter } from 'lodash'

export default defineComponent({
  components: {
    A4Button,
    A4Select,
    A4SelectOption,
  },
  props: ['record'],
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const roles = computed(() => {
      return userState?.data?.data?.roles
    })

    const itemLineList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineList || []
    })

    const itemLineRemovedList = computed(() => {
      return filter(invoiceDetailState?.manualInvoicing?.formState?.itemLineRemovedList || [], (itemLine) => {
        return new RegExp(filterText.value, 'i').test(itemLine.productId)
      })
    })

    const itemLineSelected = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineSelected
    })

    const filterText = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.filterText
    })

    // const handleRemove = () => {
    //   remove(itemLineList.value, (itemLine) => {
    //     return props.record.productId === itemLine.productId
    //   })
    //   itemLineRemovedList.value.push(props.record)
    // }

    const handleChange = (v) => {
      const selectedRecord = find(itemLineRemovedList.value, { productId: v })
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        itemLineSelected: selectedRecord
      })
    }

    watch(filterText, () => {
      handleChange(undefined)
    })

    return {
      itemLineList,
      itemLineRemovedList,
      itemLineSelected,
      // handleRemove
      invoiceDetailState,
      handleChange,
    }
  }
})
</script>