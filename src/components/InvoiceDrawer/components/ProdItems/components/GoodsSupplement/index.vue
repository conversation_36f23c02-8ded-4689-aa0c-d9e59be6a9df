<template>
  <a4-input-group size="small" compact>
    <a4-input
      size="small"
      style="width: 240px"
      placeholder="请输入宜家商品代码查询补差商品"
      v-model:value="itemNo"
      :disabled="invoiceDetailState.goodsSupplement?.status === 'PENDING'"
    />
    <a4-button
      size="small"
      type="primary"
      :disabled="!itemNo"
      :loading="invoiceDetailState.goodsSupplement?.status === 'PENDING'"
      @click.prevent.stop="handleClick"
    >
      查询
    </a4-button>
  </a4-input-group>
</template>

<script lang="ts">
import {
  InputGroup as A4InputGroup,
  Input as A4Input,
  Button as A4Button,
} from 'ant-design-vue-4'
import { defineComponent, ref } from 'vue'
import { useStore } from 'vuex'

export default defineComponent({
  components: {
    A4InputGroup,
    A4Input,
    A4Button,
  },
  setup() {
    const itemNo = ref()
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const handleClick = () => {
      if (!itemNo.value?.trim()) {
        return
      }
      store.dispatch('invoiceCenter/queryGoodsSupplementItem', {
        itemNo: itemNo.value.trim(),
      }).then((res) => {
        // 只有查询成功且有数据时才清空输入框
        if (res?.data?.code === '0' && res?.data?.data) {
          itemNo.value = ''
        }
      }).catch((err) => {
        console.error('查询商品补差信息失败:', err)
        // 错误已在 store action 中处理，这里只记录日志
      })
    }

    return {
      handleClick,
      itemNo,
      invoiceDetailState,
    }
  }
})
</script>

<style lang="scss" scoped>

</style>