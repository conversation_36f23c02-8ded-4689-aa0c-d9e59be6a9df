<template>
  <a4-input-number
    size="small"
    :value="discountAmount"
    :min="0"
    :step="0.01"
    @change="handleChange"
    style="width: 80px;"
    :controls="false"
    :placeholder="originDiscountAmount"
    allowClear
    :class="`${classNames({ 'has-error': hasError })}`"
  />
</template>

<script lang="ts">
import classNames from 'classnames'
import { find, findIndex, clone } from 'lodash'
import {
  Button as A4Button,
  Input as A4Input,
  InputNumber as A4InputNumber,
  message
} from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'

export default defineComponent({
  components: {
    A4Button,
    A4Input,
    A4InputNumber,
  },
  props: ['productId'],
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const itemLineAvailableList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || []
    })

    const index = computed(() => {
      return findIndex(itemLineAvailableList.value, { productId: props.productId })
    })

    const itemLine = computed(() => {
      return find(itemLineAvailableList.value, { productId: props.productId })
    })

    const discountAmount = computed(() => {
      return itemLine.value?.discountAmount || 0
    })

    const originDiscountAmount = computed(() => {
      return itemLine.value?.originDiscountAmount || 0
    })

    const hasError = computed(() => {
      return itemLine.value?.netAmount < itemLine.value?.discountAmount
    })

    const handleChange = (v) => {
      itemLine.value.discountAmount = v
      itemLine.value.hasError = hasError.value || !itemLine.value?.productName
      itemLineAvailableList.value[index.value] = itemLine.value
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        itemLineAvailableList: itemLineAvailableList.value
      })
    }

    return {
      hasError,
      classNames,
      discountAmount,
      originDiscountAmount,
      itemLineAvailableList,
      handleChange,
    }
  }
})
</script>

<style scoped lang="scss">
.has-error {
  border-color: #f00;
  :deep(.ant-input-number-input) {
    color: #f00;
  }
}
</style>