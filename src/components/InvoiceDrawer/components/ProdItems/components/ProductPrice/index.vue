<template>
  <a4-input-number
    size="small"
    :value="price"
    :min="0.01"
    :step="0.01"
    @change="handleChange"
    style="width: 80px;"
    :controls="false"
    :placeholder="originPrice"
    allowClear
    :class="`${classNames({ 'has-error': hasError })}`"
    :disabled="canInvoiceQuantity === 0"
  />
</template>

<script lang="ts">
import Decimal from 'decimal.js'
import classNames from 'classnames'
import { find, findIndex, clone } from 'lodash'
import {
  Button as A4Button,
  Input as A4Input,
  InputNumber as A4InputNumber,
  message
} from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
import { remove } from 'lodash'

export default defineComponent({
  components: {
    A4Button,
    A4Input,
    A4InputNumber,
  },
  props: ['productId'],
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const itemLineAvailableList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || []
    })

    const index = computed(() => {
      return findIndex(itemLineAvailableList.value, { productId: props.productId })
    })

    const itemLine = computed(() => {
      return find(itemLineAvailableList.value, { productId: props.productId })
    })

    const price = computed(() => {
      return itemLine.value?.price || 0
    })

    const originPrice = computed(() => {
      return itemLine.value?.originPrice || 0
    })

    const canInvoiceQuantity = computed(() => {
      return itemLine.value?.canInvoiceQuantity
    })

    const hasError = computed(() => {
      return itemLine.value?.amount < itemLine.value?.discountAmount
    })

    const handleChange = (v) => {
      itemLine.value.price = new Decimal(v).toFixed(2)
      itemLine.value.amount = new Decimal(v).mul(itemLine.value?.quantity)
      itemLine.value.hasError = hasError.value || !itemLine.value?.productName
      itemLineAvailableList.value[index.value] = itemLine.value
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        itemLineAvailableList: itemLineAvailableList.value
      })
    }

    return {
      hasError,
      classNames,
      canInvoiceQuantity,
      price,
      originPrice,
      itemLineAvailableList,
      handleChange,
    }
  }
})
</script>

<style scoped lang="scss">
.has-error {
  border-color: #f00;
  :deep(.ant-input-number-input) {
    color: #f00;
  }
}
</style>