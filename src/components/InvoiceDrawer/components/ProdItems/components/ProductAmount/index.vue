<template>
  {{ amount }}
</template>

<script lang="ts">
import Decimal from 'decimal.js';
import { find, findIndex } from 'lodash'
import {
  Button as A4Button,
  Input as A4Input,
  InputNumber as A4InputNumber,
  message
} from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
import { remove } from 'lodash'

export default defineComponent({
  components: {
    A4Button,
    A4Input,
    A4InputNumber,
  },
  props: ['productId'],
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const itemLineAvailableList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || []
    })

    const index = computed(() => {
      return findIndex(itemLineAvailableList.value, { productId: props.productId })
    })

    const itemLine = computed(() => {
      return find(itemLineAvailableList.value, { productId: props.productId })
    })

    const price = computed(() => {
      return itemLine.value?.price || 0
    })

    const quantity = computed(() => {
      return itemLine.value?.quantity || 0
    })

    const amount = computed(() => {
      // return 
      return new Decimal(price.value).mul(quantity.value).toNumber()
    })

    return {
      price,
      quantity,
      amount,
      itemLineAvailableList,
    }
  }
})
</script>