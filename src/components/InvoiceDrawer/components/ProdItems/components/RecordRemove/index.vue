<template>
  <a4-button
    size="small"
    type="link"
    style="padding: 0 4px;"
    @click.prevent.stop="handleRemove"
  >删除</a4-button>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { Button as A4Button, message } from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
import { remove, find } from 'lodash'

export default defineComponent({
  components: {
    A4Button
  },
  props: ['record'],
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const roles = computed(() => {
      return userState?.data?.data?.roles
    })

    const itemLineList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineList || []
    })

    const itemLineAvailableList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || []
    })

    const itemLineRemovedList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineRemovedList || []
    })

    const selectedRecords = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.selectedRecords || {}
    })

    // const handleRemove = () => {
    //   remove(itemLineAvailableList.value, (itemLine) => {
    //     return props.record?.productId === itemLine?.productId
    //   })
    //   // 
    //   const extRecord = find(itemLineAvailableList.value, (itemLine) => {
    //     return itemLine?.id === -1
    //   })

    //   if (!extRecord) {
    //     itemLineAvailableList.value.push({ id: -1 })
    //   } 
    //   // 
    //   itemLineRemovedList.value.push(props.record)
    // }

    const handleRemove = () => {
      delete selectedRecords.value[props.record.productId]
      
      eventBus.emit('invoice-center:invoice-manual-form:remove-item', props.record);
    }

    return {
      itemLineList,
      handleRemove
    }
  }
})
</script>