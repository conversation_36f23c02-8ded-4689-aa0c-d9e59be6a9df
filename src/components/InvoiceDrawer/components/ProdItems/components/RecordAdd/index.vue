<template>
  <a4-button
    size="small"
    type="link"
    style="padding: 0 4px;"
    @click.prevent.stop="handleAdd"
  >增加</a4-button>
</template>

<script lang="ts">
import { Button as A4Button, message } from 'ant-design-vue-4';
import { computed, defineComponent } from 'vue'
import { useStore } from 'vuex'
import { remove } from 'lodash'

export default defineComponent({
  components: {
    A4Button
  },
  props: ['record'],
  setup(props) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const roles = computed(() => {
      return userState?.data?.data?.roles
    })

    const itemLineList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineList || []
    })

    const itemLineAvailableList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || []
    })

    const itemLineRemovedList = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineRemovedList || []
    })

    const itemLineSelected = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.itemLineSelected
    })

    const handleAdd = () => {
      remove(itemLineAvailableList.value, (itemLine) => {
        return itemLine.id === -1
      })

      itemLineAvailableList.value.push(itemLineSelected.value)
      remove(itemLineRemovedList.value, (itemLine) => {
        return itemLineSelected.value.productId === itemLine.productId
      })

      if (itemLineRemovedList.value.length) {
        itemLineAvailableList.value.push({ id: -1 })
      }

      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        // itemLineRemovedList: itemLineRemovedList.value,
        itemLineSelected: undefined
      })
    }

    return {
      itemLineAvailableList,
      handleAdd
    }
  }
})
</script>