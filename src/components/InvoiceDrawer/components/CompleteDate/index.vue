<template>
  <a4-form-item
    class="form-item"
    label="订单完成时间"
    style="align-items: baseline;"
  >
    <div class="form-item-line">
      {{
        orderCompletedTimeStamp
          ? dayjs(orderCompletedTimeStamp).format('YYYY-MM-DD HH:mm:ss')
          : '--'
      }}
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');

import {
  FormItem as A4FormItem,
  Radio as A4Radio,
  RadioGroup as A4RadioGroup,
  Checkbox as A4Checkbox,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Radio,
    A4RadioGroup,
    A4Checkbox,
  },
  setup(props) {
    // const value = ref<number>(1);
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const orderCompletedTimeStamp = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.orderCompletedTimeStamp
    })

    return {
      dayjs,
      // value,
      orderCompletedTimeStamp,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    // border: 1px solid #000;
    margin-bottom: 0;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      display: none;
    }
  }
}
</style>