<template>
  <div class="form-item">
    <!-- === {{ ikeaOrderNO }} === -->
    <!-- {{ invoiceTitleType }} -->
    <a4-form-item
      label="抬头类型"
      style="align-items: baseline;"
    >
      <div class="form-item-line">
        <a4-radio-group
          v-model:value="invoiceTitleType"
          @change="handleInvoiceTitleTypeChange"
        >
          <a4-radio value="PERSON">个人/非企业性单位</a4-radio>
          <a4-radio value="ENTERPRISE">企业</a4-radio>
        </a4-radio-group>
      </div>
      <div class="form-item-line" v-if="['PERSON'].includes(invoiceTitleType)">
        <!-- Natural Person -->
        <a4-checkbox
          :checked="isNaturalPerson"
          @change="handleNaturalPersonChange"
        >开票给自然人</a4-checkbox>
      </div>
    </a4-form-item>
  </div>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { defineComponent, ref, computed, h } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Radio as A4Radio,
  RadioGroup as A4RadioGroup,
  Checkbox as A4Checkbox,
  Modal as A4Modal,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Radio,
    A4RadioGroup,
    A4Checkbox,
    A4Modal,
  },
  setup(props) {
    const store = useStore();
    const {
      actionState,
      invoiceDetailState
    } = store.state.invoiceCenter;

    const ikeaOrderNO = computed(() => {
      return actionState?.record?.ikeaOrderNO
    })

    const invoiceTitleType = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.invoiceTitleType;
    })

    const isNaturalPerson = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.isNaturalPerson;
    })
    
    const handleInvoiceTitleTypeChange = (e) => {
      eventBus.emit('invoice-center:invoice-manual-form:reset-form');
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        invoiceTitleType: e.target.value,
        invoiceType: 'GENERAL_DIGITAL_INVOICE'
      });

      store.dispatch('invoiceCenter/fetchInvoiceDetail', {
        orderId: ikeaOrderNO.value,
        extOrderType: 'JD',
        invoiceTitleType: e.target.value, 
      });
    }

    const handleNaturalPersonChange = (e) => {
      if (e.target.checked) {
        A4Modal.info({
          width: 720,
          title: '确认开票给自然人',
          content: h('div', {}, [
            h('p', '除特定业务外的普通发票：'),
            h('p', '1. 如受票方（发票抬头）为自然人，请根据实际需要提供姓名或姓。如您的姓名为张某某，可在名称栏次填写：张某某、张先生或张女士；'),
            h('p', '2. 如受票方（发票抬头）为自然人，并要求能将发票归集在个人票夹中展示，需要提供姓名及身份证号码（自然人納税人识别号）；'),
            h('p', '3. 如受票方（发票抬头）为个体工商户，需提供统一社会信用代码或納税人识别号，请勿勾选此标识。'),
          ]),
          okText: '已知晓',
          onOk() {
            console.log('ok');
          },
        })
      }
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        isNaturalPerson: e.target.checked,
      });
    }

    return {
      isNaturalPerson,
      invoiceDetailState,
      invoiceTitleType,
      handleInvoiceTitleTypeChange,
      handleNaturalPersonChange,
      ikeaOrderNO,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      display: none;
    }
  }
}
</style>