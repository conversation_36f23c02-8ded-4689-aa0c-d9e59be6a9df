<template>
  <a4-form-item
    class="form-item"
    label="折扣金额"
    style="align-items: baseline;"
  >
    <div class="form-item-line">
      {{ discountAmount}}
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { filter, keys, map } from 'lodash'
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Radio as A4Radio,
  RadioGroup as A4RadioGroup,
  Checkbox as A4Checkbox,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Radio,
    A4RadioGroup,
    A4Checkbox,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const filterText = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.filterText
    })
    
    const itemLineAvailableList = computed(() => {
      return filter(invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || [], (itemLine) => {
        return new RegExp(filterText.value, 'i').test(itemLine.productId) || itemLine.id === -1
      })
    })

    const discountAmount = computed(() => {
      return itemLineAvailableList.value.reduce((acc, itemLine) => {
        return acc + (itemLine?.discountAmount || 0)
      }, 0)
    })

    return {
      discountAmount
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    // border: 1px solid #000;
    margin-bottom: 0;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      display: none;
    }
  }
}
</style>