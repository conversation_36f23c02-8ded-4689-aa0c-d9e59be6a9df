<template>
  <!-- {{ invoiceDetailState?.manualInvoicing?.formState }} -->
  <a4-form-item
    class="form-item"
    label="发票类型"
    style="align-items: baseline;"
  >
    <div class="form-item-line">
      <a4-radio-group
        v-model:value="invoiceType"
        @change="handleInvoiceTypeChange"
      >
        <a4-radio
          value="GENERAL_DIGITAL_INVOICE"
        >
          数电票（普通发票）
        </a4-radio>
        <a4-radio
          value="SPECIAL_DIGITAL_INVOICE"
          :disabled="invoiceTitleType === 'PERSON'"
        >
          数电票（专用发票）
        </a4-radio>
      </a4-radio-group>
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Radio as A4Radio,
  RadioGroup as A4RadioGroup,
  Checkbox as A4<PERSON><PERSON>ckbox,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Radio,
    A4RadioGroup,
    A4Checkbox,
  },
  setup(props) {
    const store = useStore();
    const {
      invoiceDetailState
    } = store.state.invoiceCenter;

    const invoiceType = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.invoiceType;
    })

    const invoiceTitleType = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.invoiceTitleType;
    })

    const handleInvoiceTypeChange = (e) => {
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        invoiceType: e.target.value,
      });
    }

    return {
      invoiceType,
      invoiceTitleType,
      invoiceDetailState,
      handleInvoiceTypeChange,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      display: none;
    }
  }
}
</style>