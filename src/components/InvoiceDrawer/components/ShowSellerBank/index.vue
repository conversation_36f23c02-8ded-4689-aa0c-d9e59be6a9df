<template>
  <a4-form-item
    class="form-item"
    style="align-items: baseline;"
  >
    <div class="form-item-line">
      <a4-checkbox
        :checked="isShowSellerBank"
        @change="handleShowSellerBankChange"
      >添加销方银行</a4-checkbox>
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { filter, keys, map } from 'lodash'
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Radio as A4Radio,
  RadioGroup as A4RadioGroup,
  Checkbox as A4Checkbox,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Radio,
    A4RadioGroup,
    A4Checkbox,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const isShowSellerBank = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.isShowSellerBank
    })

    const handleShowSellerBankChange = (e) => {
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        isShowSellerBank: e?.target?.checked
      })
    }

    return {
      isShowSellerBank,
      invoiceDetailState,
      handleShowSellerBankChange,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    // border: 1px solid #000;
    margin-bottom: 0;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      display: none;
    }
  }
}
</style>