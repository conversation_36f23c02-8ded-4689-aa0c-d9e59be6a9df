<template>
  <a4-form-item
    class="form-item"
    style="align-items: baseline;"
    ref="filterText"
    name="filterText"
  >
    <div class="form-item-line">
      <a4-input
        size="small"
        placeholder="输入商品id查询商品"
        @change="handleFilterTextChange"
        allowClear
      />
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Radio as A4Radio,
  RadioGroup as A4RadioGroup,
  Checkbox as A4Checkbox,
  Input as A4Input,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Radio,
    A4RadioGroup,
    A4Checkbox,
    A4Input,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const handleFilterTextChange = (e) => {
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        filterText: e?.target?.value?.trim()
      })
    }

    return {
      invoiceDetailState,
      handleFilterTextChange,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    // border: 1px solid #000;
    margin-bottom: 0;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      // display: none;
    }
  }
}
</style>