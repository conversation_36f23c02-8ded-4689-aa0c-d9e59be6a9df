<template>
  <a4-form-item
    class="form-item"
    style="align-items: baseline;"
  >
    <div class="form-item-line">
      <!-- {{ (selectedRecords) }} -->
      <a4-button
        size="small"
        :disabled="keys(selectedRecords).length === 0"
        @click="handleBatchRemove"
      >批量删除</a4-button>
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { keys, each } from 'lodash'
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Radio as A4Radio,
  RadioGroup as A4RadioGroup,
  Checkbox as A4Checkbox,
  Input as A4Input,
  Button as A4Button,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Radio,
    A4RadioGroup,
    A4Checkbox,
    A4Input,
    A4Button,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const selectedRecords = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.selectedRecords || []
    })

    const handleBatchRemove = () => {
      // selectedRecords.value.forEach((record) => {
      //   eventBus.emit('invoice-center:invoice-manual-form:remove-item', record)
      // })
      each(selectedRecords.value, (record) => {
        eventBus.emit('invoice-center:invoice-manual-form:remove-item', record)
      })

      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        selectedRecords: {}
      })
    }

    return {
      keys,
      selectedRecords,
      // value,
      invoiceDetailState,
      handleBatchRemove,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    // border: 1px solid #000;
    margin-bottom: 0;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      display: none;
    }
  }
}
</style>