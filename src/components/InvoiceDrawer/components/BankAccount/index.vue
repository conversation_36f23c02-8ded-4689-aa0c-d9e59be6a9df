<template>
  <a4-form-item
    class="form-item"
    label="银行账号"
    ref="bankAccount"
    name="bankAccount"
    :labelCol="{ span: 6 }"
    style="align-items: baseline; padding: 0 1rem 0 0;"
  >
    <div class="form-item-line">
      <a4-input
        allowClear
        size="small"
        v-model:value="bankAccount"
        @change="handleBankAccountChange"
      />
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Input as A4Input,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Input,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const bankAccount = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.bankAccount
    })

    const handleBankAccountChange = (e) => {
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        bankAccount: e.target.value
      })
    }

    return {
      bankAccount,
      invoiceDetailState,
      handleBankAccountChange,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    margin-bottom: .5rem;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      // display: none;
    }
  }
}
</style>