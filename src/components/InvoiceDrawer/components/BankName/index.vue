<template>
  <a4-form-item
    class="form-item"
    label="开户行"
    ref="bankName"
    name="bankName"
    :labelCol="{ span: 6 }"
    style="align-items: baseline; padding: 0 1rem 0 0;"
  >
    <div class="form-item-line">
      <a4-input
        allowClear
        size="small"
        v-model:value="bankName"
        @change="handleBankNameChange"
      />
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Input as A4Input,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Input,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter
    
    const bankName = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.bankName?.trim()
    })

    const handleBankNameChange = (e) => {
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        bankName: e.target.value
      })
    }

    return {
      bankName,
      invoiceDetailState,
      handleBankNameChange,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    margin-bottom: .5rem;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    :deep(.ant-form-item-explain) {
      // display: none;
    }
  }
}
</style>