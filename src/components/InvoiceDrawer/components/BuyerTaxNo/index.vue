<template>
  <a4-form-item
    v-if="bool"
    class="form-item"
    ref="buyerTaxNo"
    name="buyerTaxNo"
    :label="label"
    :labelCol="{ span: 6 }"
    style="align-items: baseline; padding: 0 1rem 0 0;"
  >
    <div class="form-item-line">
      <a4-input
        allowClear
        size="small"
        v-model:value="buyerTaxNo"
        @change="handleBuyerTaxNoChange"
      />
    </div>
  </a4-form-item>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue'
import { useStore } from 'vuex'

import {
  FormItem as A4FormItem,
  Input as A4Input,
} from 'ant-design-vue-4'

export default defineComponent({
  components: {
    A4FormItem,
    A4Input,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const invoiceTitleType = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.invoiceTitleType;
    })

    const isNaturalPerson = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.isNaturalPerson;
    })

    const buyerTaxNo = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.buyerTaxNo
    })

    const label = computed(() => {
      if (['ENTERPRISE'].includes(invoiceTitleType.value)) {
        return '税号'
      }
      return '身份证'
    })

    const bool = computed(() => {
      if (['ENTERPRISE'].includes(invoiceTitleType.value)) {
        return true
      } else if (['PERSON'].includes(invoiceTitleType.value)) {
        if (isNaturalPerson.value) {
          return true
        }
        return false
      }
    })

    const handleBuyerTaxNoChange = (e) => {
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        buyerTaxNo: e?.target?.value?.trim(),
      })
    }

    return {
      bool,
      label,
      invoiceTitleType,
      buyerTaxNo,
      isNaturalPerson,
      handleBuyerTaxNoChange,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 4px 0;
  }

  .form-item {
    margin-bottom: .5rem;
    &-line {
      margin: 0;
    }
    :deep(.ant-form-item) {
      margin-bottom: 0!important;
    }
    // :deep(.ant-form-item-explain) {
    //   display: none;
    // }
  }
}
</style>