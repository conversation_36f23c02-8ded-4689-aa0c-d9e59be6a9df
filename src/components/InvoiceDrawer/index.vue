<template>
  <a4-drawer
    :bodyStyle="{ padding: '0' }"
    size="small"
    :open="open"
    :width="1080"
    placement="right"
    :closable="true"
    :destroyOnClose="true"
    @close="handleClose"
  >
    <template #extra>
      <template v-if="actionState?.type === 'OrderInvoiceView'">
        <!-- <a :href="record?.invoicePdfView" target="_blank">发票链接</a> -->
      </template>
    </template>
    <template #title>
      <template v-if="actionState?.type === 'OrderInvoiceCreate'">开票</template>
      <template v-if="actionState?.type === 'OrderInvoiceView'">查看发票</template>
    </template>
    <template v-if="actionState?.type === 'OrderInvoiceCreate'">
      <a4-spin
        :spinning="
          invoiceDetailState.status === 'PENDING'
          || invoiceDetailState.manualInvoicing.status === 'PENDING'
        "
      >
        <!-- {{ invoiceDetailState?.manualInvoicing?.formState }} -->
        <a4-form
          ref="formRef"
          autocomplete="off"
          :model="invoiceDetailState?.manualInvoicing?.formState"
          :rules="rules"
          :validateFirst="true"
        >
          <invoice-title />

          <order-info />
        </a4-form>
      </a4-spin>
    </template>
    <tempalte v-else-if="actionState?.type === 'OrderInvoiceView'">
      <vue-pdf :src="record?.invoicePdfView" />
    </tempalte>
    
    <template #footer>
      <invoice-actions />
    </template>
  </a4-drawer>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { useStore } from 'vuex'
import { useInit } from '@/composables/invoice-center/invoice-detail/useInit';
import { useForm } from '@/composables/invoice-center/invoice-detail/useForm';

import {
  Drawer as A4Drawer,
  Form as A4Form,
  Button as A4Button,
  Spin as A4Spin,
} from 'ant-design-vue-4'

import {
  InvoiceTitle,
  OrderInfo,
} from './Parts'

import {
  InvoiceActions
} from './components'

export default defineComponent({
  components: {
    A4Drawer,
    A4Form,
    A4Button,
    A4Spin,
    InvoiceTitle,
    OrderInfo,
    InvoiceActions,
  },
  setup(props) {
    const store = useStore();
    const {
      invoiceDetailState,
      actionState,
    } = store.state.invoiceCenter;

    const {
      open,
      record,
      // handleOpenChange,
      handleClose,
      // orderDetailState,
      // orderDetail,
      // actionState,
    } = useInit(props)

    const {
      formRef,
      rules,
      // actionState,
    } = useForm(props)

    return {
      formRef,
      open,
      record,
      // handleOpenChange,
      handleClose,
      invoiceDetailState,
      // orderDetail,
      rules,
      actionState,
    }
  }
})
</script>

<style scoped lang="scss">

</style>