<template>
  <section>
    <!-- {{ invoiceDetailState?.manualInvoicing?.formState }} -->
    <a4-card title="发票抬头" size="small">
      <a4-row>
        <a4-col :span="12">
          <invoice-title-type />
        </a4-col>
        <a4-col :span="12">
          <invoice-type />
        </a4-col>
      </a4-row>

      <a4-row>
        <a4-col :span="8">
          <buyer-name />
        </a4-col>
        <!-- <a4-col :span="8">
          <id-number />
        </a4-col> -->
        <a4-col :span="8">
          <buyer-tax-no />
        </a4-col>
      </a4-row>

      <a4-row v-if="['ENTERPRISE'].includes(invoiceTitleType)">
        <a4-col :span="8">
          <buyer-address />
        </a4-col>
      </a4-row>

      <a4-row v-if="['ENTERPRISE'].includes(invoiceTitleType)">
        <a4-col :span="8">
          <bank-name />
        </a4-col>
        <a4-col :span="8">
          <bank-account/>
        </a4-col>
        <a4-col :span="8">
          <contact-phone />
        </a4-col>
      </a4-row>

      <a4-row>
        <a4-col :span="8">
          <mail-account />
        </a4-col>
        <a4-col :span="8">
          <!-- <bank-account/> -->
        </a4-col>
        <a4-col :span="8">
          <!-- <contact-phone /> -->
        </a4-col>
      </a4-row>

      <template #extra>
        <a4-flex justify="end">
          <a4-button
            size="small"
            @click.prevent.stop="handleReset"
          >清空</a4-button>
        </a4-flex>
      </template>
    </a4-card>
  </section>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'

import {
  Divider as A4Divider,
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
  Input as A4Input,
  Card as A4Card,
  Flex as A4Flex,
  Button as A4Button,
} from 'ant-design-vue-4'

import {
  InvoiceTitleType,
  InvoiceType,
  BuyerName,
  IdNumber,
  BuyerTaxNo,
  BuyerAddress,
  BankName,
  BankAccount,
  ContactPhone,
  MailAccount,
} from '../components/'

export default defineComponent({
  components: {
    A4Divider,
    A4Row,
    A4Col,
    A4FormItem,
    A4Input,
    A4Card,
    A4Flex,
    A4Button,
    InvoiceTitleType,
    InvoiceType,
    BuyerName,
    BuyerTaxNo,
    BuyerAddress,
    BankName,
    BankAccount,
    ContactPhone,
    MailAccount,
    IdNumber,
  },
  setup(props) {
    const store = useStore()
    const {
      invoiceDetailState
    } = store.state.invoiceCenter

    const invoiceTitleType = computed(() => {
      return invoiceDetailState?.manualInvoicing?.formState?.invoiceTitleType;
    })

    const handleReset = () => {
      eventBus.emit('invoice-center:invoice-manual-form:reset-form', {
        fields: [
          'buyerName',
          'buyerTaxNo',
          'buyerAddress',
          'contactPhone',
          'mailAccount',
          'bankName',
          'bankAccount'
        ]
      });
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        // invoiceTitleType: 'ENTERPRISE',
        // invoiceType: 'SPECIAL',
        buyerName: '',
        // idNumber: '',
        buyerTaxNo: '',
        buyerAddress: '',
        bankName: '',
        bankAccount: '',
        contactPhone: '',
        mailAccount: '',
      })
    }

    return {
      invoiceDetailState,
      handleReset,
      invoiceTitleType,
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  .divider {
    margin: 0 0 8px 0;
  }
}
</style>