<template>
  <section>
    <a4-card title="订单信息" size="small">
      <a4-row>
        <a4-col :span="12">
          <order-no />
        </a4-col>
        <a4-col :span="12">
          <complete-date />
        </a4-col>
      </a4-row>

      <a4-row>
        <a4-col :span="24">
          <prod-items />
        </a4-col>
      </a4-row>

      <a4-row>
        <a4-col :span="8">
          <discount-amount />
        </a4-col>
        <a4-col :span="8">
          <total-amount />
        </a4-col>
        <a4-col :span="8">
          <invoice-amount />
        </a4-col>
      </a4-row>

      <a4-row>
        <a4-col :span="8">
          <show-buyer-bank />
        </a4-col>
        <a4-col :span="8">
          <show-seller-bank />
        </a4-col>
      </a4-row>

      <a4-row>
        <a4-col :span="12">
          <remark />
        </a4-col>
      </a4-row>

      <template #extra>
      </template>
    </a4-card>
  </section>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import { useStore } from 'vuex'

import {
  Divider as A4Divider,
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
  Input as A4Input,
  Card as A4Card,
  Flex as A4Flex,
  Button as A4Button,
} from 'ant-design-vue-4'

import {
  CompleteDate,
  OrderNo,
  ProdItems,
  DiscountAmount,
  TotalAmount,
  InvoiceAmount,
  Remark,
  ShowBuyerBank,
  ShowSellerBank,
} from '../components/'
import { invalid } from 'moment'

export default defineComponent({
  components: {
    A4Divider,
    A4Row,
    A4Col,
    A4FormItem,
    A4Input,
    A4Card,
    A4Flex,
    A4Button,
    OrderNo,
    CompleteDate,
    ProdItems,
    DiscountAmount,
    TotalAmount,
    InvoiceAmount,
    Remark,
    ShowBuyerBank,
    ShowSellerBank,
  },
  setup(props) {
    return {
      
    }
  }
})
</script>

<style scoped lang="scss">
section {
  padding: 1rem;
  padding-top: 0;
  .divider {
    margin: 0 0 8px 0;
  }
}
</style>