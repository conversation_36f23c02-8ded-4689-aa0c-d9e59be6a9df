<template>
  <a4-modal
    class="a4-modal"
    size="small"
    :title="title"
    :open="show"
    @cancel="handleCancel"
    :width="1080"
    :destroyOnClose="true"
    :bodyStyle="{ margin:0, padding:0 }"
    :tarBarGutter="0"
  >
    <template #footer>
      <price-compensation-apply-form-actions />
    </template>

    <a4-tabs
      size="small"
      :animated="false"
      :destroyInactiveTabPane="true"
      @change="handleTabChange"
      :activeKey="activeKey"
      :tabBarGutter="0"
      :tabBarStyle="{ margin: 0, border: 0 }"
      class="price-compensation-dialog-tabs"
    >
      <a4-tab-pane key="form" tab="补偿操作">
        <apply-form />
      </a4-tab-pane>
      <a4-tab-pane key="table" tab="补偿历史">
        <compensation-history />
      </a4-tab-pane>
    </a4-tabs>
  </a4-modal>
</template>
<script lang="ts">
import { defineComponent, ref, UnwrapRef, reactive, watch, onMounted, computed, toRaw } from 'vue'
import { useStore } from 'vuex'
import { useInit } from '@/composables/price-compensation/compensation-apply/useInit';
import { useForm } from '@/composables/price-compensation/compensation-apply/useForm';

import {
  Tabs as A4Tabs,
  TabPane as A4TabPane,
  Modal as A4Modal
} from 'ant-design-vue-4';

import ApplyForm from './ApplyForm/index.vue'
import CompensationHistory from './CompensationHistory/index.vue'
import PriceCompensationApplyFormActions from './components/FormActions/index.vue'

export default defineComponent({
  components: {
    A4Tabs,
    A4TabPane,
    A4Modal,
    ApplyForm,
    CompensationHistory,
    PriceCompensationApplyFormActions,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const title = ref('价格补偿')
    const formViewRef = ref();
    const formActionsRef = ref();
    const formRef = ref();
    const store = useStore();
    const needDoubleConfirm = ref(false)

    const {
      tabsState,
      actionState,
    } = store.state.priceCompensation;

    const {
      handleTabChange,
    } = useInit(props)

    const {
      handleCancel,
    } = useForm(props)

    const activeKey = computed(() => tabsState.activeKey)

    watch(() => tabsState.activeKey, (_activeKey) => {
      store.commit('priceCompensation/updateDialogTabsActiveKey', {
        activeKey: _activeKey
      })
    })

    return {
      activeKey,
      title,
      needDoubleConfirm,
      formRef,
      formViewRef,
      formActionsRef,
      handleTabChange,
      handleCancel,
      actionState,
    }
  }
})
</script>
<style lang="scss" scoped>
.col {
  padding: 0 1rem;
  &-r {
    border-left: 1px solid #eaeaea;
  }
}
.form-wrapper {
  .form-item {
    // display: flex;
    // border: 1px solid #000;
    align-items: baseline;
    h3 {
      font-size: .75rem;
      font-weight: normal;
      color: #666;
      width: 20%;
    }
    label {
      display: block;
      line-height: 32px;
    }
  }
}
.error-tips {
  color: #f00;
}


.price-compensation-dialog-tabs {
  :deep(.ant-tabs-nav) {
    // border-bottom: 1px solid rgb(240, 240, 240);
  }
  :deep(.ant-tabs-tab) {
    margin: 0;
    padding: 10px 16px;
  }
  :deep(.ant-tabs-tab-active) {
    + .ant-tabs-ink-bar {
      transform: translateY(-1px)!important;
    }
  }
}
</style>
