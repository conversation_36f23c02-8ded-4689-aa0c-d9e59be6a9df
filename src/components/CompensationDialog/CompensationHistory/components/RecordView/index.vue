<template>
  <a4-button
    size="small"
    @click="handleRecordView"
  >查看</a4-button>
</template>

<script lang="ts">
import { defineComponent, } from 'vue'
import { useStore } from 'vuex'
import { Button as A4Button, Modal } from 'ant-design-vue-4';

export default defineComponent({
  props: ['record'],
  components: {
    A4Button,
  },
  // props: ['formState', 'formView', 'actionState', 'source'],
  setup(props, { emit }) {
    const store = useStore();

    const {
      orderDetailState,
      actionState,
    } = store.state.priceCompensation;
    
    const handleRecordView = () => {
      store.commit('priceCompensation/updateActionState', {
        type: 'OrderCompensationView',
        record: props.record,
      })
      store.commit('priceCompensation/updateDialogTabsActiveKey', {
        activeKey: 'form'
      })
    }

    return {
      actionState,
      orderDetailState,
      handleRecordView,
    }
  }
})
</script>