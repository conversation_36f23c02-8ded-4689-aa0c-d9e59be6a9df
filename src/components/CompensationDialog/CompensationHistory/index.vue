<template>
  <a4-table
    size="small"
    :pagination="false"
    :loading="orderDetailState?.loading?.status === 'PENDING'"
    :dataSource="dataSource"
    :columns="columns"
  >
    <template #headerCell="{ column }">
      <span style="white-space: nowrap;">
        {{ column.title }}
      </span>
    </template>
    <template #bodyCell="{ record, column }">
      <template v-if="column.key === 'refundAmount'">
        <ik-price v-if="record.refundAmount" :price="record.refundAmount" />
      </template>
      <template v-if="column.key === 'action'">
        <record-view :record="record" />
      </template>
    </template>
  </a4-table>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { Table as A4Table } from 'ant-design-vue-4';
import IkPrice from '@/stories/components/Price/index.vue'
import RecordView from './components/RecordView/index.vue'

const columns = [
  {
    title: '退款申请时间',
    dataIndex: 'applyDate',
    key: 'applyDate',
  },
  {
    title: '退款完成时间',
    dataIndex: 'completeDate',
    key: 'completeDate',
  },
  {
    title: '补偿金额',
    dataIndex: 'refundAmount',
    key: 'refundAmount',
  },
  {
    title: '退款状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '申请人',
    dataIndex: 'applier',
    key: 'applier',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
]


export default defineComponent({
  components: {
    A4Table,
    IkPrice,
    RecordView,
  },
  setup() {
    const store = useStore();
    const {
      orderDetailState,
      actionState,
    } = store.state.priceCompensation;

    const vid = computed(() => actionState?.record?.vid)

    const dataSource = computed(() => {
      return orderDetailState?.history?.data?.data?.records || []
    })

    onMounted(async () => {
      await store.dispatch('priceCompensation/fetchCompensationHistory', {
        vid: vid.value,
      })
    })

    return {
      vid,
      orderDetailState,
      actionState,
      dataSource,
      columns,
    }
  }
})
</script>