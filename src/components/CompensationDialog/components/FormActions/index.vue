<template>
  <template v-if="['OrderCompensationView'].includes(actionState.type)">
    <a4-button
      size="small"
      @click="handleCancel"
      :disabled="
        orderDetailState.status === 'PENDING'
      "
    >关闭</a4-button>
  </template>

  <template v-else-if="['OrderCompensationAudit'].includes(actionState.type)">
    <template v-if="['form'].includes(activeKey)">
      <a4-button
        size="small"
        @click="handleCancel"
        :disabled="
          orderDetailState.status === 'PENDING'
          || orderDetailState?.updatePriceCompensationRecord?.status === 'PENDING'
        "
      >取消</a4-button>
      
      <a4-button
        v-if="
          orderDetailState?.status === 'FULFILLED'
        "
        size="small"
        danger
        @click="handleAudit({
          priceProtectionId: actionState.record?.priceProtectionId,
          status: 'REJECTED'
        })"
        :disabled="
          orderDetailState.status === 'PENDING'
          || orderDetailState?.updatePriceCompensationRecord?.status === 'PENDING'
          || !roles.includes('price-protection-approve')
        "
      >审核驳回</a4-button>

      <a4-button
        v-if="
          orderDetailState?.status === 'FULFILLED'
        "
        type="primary"
        size="small"
        @click="
          handleAudit({
            priceProtectionId: actionState.record?.priceProtectionId,
            status: 'APPROVED'
          })
        "
        :disabled="
          orderDetailState.status === 'PENDING'
          || orderDetailState?.updatePriceCompensationRecord?.status === 'PENDING'
          || !roles.includes('price-protection-approve')
        "
      >审核通过</a4-button>
    </template>
    <template v-else>
      <a4-button
        size="small"
        @click="handleCancel"
        :disabled="
          orderDetailState.status === 'PENDING'
        "
      >关闭</a4-button>
    </template>
  </template>

  <template v-else>
    <template v-if="['form'].includes(activeKey)">
      <a4-button
        size="small"
        @click="handleCancel"
        :disabled="
          orderDetailState.status === 'PENDING'
        "
      >取消</a4-button>

      <a4-button
        size="small"
        type="primary"
        @click="handleSubmit"
        :disabled="
          orderDetailState.status === 'PENDING'
          || orderDetailState?.refund.status === 'PENDING'
        "
        :loading="
          orderDetailState?.refund.status === 'PENDING'
        "
        v-if="
          orderDetailState?.priceCompare?.formState?.priceCanProtect
        "
      >确定</a4-button>
    </template>
    <template v-else>
      <a4-button
        size="small"
        @click="handleCancel"
        :disabled="
          orderDetailState.status === 'PENDING'
        "
      >关闭</a4-button>
    </template>
  </template>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { defineComponent, ref, reactive, watch, onMounted, computed, toRaw, createVNode } from 'vue'
import { useStore } from 'vuex'
import { Button as A4Button, Modal } from 'ant-design-vue-4';

export default defineComponent({
  components: {
    A4Button,
  },
  // props: ['formState', 'formView', 'actionState', 'source'],
  setup(props, { emit }) {
    const store = useStore();

    const {
      userState
    } = store.state.global

    const {
      tabsState,
      orderDetailState,
      actionState,
      // couponRefundState,
    } = store.state.priceCompensation;

    const roles = computed(() => {
      return userState?.data?.data?.roles || []
    })

    const activeKey = computed(() => tabsState.activeKey)

    const handleCancel = () => {
      eventBus.emit('price-compensation:compensation-apply-form:cancel-form');
    }
    
    const handleSubmit = () => {
      eventBus.emit('price-compensation:compensation-apply-form:submit-form');
    }
    
    const handleAudit = (params) => {
      eventBus.emit('price-compensation:compensation-apply-form:audit-form', params);
    }

    return {
      activeKey,
      actionState,
      orderDetailState,
      // couponRefundState,
      handleSubmit,
      handleCancel,
      handleAudit,
      roles,
    }
  }
})
</script>