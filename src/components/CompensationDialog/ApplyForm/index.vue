<template>
  <!-- {{ orderDetailState?.priceCompare?.formState?.paymentGatewayReferenceList }} -->
  <!-- {{ orderDetailState?.compensationApply?.formState }} -->
  <a4-spin
    :spinning="
      orderDetailState?.status === 'PENDING'
      || orderDetailState?.refund?.status === 'PENDING'
    "
  >
    <div v-if="
      orderDetailState?.data?.code !== '0'"
    >
      <a4-result
        status="error"
        v-if="orderDetailState?.status === 'REJECTED'"
        :sub-title="orderDetailState?.data?.msg"
      />
    </div>
    <div class="form-wrapper" v-else>
      <!-- {{ actionState?.type }} -->
      <a4-form
        ref="formRef"
        autocomplete="off"
        :model="orderDetailState?.priceCompare?.formState"
        :rules="rules"
      >
        <div style="margin: 1rem 0;">
          <a4-row>
            <a4-col :span="6" class="col col-l">
              <!-- left part -->
              <left-part />
            </a4-col>
            <a4-col :span="18" class="col col-r">
              <!-- right part -->
              <right-part />
            </a4-col>
          </a4-row>
        </div>
      </a4-form>
    </div>
  </a4-spin>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import { useForm } from '@/composables/price-compensation/compensation-apply/useForm';

import {
  Spin as A4Spin,
  Row as A4Row,
  Col as A4Col,
  Alert as A4Alert,
  Form as A4Form,
  FormItem as A4FormItem,
  Result as A4Result,
} from 'ant-design-vue-4'

import {
  LeftPart,
  RightPart,
} from './Parts/index'

export default defineComponent({
  name: 'ApplyForm',
  components: {
    A4Spin,
    A4Row,
    A4Col,
    A4Alert,
    A4Form,
    A4FormItem,
    A4Result,
    LeftPart,
    RightPart,
  },
  setup(props) {
    const store = useStore();
    const {
      orderDetailState,
      actionState,
    } = store.state.priceCompensation;

    const {
      formRef,
      rules,
      validatedTotalRefundAmount,
    } = useForm(props);

    return {
      formRef,
      rules,
      orderDetailState,
      actionState,
      validatedTotalRefundAmount,
    }
  }
})
</script>

<style lang="scss" scoped>
.form-wrapper {
  :deep(.ant-form-item) {
    margin-bottom: .2rem;
  }
}

.col {
  padding: 0 1rem;

  &-r {
    border-left: 1px solid #eaeaea;
  }
}
</style>
