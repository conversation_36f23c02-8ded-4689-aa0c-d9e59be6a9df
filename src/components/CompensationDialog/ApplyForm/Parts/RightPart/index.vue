<template>
  <!-- {{ storeList }} -->
  <!-- {{ orderDetailState?.priceCompare?.formState }} -->
  <a4-spin :spinning="orderDetailState.priceCompare?.status === 'PENDING'">
    <div class="form-items-wrapper">
      <h3>比价结果 {{ storeInfo?.storeNameCn ? `vs ${storeInfo?.storeNameCn}` : `${ compareStore ? `vs ${compareStore}` : '' }` }}</h3>

      <a4-alert
        type="warning"
        style="margin-bottom: 8px;"
        v-if="priceCompareMsg && priceCompareCode === '0'"
      >
        <template #message>
          <span style="color: #f00;">{{ priceCompareMsg }}</span>
        </template>
      </a4-alert>

      <div class="form-item">
        <a4-result
          v-if="!compareStore"
          status="info"
          title="选择对比渠道进行比价"
          sub-title=""
        />
        
        <a4-result v-else-if="
          priceCompareData?.code
          && priceCompareData?.code !== '0'
          && orderDetailState.priceCompare?.status !== 'PENDING'
        "
          status="warning"
          :title="undefined"
        >
          <template #subTitle>
            <a4-alert type="warning">
              <template #message>
                <strong style="color: #f00;">{{ priceCompareData?.msg }}</strong>
              </template>
            </a4-alert>
          </template>
        </a4-result>
        <div v-else>
          <div class="form-item">
            <a4-row style="width: 100%;">
              <a4-col :span="8">
                <sac-id />
              </a4-col>
              <a4-col :span="8">
                <price-can-protect />
              </a4-col>
              <a4-col :span="8">
                <refund-reason />
              </a4-col>
            </a4-row>
          </div>
          <!-- <div class="form-item">
            <a4-row style="width: 100%;">
              <a4-col :span="12">
                <refund-reason />
              </a4-col>
            </a4-row>
          </div> -->

          <div class="form-item">
            <h4>价保明细</h4>
            <item-details />
          </div>

          <div class="form-item">
            <h4>支付记录</h4>
            <payment-details />
          </div>

          <!-- <a4-tabs size="small" v-model:activeKey="activeKey">
            <a4-tab-pane key="item-details" tab="价保明细">
              <item-details />
            </a4-tab-pane>
            <a4-tab-pane key="payment-details" tab="支付记录">
              <payment-details />
            </a4-tab-pane>
          </a4-tabs> -->

          <!-- <item-details /> -->
        </div>
      </div>
    </div>
  </a4-spin>
</template>

<script lang="ts">
import { defineComponent, computed, ref } from 'vue'
import { useStore } from 'vuex'
import { find } from 'lodash'

import {
  Row as A4Row,
  Col as A4Col,
  Result as A4Result,
  Alert as A4Alert,
  Spin as A4Spin,
  Tabs as A4Tabs,
  TabPane as A4TabPane,
} from 'ant-design-vue-4';

import {
  ItemDetails,
  PaymentDetails,
  PriceCanProtect,
  SacId,
  RefundReason,
} from '../../FormItems/index'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    A4Result,
    A4Alert,
    A4Spin,
    A4Tabs,
    A4TabPane,
    ItemDetails,
    PaymentDetails,
    PriceCanProtect,
    SacId,
    RefundReason,
  },
  setup() {
    const activeKey = ref('item-details');
    const store = useStore();
    const {
      orderDetailState,
    } = store.state.priceCompensation;

    const { searchPermissionStoreState } = store.state.oms;

    const stores = computed(() => {
      return searchPermissionStoreState?.data?.data?.permissionStoreList || []
    })

    const compareStore = computed(() => {
      return orderDetailState?.compensationApply?.formState?.compareStore
    })

    const storeList = computed(() => {
      return orderDetailState?.compensationApply?.formState?.storeList || []
    })

    const storeInfo = computed(() => {
      const _store = find(stores.value, { storeNo: compareStore.value })
      // debugger
      return _store
    })

    const priceCompareCode = computed(() => {
      return orderDetailState?.priceCompare?.data?.code
    })

    const priceCompareMsg = computed(() => {
      return orderDetailState?.priceCompare?.data?.msg
    })

    const priceCompareData = computed(() => {
      return orderDetailState?.priceCompare?.data
    })

    return {
      activeKey,
      compareStore,
      orderDetailState,
      priceCompareData,
      storeList,
      storeInfo,
      stores,
      priceCompareCode,
      priceCompareMsg,
    }
  }
})
</script>

<style lang="scss" scoped>
h3 {
  font-size: 16px;
}

h4 {
  font-size: 14px;
}

.form-items-wrapper{
  :deep(.ant-tabs-nav) {
    margin: 0 !important;
  }
}
</style>