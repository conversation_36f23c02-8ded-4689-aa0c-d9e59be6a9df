<template>
  <div class="form-items-wrapper">
    <!-- 订单编号 -->
    <vid />

    <gross-amount />

    <contact-name />
    
    <contact-phone />

    <apply-time />

    <pay-due-time />

    <!-- <div class="wrapper">
      <div class="form-item">
        <a4-row style="width: 100%;">
          <a4-col :span="12">
            <contact-phone />
          </a4-col>
          <a4-col :span="12">
            <contact-name />
          </a4-col>
        </a4-row>
      </div>

      <div class="form-item">
        <a4-row style="width: 100%;">
          <a4-col :span="12">
            <apply-time />
          </a4-col>
          <a4-col :span="12">
            <pay-due-time />
          </a4-col>
        </a4-row>
      </div>
    </div> -->

    <compare-store-no />

  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'

import {
  Row as A4Row,
  Col as A4Col,
} from 'ant-design-vue-4'

import {
  Vid,
  ContactPhone,
  ContactName,
  ApplyTime,
  PayDueTime,
  GrossAmount,
  CompareStoreNo,
} from '../../FormItems/index'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    Vid,
    ContactPhone,
    ContactName,
    ApplyTime,
    PayDueTime,
    GrossAmount,
    CompareStoreNo,
  },
  setup() {
    const store = useStore();
    return {
    }
  }
})
</script>

<style lang="scss" scoped>
.wrapper {
  border: 1px solid #e8e8e8;
  margin: 8px 0;
  padding: 8px 0;
}
</style>