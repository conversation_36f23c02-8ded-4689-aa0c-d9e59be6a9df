<template>
  <div class="wrapper">
    <a4-table
      size="small"
      :columns="columns"
      :dataSource="paymentDetails"
      :pagination="false"
      :defaultExpandAllRows="true"
      :indentSize="8"
    >
      <template #headerCell="{ column }">
        <span style="white-space: nowrap;">
          {{ column.title }}
        </span>
      </template>
      <template #bodyCell="{ record, column, index }">
        <template v-if="column.key === 'originAmount'">
          <ik-price v-if="record.originAmount" :price="record.originAmount" />
        </template>
        <template v-if="column.key === 'refundedAmount'">
          <ik-price v-if="record.refundedAmount" :price="record.refundedAmount" />
        </template>
        <template v-if="column.key === 'refundAmount'">
          <!-- {{ record?.paymentGatewayReferenceId }} -->
          <!-- ---- {{ index }} ---- -->
          <refund-amount
            v-if="record?.paymentGatewayReferenceId"
            :record="record"
            :paymentGatewayReferenceId="record.paymentGatewayReferenceId"
            :index="index"
          />
        </template>
      </template>
    </a4-table>

    <a4-alert
      show-icon
      style="margin: 8px 0;"
      v-if="validateResult?.totalRefundAmount"
      type="error"
    >
      <template #message>
        <span style="color: #ff0000;">{{ validateResult?.totalRefundAmount?.message }}</span>
      </template>
    </a4-alert>
  </div>

  <div class="form-item" style="display: none;">
    <a4-row style="width: 100%;">
      <a4-col :span="12">
        <a4-form-item
          label="退款总金额"
          ref="totalRefundAmount"
          name="totalRefundAmount"
        >
          <span style="white-space: nowrap;">
            <a4-input
              size="small"
              v-model:value="totalRefundAmount"
            />
          </span>
        </a4-form-item>
      </a4-col>
    </a4-row>
  </div>
  
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Col as A4Col,
  Input as A4Input,
  FormItem as A4FormItem,
  Table as A4Table,
  Alert as A4Alert,
} from 'ant-design-vue-4'
import IkPrice from '@/stories/components/Price/index.vue'
import RefundAmount from './RefundAmount/index.vue'
import columns from './columns'
import {
  innerLayout,
} from '../config'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    A4FormItem,
    A4Table,
    A4Input,
    A4Alert,
    IkPrice,
    RefundAmount,
  },
  setup() {
    const store = useStore();

    const {
      orderDetailState,
    } = store.state.priceCompensation;

    const validateResult = computed(() => {
      return orderDetailState?.priceCompare?.validateResult
    })

    const paymentDetails = computed(() => {
      return orderDetailState?.priceCompare?.formState?.paymentDetails
    })

    const paymentGatewayReferenceList = computed(() => {
      return orderDetailState?.priceCompare?.formState?.paymentGatewayReferenceList || []
    })

    const totalRefundAmount = computed(() => {
      return paymentGatewayReferenceList.value.reduce((acc, cur) => {
        return acc + (cur.refundAmount || 0)
      }, 0)
    })

    // const handleTotalRefundAmountChange = (e) => {
    //   debugger
    //   console.log('handleTotalRefundAmountChange', e.target.value)
    // }

    return {
      columns,
      orderDetailState,
      innerLayout,
      paymentDetails,
      totalRefundAmount,
      validateResult,
      // handleTotalRefundAmountChange,
    }
  },
})
</script>
<style lang="scss" scoped>
.wrapper {
  margin: 8px 0;
  position: relative;
  
  .form-item {
    position: absolute;
    top: -29px;
    left: 4rem;
    z-index: 1;
  }
}
</style>