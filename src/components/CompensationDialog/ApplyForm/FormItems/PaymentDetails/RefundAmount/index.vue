<template>
  <template v-if="actionState.type === 'OrderCompensationView'">
    <ik-price v-if="refundAmount" :price="refundAmount" />
  </template>
  <a4-input-number
    v-else
    size="small"
    style="width: 160px"
    v-model:value="refundAmount"
    :step="0.01"
    :controls="false"
    :placeholder="record?.canRefundAmount?.toString()"
    :max="record?.canRefundAmount"
    :min="0"
    @change="handleChange"
    :disabled="
      ['OrderCompensationView', 'OrderCompensationAudit'].includes(actionState.type)
    "
  />
</template>

<script lang="ts">
import { find } from 'lodash'
import { defineComponent, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
  InputNumber as A4InputNumber,
} from 'ant-design-vue-4'
import IkPrice from '@/stories/components/Price/index.vue'
import { eventBus } from '@/eventBus';
// import {
//   innerLayout,
// } from '../config'

export default defineComponent({
  props: ['record', 'paymentGatewayReferenceId', 'index'],
  components: {
    A4Row,
    A4Col,
    A4FormItem,
    A4InputNumber,
    IkPrice,
  },
  setup(props) {
    const store = useStore();

    const {
      orderDetailState,
      actionState,
    } = store.state.priceCompensation;

    const paymentGatewayReferenceList = computed(() => {
      return orderDetailState?.priceCompare?.formState?.paymentGatewayReferenceList
    })

    const paymentGatewayReference = computed(() => {
      return find(paymentGatewayReferenceList.value, { paymentGatewayReferenceId: props.paymentGatewayReferenceId })
    })

    const refundAmount = computed(() => {
      return paymentGatewayReference?.value?.refundAmount
    })

    const handleChange = (v) => {
      const _paymentGatewayReferenceList = JSON.parse(JSON.stringify(paymentGatewayReferenceList.value))
      _paymentGatewayReferenceList[props.index].refundAmount = v || undefined
      store.commit('priceCompensation/updateCompensationPriceCompareFormState', {
        paymentGatewayReferenceList: _paymentGatewayReferenceList
      })
      eventBus.emit('price-compensation:compensation-apply-form:form-validate-totalRefundAmount')
    }

    // watch(() => orderDetailState?.compensationApply?.formState?.storeNo, (storeNo) => {
    //   setTimeout(() => {
    //     handleChange(props?.record?.canRefundAmount)
    //   }, 1000)
    // })

    onBeforeUnmount(() => {
    })

    return {
      actionState,
      orderDetailState,
      // innerLayout,
      refundAmount,
      handleChange,
      paymentGatewayReference,
      paymentGatewayReferenceList,
    }
  },
})
</script>
<style lang="scss" scoped>
</style>