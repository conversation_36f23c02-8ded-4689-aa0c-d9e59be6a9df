<template>
  <div class="form-item">
    <a4-row style="width: 100%;">
      <a4-col :span="12">
        <a4-form-item
          label="支付时间"
          ref="payDueTime"
          name="payDueTime"
        >
          <span style="white-space: nowrap;">
            {{ payDueTime }}
          </span>
        </a4-form-item>
      </a4-col>
    </a4-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
import {
  innerLayout,
} from '../config'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    A4FormItem,
  },
  setup() {
    const store = useStore();

    const {
      orderDetailState,
    } = store.state.priceCompensation;

    const payDueTime = computed(() => {
      return orderDetailState?.compensationApply?.formState?.payDueTime
    })

    return {
      orderDetailState,
      innerLayout,
      payDueTime,
    }
  },
})
</script>
<style lang="scss" scoped>
</style>