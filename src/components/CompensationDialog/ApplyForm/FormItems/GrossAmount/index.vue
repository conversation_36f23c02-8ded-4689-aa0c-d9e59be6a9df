<template>
  <div class="form-item">
    <!-- 订单金额: {{ grossAmount }} -->

    <a4-row style="width: 100%;">
      <a4-col :span="12">
        <a4-form-item
          label="订单金额"
          ref="grossAmount"
          name="grossAmount"
        >
          <span style="white-space: nowrap;">
            <ik-price :price="grossAmount" />
          </span>
        </a4-form-item>
      </a4-col>
    </a4-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
import IkPrice from '@/stories/components/Price/index.vue'
import {
  innerLayout,
} from '../config'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    A4FormItem,
    IkPrice,
  },
  setup() {
    const store = useStore();

    const {
      orderDetailState,
    } = store.state.priceCompensation;

    const grossAmount = computed(() => {
      return orderDetailState?.compensationApply?.formState?.grossAmount
    })

    return {
      orderDetailState,
      innerLayout,
      grossAmount,
    }
  },
})
</script>
<style lang="scss" scoped>
.form-item {
  // position: absolute;
  // right: 16px; top: 4px;
}
</style>