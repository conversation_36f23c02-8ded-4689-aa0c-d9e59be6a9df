<template>
  <div class="form-item">
    <a4-row style="width: 100%;">
      <a4-col :span="12">
        <a4-form-item
          label="补偿金额"
          ref="priceCanProtect"
          name="priceCanProtect"
        >
          <span style="white-space: nowrap;">
            <ik-price :price="priceCanProtect" />
          </span>
        </a4-form-item>
      </a4-col>
    </a4-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
import IkPrice from '@/stories/components/Price/index.vue'
import {
  innerLayout,
} from '../config'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    A4FormItem,
    IkPrice,
  },
  setup() {
    const store = useStore();

    const {
      orderDetailState,
    } = store.state.priceCompensation;

    const priceCanProtect = computed(() => {
      return orderDetailState?.priceCompare?.formState?.priceCanProtect
    })

    return {
      orderDetailState,
      innerLayout,
      priceCanProtect,
    }
  },
})
</script>
<style lang="scss" scoped>
</style>