<template>
  <div class="wrapper">
    <a4-table
      v-if="itemDetails?.length"
      size="small"
      :columns="columns"
      :dataSource="itemDetails"
      :pagination="false"
      :defaultExpandAllRows="true"
      :indentSize="8"
      :rowKey="record => record.productNo"
      @expandedRowsChange="handleExpandedRowsChange"
      @expand="handleExpand"
    >
      <template #headerCell="{ column }">
        <span style="white-space: nowrap;">
          {{ column.title }}
        </span>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'differentPriceSingle'">
          <ik-price v-if="record.differentPriceSingle" :price="record.differentPriceSingle" />
        </template>
        <template v-if="column.key === 'differentPriceTotal'">
          <ik-price v-if="record.differentPriceTotal" :price="record.differentPriceTotal" />
        </template>
      </template>
    </a4-table>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
  Table as A4Table,
} from 'ant-design-vue-4'
import IkPrice from '@/stories/components/Price/index.vue'
import columns from './columns'
import {
  innerLayout,
} from '../config'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    A4FormItem,
    A4Table,
    IkPrice,
  },
  setup() {
    const store = useStore();

    const {
      orderDetailState,
    } = store.state.priceCompensation;

    const itemDetails = computed(() => {
      return orderDetailState?.priceCompare?.formState?.itemDetails
    })

    const handleExpandedRowsChange = (expandedRows: any) => {
      console.log('expandedRows:::', expandedRows)
    }

    const handleExpand = (expanded: boolean, record: any) => {
      console.log('expanded:::', expanded)
      console.log('record:::', record)
    }

    return {
      columns,
      orderDetailState,
      innerLayout,
      itemDetails,
      handleExpandedRowsChange,
      handleExpand,
    }
  },
})
</script>
<style lang="scss" scoped>
.wrapper {
  margin: 8px 0;
  
  .form-item {
    position: absolute;
    right: 16px; top: 4px;
  }
}
</style>