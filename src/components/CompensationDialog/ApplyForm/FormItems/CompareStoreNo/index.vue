<template>
  <div class="form-item">
    <a4-row style="width: 100%;">
      <a4-col :span="12">
        <a4-form-item
          label="对比渠道"
          ref="compareStoreNo"
          name="compareStoreNo"
        >
          <span style="white-space: nowrap;">
            <span v-if="['OrderCompensationView', 'OrderCompensationAudit'].includes(actionState.type)">
              {{
                find(stores, { storeNo: compareStore })
                  ? find(stores, { storeNo: compareStore })?.storeNameCn
                  : compareStore
              }}
            </span>
            <a4-select
              v-else
              allowClear
              size="small"
              placeholder="请选择"
              style="width: 172px;"
              @change="handleCompareStoreNoChange"
            >
              <a4-select-option
                v-for="store in storeList"
                :value="store.storeNo"
              >
                {{ store.storeName }}
              </a4-select-option>
            </a4-select>
          </span>
        </a4-form-item>
      </a4-col>
    </a4-row>
  </div>
</template>

<script lang="ts">
import { eventBus } from '@/eventBus';
import { find } from 'lodash'
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
  Select as A4Select,
  SelectOption as A4SelectOption,
} from 'ant-design-vue-4'
import {
  innerLayout,
} from '../config'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    A4FormItem,
    A4Select,
    A4SelectOption,
  },
  setup() {
    const store = useStore();

    const {
      orderDetailState,
      actionState,
    } = store.state.priceCompensation;

    const { searchPermissionStoreState } = store.state.oms;

    const storeList = computed(() => {
      return orderDetailState?.compensationApply?.formState?.storeList
    })

    const storeNo = computed(() => {
      return orderDetailState?.compensationApply?.formState?.storeNo
    })

    const compareStore = computed(() => {
      return orderDetailState?.compensationApply?.formState?.compareStore
    })

    const stores = computed(() => {
      return searchPermissionStoreState?.data?.data?.permissionStoreList || []
    })

    const handleCompareStoreNoChange = (value: string) => {
      console.log('handleCompareStoreNoChange...', value)
      store.commit('priceCompensation/resetCompensationPriceCompareFormValidateResult')
      store.commit('priceCompensation/updateCompensationApplyFormState', {
        compareStore: value,
      })
      eventBus.emit('price-compensation:compensation-apply-form:price-compare');
    }

    return {
      find,
      actionState,
      orderDetailState,
      innerLayout,
      storeList,
      storeNo,
      compareStore,
      handleCompareStoreNoChange,
      stores,
    }
  },
})
</script>
<style lang="scss" scoped>
</style>