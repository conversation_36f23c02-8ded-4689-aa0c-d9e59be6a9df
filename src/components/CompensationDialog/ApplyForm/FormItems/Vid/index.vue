<template>
  <h3>订单号: {{ vid }}</h3>

  <div class="form-item" style="display: none;">
    <a4-row style="width: 100%;">
      <a4-col :span="12">
        <a4-form-item
          label="订单号"
          ref="vid"
          name="vid"
        >
          <span style="white-space: nowrap;">
            {{ vid }}
          </span>
        </a4-form-item>
      </a4-col>
    </a4-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useStore } from 'vuex'
import {
  Row as A4Row,
  Col as A4Col,
  FormItem as A4FormItem,
} from 'ant-design-vue-4'
import {
  innerLayout,
} from '../config'

export default defineComponent({
  components: {
    A4Row,
    A4Col,
    A4FormItem,
  },
  setup() {
    const store = useStore();

    const {
      orderDetailState,
    } = store.state.priceCompensation;

    const vid = computed(() => {
      return orderDetailState?.compensationApply?.formState?.vid
    })

    return {
      orderDetailState,
      innerLayout,
      vid,
    }
  },
})
</script>
<style lang="scss" scoped>
h3 {
  font-size: 16px;
  font-weight: 500;
}
</style>