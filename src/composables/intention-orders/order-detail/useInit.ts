import { ref, watch, computed, onMounted, createVNode } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { eventBus } from '@/eventBus';

export function useInit(props) {
  const store = useStore();
  const route = useRoute();

  const {
    actionState,
    orderDetailState,
  } = store.state.intentionOrders;

  const record = computed(() => {
    return actionState.record
  })

  const actionStateType = computed(() => {
    return actionState.type
  })

  const open = computed(() => {
    return !!record.value
  })

  const orderDetail = computed(() => {
    return orderDetailState?.data?.data || {}
  })

  const serviceType = computed(() => {
    return record.value?.serviceType
  })

  eventBus.off('intention-order-detail-drawer:close-drawer')
  eventBus.on('intention-order-detail-drawer:close-drawer', () => {
    store.commit('intentionOrders/updateActionState', {
      type: '',
      record: undefined,
    })
  })

  const handleOpenChange = (_open) => {
    if (_open) {
      console.log(actionStateType.value)
      store.dispatch('intentionOrders/fetchOrderDetail', {
        orderNo: record.value.orderNo,
      })
    } else {

    }
  }

  const handleClose = () => {
    eventBus.emit('intention-order-detail-drawer:close-drawer')
  }

  return {
    record,
    open,
    serviceType,
    actionStateType,
    handleOpenChange,
    handleClose,
    orderDetailState,
    orderDetail,
    actionState,
  }
}