import { ref, watch, computed, onMounted, createVNode } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { eventBus } from '@/eventBus';

export function useInit(props) {
  const store = useStore();
  const route = useRoute();

  const role = ref('intention-orders')

  const globalState = computed(() => store.state.global.globalState)
  const userState = computed(() => store.state.global.userState)
  const roles = computed(() => userState.value?.data?.data?.roles ?? [])
  const modules = computed(() => globalState.value.intentionOrders.modules)
  const system = computed(() => globalState.value.intentionOrders.system)
  const openKeys = computed(() => [])
  const selectedKeys = ref(['intention-orders'])

  const hasPermission = computed(() => {
    return roles.value.includes(role.value)
  })

  const module = computed(() => {
    return {
      key: 'intention-orders',
      title: 'GP意向单管理',
    }
  })

  onMounted(async () => {
    // await store.dispatch('oms/searchPermissionStore')
    store.dispatch('global/getCurrentUser')
      .then(async (_userState) => {
        hasPermission.value && 
        eventBus.emit('intention-orders-search:form-view:reset-form')
      })
  })

  return {
    role,
    roles,
    globalState,
    userState,
    modules,
    module,
    system,
    openKeys,
    selectedKeys,
    hasPermission,
  }
}