import { ref, watch, computed, onMounted, createVNode } from 'vue';
import { useStore } from 'vuex';
import { eventBus } from '@/eventBus';
// import { useRoute } from 'vue-router';
import { message, Modal as A4Modal } from 'ant-design-vue-4';

export function useForm(props) {
  const store = useStore();
  const {
    orderDetailState,
    actionState,
  } = store.state.intentionOrders;

  const formRef = ref();

  const orderNo = computed(() => {
    return actionState.record.orderNo
  })

  const preCreateDAFormState = computed(() => {
    return orderDetailState?.preCreateDA?.formState
  })

  const handleSubmit = async () => {
    await formRef.value.validate()
      .then((ret) => {
        console.log('ret:', ret)
        const params = {
          orderNo: orderNo.value
        }

        if (preCreateDAFormState.value?.timeWindowId) {
          params.deliveryIdAndWindowIdMap = preCreateDAFormState.value?.timeWindowId
          params.solutionId = preCreateDAFormState.value?.solutionId
          params.deliveryArrangementId = preCreateDAFormState.value?.deliveryArrangementId
        }

        store.dispatch('intentionOrders/distribution', params).then((res) => {
          // 
          if (res.code === '0') {
            message.success('创建成功！', 0.9, () => {
              eventBus.emit('intention-order:delivery-confirm-form:cancel-form')
              eventBus.emit('intention-orders-search:form-view:submit-form');
            })
          } else {
            message.error(res.message || '系统异常，创建失败！', 0.9, () => {
              // eventBus.emit('intention-order:delivery-confirm-form:cancel-form')
              eventBus.emit('intention-orders-search:form-view:submit-form');
            })
          }
        }).catch((err) => {
          // message.error(err?.message)
          // debugger
          // eventBus.emit('intention-order:delivery-confirm-form:cancel-form')
        })
      })
      .catch((err) => {
        console.log(err);
      });
  }

  eventBus.off('intention-order:delivery-confirm-form:submit-form')
  eventBus.on('intention-order:delivery-confirm-form:submit-form', handleSubmit);

  return {
    formRef,
    handleSubmit,
  }
}