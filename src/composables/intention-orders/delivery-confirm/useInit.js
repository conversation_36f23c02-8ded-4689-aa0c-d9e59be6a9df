import { ref, watch, computed, onMounted, createVNode } from 'vue';
import { useStore } from 'vuex';
import { eventBus } from '@/eventBus';
// import { useRoute } from 'vue-router';
// import { message, Modal } from 'ant-design-vue-4';

export function useInit(props) {
  const store = useStore();
  
  const handleCancel = () => {
    store.commit('intentionOrders/resetActionState')
    store.commit('intentionOrders/resetPreCreateDAFormState')
    eventBus.emit('intention-orders-search:form-view:submit-form')
  }

  eventBus.off('intention-order:delivery-confirm-form:cancel-form')
  eventBus.on('intention-order:delivery-confirm-form:cancel-form', handleCancel);

  return {
    handleCancel,
  }
}