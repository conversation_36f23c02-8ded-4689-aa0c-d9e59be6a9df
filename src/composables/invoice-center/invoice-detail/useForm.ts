import { ref, watch, computed, onMounted, createVNode } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { eventBus } from '@/eventBus';
import { message } from 'ant-design-vue-4';
import { RegRules } from '@/consts';
import { remove, find } from 'lodash'
import Decimal from 'decimal.js'
import {
  PLACEHOLDER_ITEM_ID,
  isSameItem,
  hasPlaceholderItem,
} from '@/utils/goods-supplement'

export function useForm(props) {
  const store = useStore();
  const route = useRoute();

  // 表单引用
  const formRef = ref();

  const {
    actionState,
    invoiceDetailState,
  } = store.state.invoiceCenter;

  const record = computed(() => {
    return actionState.record
  })

  const storeCode = computed(() => {
    return record.value?.storeCode
  })

  const invoiceDetailData = computed(() => {
    return invoiceDetailState?.data?.data
  })

  const manualFormState = computed(() => {
    return invoiceDetailState?.manualInvoicing?.formState
  })

  const invoiceTitleType = computed(() => {
    return invoiceDetailState?.manualInvoicing?.formState?.invoiceTitleType;
  })

  const isNaturalPerson = computed(() => {
    return invoiceDetailState?.manualInvoicing?.formState?.isNaturalPerson;
  })

  const itemLineAvailableList = computed(() => {
    return invoiceDetailState?.manualInvoicing?.formState?.itemLineAvailableList || []
  })

  const itemLineRemovedList = computed(() => {
    return invoiceDetailState?.manualInvoicing?.formState?.itemLineRemovedList || []
  })

  const itemLineSelected = computed(() => {
    return invoiceDetailState?.manualInvoicing?.formState?.itemLineSelected
  })

  const invoiceAmount = computed(() => {
    const amount = itemLineAvailableList.value.reduce((acc, itemLine) => {
      // return acc + (itemLine?.netAmount || 0)
      const netAmount = new Decimal(itemLine?.price || 0).mul(itemLine?.quantity || 0)
      return new Decimal(acc).add(
        (new Decimal(netAmount).minus(itemLine?.discountAmount || 0))
      )
    }, 0)
    return amount
  })

  const isValidBuyerTaxNo = (e: KeyboardEvent, v: string) => {
    if (invoiceTitleType.value === 'PERSON' && isNaturalPerson.value) {
      if (/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(v)) {
        return Promise.resolve()
      } else {
        return Promise.reject('请输入正确的身份证号码')
      }
    } else {
      if (!v) {
        return Promise.reject('请输入纳税人识别号')
      } else {
        if (/^[A-Za-z0-9]{15,20}$/.test(v)) {
          return Promise.resolve()
        } else {
          return Promise.reject('请输入正确的纳税人识别号')
        }
      }
    }
  }

  const isValidBankAccount = (e, v) => {
    if (!v) return Promise.resolve()
    // if (/^([1-9]{1})(\d{15,18})$/.test(v)) {
    //   return Promise.resolve()
    // } else {
    //   return Promise.reject('请输入正确的银行卡号')
    // }
    return Promise.resolve()
  }

  const isValidContactPhone = (e, v) => {
    if (!v) return Promise.resolve()
    if (RegRules.TELRe.test(v) || RegRules.MOBILERe.test(v)) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入正确的电话号码')
    }
  }

  const isValidMailAccount = (e, v) => {
    if (!v) return Promise.resolve()
    if (RegRules.MAILRe.test(v)) {
      return Promise.resolve()
    } else {
      return Promise.reject('请输入正确的收票邮箱')
    }
  }

  const isValidFilterText = (e, v) => {
    if (v?.trim()) {
      return Promise.reject('提交表单请请空过滤关键词')
    }
    return Promise.resolve()
  }
  

  const isValidItemLineAvailableList = (e, v) => {
    const data = (v || []).filter((item) => {
      return item?.id !== -1
    })
    if (data.length > 0) {
      const hasErrorItem = find(data, { hasError: true })
      if (hasErrorItem) {
        return Promise.reject('请检查商品/服务名不能为空，优惠金额不超过含税金额')
      } else {
        // 原单剩余可开票的总金额
        const _invoiceAmount = new Decimal(invoiceDetailData.value?.paymentAmount).minus(invoiceDetailData.value?.paymentDiscountAmount)
        // 开票金额不能大于原单剩余可开票的总金额
        if (invoiceAmount.value.minus(_invoiceAmount) > 0) {
          return Promise.reject(`开票金额(${invoiceAmount.value})不能大于原单剩余可开票的总金额(${_invoiceAmount})`)
        } else {
          return Promise.resolve()
        }
      }      
    } else {
      return Promise.reject('请至少选择一个商品！')
    }
  }

  const isValidRemark = (e, v) => {
    if (v?.trim()) {
      if (v?.trim()?.length > 500) {
        return Promise.reject('备注不能超过500个字')
      } else {
        return Promise.resolve()
      }
    }
    return Promise.resolve()
  }

  const rules = {
    buyerName: [
      { required: true, message: '请输入发票抬头', trigger: 'blur' },
    ],
    buyerAddress: [
      { required: false, message: '请输入地址', trigger: 'blur' },
    ],
    buyerTaxNo: [
      { required: true, validator: isValidBuyerTaxNo, trigger: 'blur' },
    ],
    bankName: [
      { required: false, message: '请输入开户行', trigger: 'blur' },
    ],
    bankAccount: [
      { required: false, validator: isValidBankAccount, trigger: 'blur' },
    ],
    contactPhone: [
      { required: false, validator: isValidContactPhone, trigger: 'blur' },
    ],
    mailAccount: [
      { required: false, validator: isValidMailAccount, trigger: 'blur' },
    ],
    filterText: [
      { required: true, validator: isValidFilterText, trigger: 'blur' },
    ],
    itemLineAvailableList: [
      { required: true, validator: isValidItemLineAvailableList, trigger: 'blur' },
    ],
    remark: [
      { required: false, validator: isValidRemark, trigger: 'blur' },
    ],
  }

  const handleSubmit = () => {
    if (formRef.value) {
      formRef.value.validate()
        .then((ret) => {
          const data = {
            storeCode: storeCode.value,
            orderId: manualFormState.value?.orderId,
            iSellId: manualFormState.value?.iSellId,
            invoiceTitleType: manualFormState.value?.invoiceTitleType,
            buyerName: manualFormState.value?.buyerName,
            invoiceType: manualFormState.value?.invoiceType,
            buyerTaxNo: manualFormState.value?.buyerTaxNo,

            bankName: manualFormState.value?.bankName,
            buyerAddress: manualFormState.value?.buyerAddress,
            bankAccount: manualFormState.value?.bankAccount,

            phone: manualFormState.value?.contactPhone,
            email: manualFormState.value?.mailAccount,

            invoiceAmountDetails: (manualFormState.value?.itemLineAvailableList || []).filter((item) => {
              return item?.id !== -1 && item?.canInvoiceQuantity !== 0
            }),

            remark: manualFormState.value?.remark,
            isShowSellerBank: manualFormState.value?.isShowSellerBank,
            isShowBuyerBank: manualFormState.value?.isShowBuyerBank,

            clientSystem: manualFormState.value?.clientSystem,
          }
          // console.log('manualFormState:::', manualFormState.value)
          if (manualFormState.value?.isNaturalPerson) data.invoiceTitleType = 'USER'

          store.dispatch('invoiceCenter/manualInvoicing', data)
            .then((res) => {
              if (res?.code === '0') {
                message.success('提交开票成功！', 0.9, () => {
                  eventBus.emit('oms-search:form-view:submit-form')
                })
                eventBus.emit('invoice-center-detail-drawer:close-drawer')
              } else {
                message.error(res.msg || '提交开票失败！')
              }
              // console.log('res:::', res)
            })
            .catch((err) => {
              // debugger
              console.log(err)
            })
        })
        .catch((err) => {
          // debugger
          console.log(err)
        });
    }
  }

  const handleReset = (params) => {
    if (formRef.value) {
      formRef.value.resetFields(params?.fields);
    }
  }

  const handleRemoveItem = (record) => {
    // 检查被删除的商品是否是当前选中的商品
    const currentSelected = itemLineSelected.value
    const isSelectedItem = currentSelected && isSameItem(currentSelected, record)

    remove(itemLineAvailableList.value, (itemLine) => {
      return record?.productId === itemLine?.productId
    })

    // 添加到 itemLineRemovedList
    itemLineRemovedList.value.push(record)

    // 检查是否需要占位项（基于 itemLineRemovedList 是否有数据）
    const hasSelectedItem = hasPlaceholderItem(itemLineAvailableList.value)
    const shouldHaveSelectedItem = itemLineRemovedList.value.length > 0

    if (isSelectedItem) {
      // 如果删除的是当前选中的商品，清空 itemLineSelected
      store.commit('invoiceCenter/updateInvoiceManualFormState', {
        itemLineSelected: undefined
      })
    }

    // 根据 itemLineRemovedList 的状态管理占位项
    if (shouldHaveSelectedItem && !hasSelectedItem) {
      // 需要占位项但不存在，添加
      itemLineAvailableList.value.push({ id: PLACEHOLDER_ITEM_ID })
    } else if (!shouldHaveSelectedItem && hasSelectedItem) {
      // 不需要占位项但存在，删除
      remove(itemLineAvailableList.value, (itemLine) => itemLine?.id === PLACEHOLDER_ITEM_ID)
    }
  }

  // onMounted(() => {
  //   // debugger
  //   if (record.value) {
  //     debugger
  //     store.dispatch('invoiceCenter/fetchInvoiceDetail', {
  //       orderId: 'JD1234567892',
  //       extOrderType: 'JD',
  //     });
  //   }
  // })

  eventBus.off('invoice-center:invoice-manual-form:submit-form');
  eventBus.off('invoice-center:invoice-manual-form:reset-form');
  eventBus.off('invoice-center:invoice-manual-form:remove-item');

  eventBus.on('invoice-center:invoice-manual-form:submit-form', handleSubmit);
  eventBus.on('invoice-center:invoice-manual-form:reset-form', handleReset);
  eventBus.on('invoice-center:invoice-manual-form:remove-item', handleRemoveItem);

  return {
    formRef,
    rules,
    actionState,
    invoiceDetailState,
  }
}