import { ref, watch, computed, onMounted, createVNode, } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { eventBus } from '@/eventBus';

export function useInit(props) {
  const store = useStore();
  const route = useRoute();

  const {
    actionState,
    orderDetailState,
  } = store.state.invoiceCenter;

  const record = computed(() => {
    return actionState.record
  })

  const actionStateType = computed(() => {
    return actionState.type
  })

  const open = computed(() => {
    return !!record.value
  })

  const orderDetail = computed(() => {
    return orderDetailState?.data?.data || {}
  })

  eventBus.off('invoice-center-detail-drawer:close-drawer')
  eventBus.on('invoice-center-detail-drawer:close-drawer', () => {
    store.commit('invoiceCenter/updateActionState', {
      type: '',
      record: undefined,
    })
    store.commit('invoiceCenter/RESET_INVOICE_DETAIL')
  })

  const handleOpenChange = (_open) => {
    // console.log('====================================')
    // if (_open) {
    //   console.log(actionStateType.value)
    //   store.dispatch('intentionOrders/fetchOrderDetail', {
    //     orderNo: record.value.orderNo,
    //   })
    // } else {

    // }
  }

  watch(open, (_open) => {
    if (_open && actionStateType.value === 'OrderInvoiceCreate') {
      store.dispatch('invoiceCenter/fetchInvoiceDetail', {
        orderId: record.value?.ikeaOrderNO,
        extOrderType: 'JD',
      });
    }
  })

  const handleClose = () => {
    eventBus.emit('invoice-center-detail-drawer:close-drawer')
  }

  return {
    record,
    open,
    actionStateType,
    handleOpenChange,
    handleClose,
    orderDetailState,
    orderDetail,
    actionState,
  }
}