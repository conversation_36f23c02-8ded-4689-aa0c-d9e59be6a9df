import { ref, watch, computed, onMounted, createVNode } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { eventBus } from '@/eventBus';

export function useInit(props) {
  const store = useStore();
  const route = useRoute();

  const role = ref('price-compensation')

  const globalState = computed(() => store.state.global.globalState)
  const userState = computed(() => store.state.global.userState)
  const actionState = computed(() => store.state.priceCompensation.actionState)

  const roles = computed(() => userState.value?.data?.data?.roles ?? [])
  const modules = computed(() => globalState.value.priceCompensation.modules)
  const system = computed(() => globalState.value.priceCompensation.system)
  const openKeys = computed(() => [])
  const selectedKeys = ref(['price-compensation'])

  const hasPermission = computed(() => {
    return roles.value.includes(role.value)
  })

  const module = computed(() => {
    return {
      key: 'price-compensation',
      title: '价格补偿管理',
    }
  })

  onMounted(async () => {
    await store.dispatch('oms/searchPermissionStore')
    store.dispatch('global/getCurrentUser')
      .then(async (_userState) => {
        roles.value.indexOf(role.value) > -1 && 
        eventBus.emit('price-compensation-search:form-view:reset-form')
      })
  })

  return {
    role,
    globalState,
    userState,
    actionState,
    roles,
    modules,
    module,
    system,
    openKeys,
    selectedKeys,
    hasPermission,
  }
}