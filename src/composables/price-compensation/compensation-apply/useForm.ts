import { ref, watch, computed, onMounted, createVNode } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { eventBus } from '@/eventBus';
import { message } from 'ant-design-vue-4';

export function useForm(props) {
  const store = useStore();
  const route = useRoute();

  const {
    actionState,
    orderDetailState,
  } = store.state.priceCompensation;

  // 表单引用
  const formRef = ref();

  const validatedTotalRefundAmount: any = ref(null);

  const compareStore = computed(() => {
    return orderDetailState?.compensationApply?.formState?.compareStore;
  })

  const vid = computed(() => {
    return orderDetailState?.compensationApply?.formState?.vid;
  })

  const paymentGatewayReferenceList = computed(() => {
    return orderDetailState?.priceCompare?.formState?.paymentGatewayReferenceList || []
  })

  const totalRefundAmount = computed(() => {
    return paymentGatewayReferenceList.value.reduce((acc, cur) => {
      return acc + (cur.refundAmount || 0)
    }, 0)
  })

  const record = computed(() => {
    return actionState.record
  })

  const actionStateType = computed(() => {
    return actionState.type
  })

  const isValidTotalRefundAmount = (e, v) => {
    if (totalRefundAmount.value > 0) {
      store.commit('priceCompensation/updateCompensationPriceCompareFormValidateResult', {
        totalRefundAmount: null
      })
      return Promise.resolve()
    } else {
      store.commit('priceCompensation/updateCompensationPriceCompareFormValidateResult', {
        totalRefundAmount: {
          message: '请填写本次退款金额'
        }
      })
      return Promise.reject() 
    }
  }

  const rules = {
    totalRefundAmount: [
      { validator: isValidTotalRefundAmount, trigger: ['blur', 'change'] }
    ],
  }

  const handleCancel = () => {
    // debugger
    store.commit('priceCompensation/updateDialogTabsActiveKey', { activeKey: 'form' })
    store.commit('priceCompensation/resetActionState')
    store.commit('priceCompensation/resetCompensationApplyFormState')
    store.commit('priceCompensation/resetCompensationPriceCompareFormState')
    store.commit('priceCompensation/resetCompensationPriceCompareData')
  }

  const handleSubmit = () => {
    if (formRef.value) {
      store.commit('priceCompensation/resetCompensationPriceCompareFormValidateResult')
      formRef.value.validate()
        .then((ret) => {
          store.dispatch('priceCompensation/refund', {
            vid: vid.value,
            storeNo: compareStore.value,
            paymentGatewayReferenceList: paymentGatewayReferenceList.value
          })
          .then((ret) => {
            console.log(ret)
            // debugger
            if (ret.code === '0') {
              message.success('提交成功', 0.9, () => {
                eventBus.emit('price-compensation:compensation-apply-form:cancel-form');
                eventBus.emit('oms-search:form-view:submit-form');
              });
            } else {
              message.error(ret?.msg || '系统异常，提交失败！', 0.9, () => {
                // eventBus.emit('price-compensation:compensation-apply-form:cancel-form');
              });
            }
          })
          .catch((err) => {
            console.log(err)
            // debugger
            message.error('系统异常！')
          })
        })
        .catch((err) => {
          // debugger
          console.log(err)
        });
    }
  }

  const priceCompare = () => {
    store.commit('priceCompensation/resetCompensationPriceCompareFormState');
    store.dispatch('priceCompensation/priceCompare', {
      vid: record.value?.vid,
      storeNo: compareStore.value
    });
  }

  const handleAudit = (params) => {
    store.dispatch('priceCompensation/updatePriceCompensationRecord', params).then((res) => {
      // 
      if (res.code === '0') {
        message.success('审核已完成！', 0.9, () => {
          eventBus.emit('price-compensation-search:form-view:submit-form');
          eventBus.emit('price-compensation:compensation-apply-form:cancel-form');
        })
      } else {
        message.error(res.message || '系统异常，请重试！', 0.9, () => {
          eventBus.emit('price-compensation-search:form-view:submit-form');
        })
      }
    }).catch((err) => {
      // message.error(err?.message)
      // debugger
    })
  }

  onMounted(() => {
    store.commit('priceCompensation/resetCompensationPriceCompareFormState');
    if (record.value) {
      if (['OrderCompensationView', 'OrderCompensationAudit'].includes(actionStateType.value)) {
        store.dispatch('priceCompensation/fetchOrderDetailInfo', {
          priceProtectionId: record.value?.priceProtectionId
        });
      }

      if (actionStateType.value === 'OrderCompensationRefund') {
        store.dispatch('priceCompensation/fetchOrderDetail', {
          vid: record.value?.vid
        });
      }
    }
  })

  // add events
  eventBus.off('price-compensation:compensation-apply-form:form-validate-totalRefundAmount')
  eventBus.off('price-compensation:compensation-apply-form:submit-form')
  eventBus.off('price-compensation:compensation-apply-form:cancel-form')
  eventBus.off('price-compensation:compensation-apply-form:price-compare')
  eventBus.off('price-compensation:compensation-apply-form:audit-form')

  eventBus.on('price-compensation:compensation-apply-form:form-validate-totalRefundAmount', () => {
    if (formRef.value) {
      formRef.value.validateFields(['totalRefundAmount'])
    }
  });
  eventBus.on('price-compensation:compensation-apply-form:submit-form', handleSubmit);
  eventBus.on('price-compensation:compensation-apply-form:cancel-form', handleCancel);
  eventBus.on('price-compensation:compensation-apply-form:price-compare', priceCompare);
  eventBus.on('price-compensation:compensation-apply-form:audit-form', handleAudit);
  
  return {
    formRef,
    rules,
    handleSubmit,
    handleCancel,
    validatedTotalRefundAmount,
  }
}