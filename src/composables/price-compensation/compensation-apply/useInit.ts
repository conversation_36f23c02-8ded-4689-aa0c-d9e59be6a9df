import { ref, watch, computed, onMounted, createVNode } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { eventBus } from '@/eventBus';

export function useInit(props) {
  const store = useStore();
  const route = useRoute();

  const handleTabChange = (activeKey) => {
    store.commit('priceCompensation/resetCompensationApplyFormState', {
      compareStore: undefined,
    })
    store.commit('priceCompensation/resetCompensationPriceCompareData')
    store.commit('priceCompensation/updateDialogTabsActiveKey', { activeKey })
  }

  return {
    handleTabChange,
  }
}