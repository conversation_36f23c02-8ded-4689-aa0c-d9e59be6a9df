export interface Query_Price_Compensation_OrderList_Req {
  // ikeaOrderNo?: string;
  vid?: string;
  contactPhoneNo?: string;
  applier?: string;
  auditor?: string;
  compareStoreNo?: string;
  orderStoreNo?: string;
  status?: string;
  applyDateStart?: string;
  applyDateEnd?: string;
  completeDate?: string;
  completeDateStart?: string;
  completeDateEnd?: string;
  curPage?: number;
  pageSize?: number;
}

export interface Query_Price_Compensation_Order_Detail_Req {
  vid?: string;
}

export interface Price_Compare_Req {
  vid?: string;
  storeNo?: string;
}

export interface Download_Req {

}