export interface Query_Invoice_List_Req {
  orderId?: string;
  invoiceNo?: string;
  buyerName?: string;
  fromInvoiceTimeStamp?: number;
  toInvoiceTimeStamp?: number;
  invoiceType?: string;
  invoiceStatus?: string;
  uploadState?: string;
  invoiceUse?: string;
  billingState?: string;
  directInvoice?: boolean;
  page?: number;
  size?: number;
}

export interface Query_Invoice_Center_Detail_Req {
  // orderId?: string;
  // invoiceNo?: string;
  invoiceTitleType: string;
}