import http from '@/utils/http'
// import qs from 'qs'

import { compact, find } from 'lodash'

import {
  Query_Price_Compensation_OrderList_Req,
  Download_Req,
} from '@/types/price-compensation'
import store from '../global/store'

const queryOrderLists = async (data: Query_Price_Compensation_OrderList_Req) => {
  const query = {
    ...data,
  }

  const url = '/price-protection/search'

  return await http({
    url,
    data: query,
    // timeout: 10000,
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const queryOrderDetail = async (params: any) => {
  return await http({
    url: `/price-protection/detail`,
    data: {
      vid: params.vid,
    }
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const queryOrderDetailInfo = async (params: any) => {
  return await http({
    url: `/price-protection/recordInfo`,
    data: {
      priceProtectionId: params.priceProtectionId,
    }
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const priceCompare = async (params: any) => {
  return await http({
    url: `/price-protection/compare`,
    data: {
      vid: params.vid,
      storeNo: params.storeNo,
    }
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const refund = async (params: any) => {
  return await http({
    url: `/price-protection/refund`,
    data: {
      vid: params.vid,
      storeNo: params.storeNo,
      sacId: params.sacId,
      paymentGatewayReferenceList: params.paymentGatewayReferenceList,
    }
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const update = async (params: any) => {
  return await http({
    url: `/price-protection/update`,
    data: {
      priceProtectionId: params.priceProtectionId,
      status: params.status,
    }
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const download = async (data: Download_Req) => {
  const query = {
    ...data,
  }
  return await http({
    url: '/price-protection/download',
    data: query,
    // timeout: 3000,
    // headers: {
    //   apikey: ENV.VITE_MASTER_DATA_API_KEY,
    // },
    // responseType: 'arraybuffer',
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

function ordersService() {
  return {
    queryOrderLists,
    queryOrderDetail,
    queryOrderDetailInfo,
    priceCompare,
    refund,
    update,
    download,
  }
}

export default ordersService()