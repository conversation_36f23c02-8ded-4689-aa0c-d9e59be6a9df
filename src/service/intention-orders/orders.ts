import http from '@/utils/http'
// import qs from 'qs'

import { compact, find } from 'lodash'

import {
  Query_Intention_OrderList_Req,
  Download_Req,
} from '@/types/intention-orders'
import store from '../global/store'

const queryOrderLists = async (data: Query_Intention_OrderList_Req) => {
  const query = {
    ...data,
  }

  const url = '/gift-purchasing/intention/order/search'

  return await http({
    url,
    data: query,
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const queryOrderDetail = async (params: any) => {
  return await http({
    url: `/gift-purchasing/order/${params.orderNo}/detail`,
    method: 'GET',
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const fetchPreCreateDeliveryArrangement = async (params: any) => {
  return await http({
    url: `/gift-purchasing/pre-create-delivery-arrangement/${params.orderNo}`,
    method: 'GET',
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const distribution = async (params: any) => {
  return await http({
    // url: `/gift-purchasing/intention/order/${params.orderNo}/distribution`,
    url: `/gift-purchasing/intention/order/distribution`,
    method: 'POST',
    data: {
      ...params,
    }
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const cancelOrder = async (params: any) => {
  return await http({
    url: `/gift-purchasing/order/${params.orderNo}/cancel`,
    method: 'GET',
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}

const download = async (data: Download_Req) => {
  const query = {
    ...data,
  }
  return await http({
    url: '/gift-purchasing/order/download',
    data: query,
    // timeout: 3000,
    // headers: {
    //   apikey: ENV.VITE_MASTER_DATA_API_KEY,
    // },
    // responseType: 'arraybuffer',
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  })
}


function ordersService() {
  return {
    queryOrderLists,
    queryOrderDetail,
    fetchPreCreateDeliveryArrangement,
    distribution,
    cancelOrder,
    download,
  }
}

export default ordersService()