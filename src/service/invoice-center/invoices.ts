import http from '@/utils/http'
// import qs from 'qs'

// import { compact, find } from 'lodash' // 暂时未使用，保留以备后续需要

import {
  Query_Invoice_List_Req,
} from '@/types/invoice-center'
import {
  QueryGoodsSupplementParams,
  QueryGoodsSupplementResponse,
} from '@/types/goods-supplement'

// 定义通用的API响应类型
interface ApiResponse<T = any> {
  code?: string | number;
  data?: T;
  msg?: string;
  [key: string]: any;
}

// 定义发票详情参数类型
interface QueryInvoiceDetailParams {
  orderId?: string;
  invoiceNo?: string;
  [key: string]: any;
}

// 定义公司税务信息参数类型
interface GetCompanyTaxInfoParams {
  companyName: string;
}

// 定义通用操作参数类型
interface InvoiceOperationParams {
  [key: string]: any;
}

/**
 * 统一的API调用封装函数
 * 保持原有的错误处理逻辑，确保向后兼容
 */
const apiCall = async <T = any>(config: {
  url: string;
  method?: 'GET' | 'POST';
  data?: any;
}): Promise<T> => {
  return await http({
    url: config.url,
    method: config.method,
    data: config.data,
  }).then(res => {
    return res;
  }).catch(err => {
    return err;
  });
};

/**
 * 查询发票列表
 * @param data 查询参数
 * @returns Promise<ApiResponse>
 */
const queryInvoiceList = async (data: Query_Invoice_List_Req): Promise<ApiResponse> => {
  const query = {
    ...data,
  };

  return apiCall({
    url: '/invoice/query/page',
    data: query,
  });
};

/**
 * 查询发票详情
 * @param params 查询参数
 * @returns Promise<ApiResponse>
 */
const queryInvoiceDetail = async (params: QueryInvoiceDetailParams): Promise<ApiResponse> => {
  return apiCall({
    url: '/invoice/order/detail',
    // method: 'GET', // 保持原有注释
    data: {
      ...params
    }
  });
};

/**
 * 获取公司税务信息
 * @param params 包含公司名称的参数
 * @returns Promise<ApiResponse>
 */
const getCompanyTaxInfo = async (params: GetCompanyTaxInfoParams): Promise<ApiResponse> => {
  return apiCall({
    url: `/invoice/getCompanyTaxInfo?companyName=${params.companyName}`,
    method: 'GET',
    // data: {
    //   ...params
    // }
  });
};

/**
 * 手动开票
 * @param params 开票参数
 * @returns Promise<ApiResponse>
 */
const manualInvoicing = async (params: InvoiceOperationParams): Promise<ApiResponse> => {
  return apiCall({
    url: '/invoice/manual',
    method: 'POST',
    data: {
      ...params
    }
  });
};

/**
 * 同步合并蓝票
 * @param params 操作参数
 * @returns Promise<ApiResponse>
 */
const syncMergeBlueInvoice = async (params: InvoiceOperationParams): Promise<ApiResponse> => {
  return apiCall({
    url: '/invoice/syncMergeBlueInvoice',
    method: 'POST',
    data: {
      ...params
    }
  });
};

/**
 * 重开发票
 * @param params 操作参数
 * @returns Promise<ApiResponse>
 */
const redraft = async (params: InvoiceOperationParams): Promise<ApiResponse> => {
  return apiCall({
    url: '/invoice/redraft',
    method: 'POST',
    data: {
      ...params
    }
  });
};

/**
 * 取消发票
 * @param params 操作参数
 * @returns Promise<ApiResponse>
 */
const cancel = async (params: InvoiceOperationParams): Promise<ApiResponse> => {
  return apiCall({
    url: '/invoice/cancel',
    method: 'POST',
    data: {
      ...params
    }
  });
};

/**
 * 查询商品补差信息
 * @param params 查询参数，包含商品代码
 * @returns Promise<QueryGoodsSupplementResponse>
 */
const queryGoodsSupplementItem = async (params: QueryGoodsSupplementParams): Promise<QueryGoodsSupplementResponse> => {
  // URL 编码防止特殊字符问题
  const encodedItemNo = encodeURIComponent(params.itemNo);

  return apiCall({
    url: `/invoice/getItemLineInfo?itemNo=${encodedItemNo}`,
    method: 'GET',
  });
};

/**
 * 发票中心服务
 * 提供发票相关的所有API调用功能
 */
function invoicesService() {
  return {
    queryInvoiceList,
    queryInvoiceDetail,
    getCompanyTaxInfo,
    manualInvoicing,
    syncMergeBlueInvoice,
    redraft,
    cancel,
    queryGoodsSupplementItem,

    // 预留的功能接口
    // distribution,
    // download,
  };
}

export default invoicesService();