<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Service 集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Invoice Service 优化后集成测试</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <div class="info test-result">
            这个页面用于验证优化后的 invoices service 是否能在浏览器环境中正常工作。
            请打开浏览器开发者工具查看详细的测试结果。
        </div>
    </div>

    <div class="test-container">
        <h2>基础功能测试</h2>
        <button onclick="testBasicFunctionality()">运行基础功能测试</button>
        <div id="basic-test-results"></div>
    </div>

    <div class="test-container">
        <h2>API调用测试</h2>
        <button onclick="testApiCalls()">运行API调用测试</button>
        <div id="api-test-results"></div>
    </div>

    <div class="test-container">
        <h2>错误处理测试</h2>
        <button onclick="testErrorHandling()">运行错误处理测试</button>
        <div id="error-test-results"></div>
    </div>

    <div class="test-container">
        <h2>类型安全测试</h2>
        <button onclick="testTypeSafety()">运行类型安全测试</button>
        <div id="type-test-results"></div>
    </div>

    <script>
        // 模拟 http 工具
        const mockHttp = (config) => {
            console.log('HTTP调用:', config);
            
            // 模拟不同的响应
            if (config.url.includes('error')) {
                return Promise.reject(new Error('模拟网络错误'));
            }
            
            const mockResponse = {
                data: {
                    code: '0',
                    data: config.url.includes('getItemLineInfo') ? {
                        productId: 'test-product-123',
                        productName: '测试商品',
                        ikeaItemNo: 'TEST001',
                        price: 99.99,
                        quantity: 1
                    } : {
                        list: [],
                        total: 0
                    },
                    msg: 'success'
                }
            };
            
            return Promise.resolve(mockResponse);
        };

        // 模拟 invoices service 的核心逻辑
        const createInvoicesService = () => {
            // 统一的API调用封装函数
            const apiCall = async (config) => {
                return await mockHttp({
                    url: config.url,
                    method: config.method,
                    data: config.data,
                }).then(res => {
                    return res;
                }).catch(err => {
                    return err;
                });
            };

            // 查询发票列表
            const queryInvoiceList = async (data) => {
                return apiCall({
                    url: '/invoice/query/page',
                    data: data,
                });
            };

            // 查询发票详情
            const queryInvoiceDetail = async (params) => {
                return apiCall({
                    url: '/invoice/order/detail',
                    data: params
                });
            };

            // 获取公司税务信息
            const getCompanyTaxInfo = async (params) => {
                return apiCall({
                    url: `/invoice/getCompanyTaxInfo?companyName=${params.companyName}`,
                    method: 'GET',
                });
            };

            // 查询商品补差信息
            const queryGoodsSupplementItem = async (params) => {
                const encodedItemNo = encodeURIComponent(params.itemNo);
                return apiCall({
                    url: `/invoice/getItemLineInfo?itemNo=${encodedItemNo}`,
                    method: 'GET',
                });
            };

            return {
                queryInvoiceList,
                queryInvoiceDetail,
                getCompanyTaxInfo,
                queryGoodsSupplementItem,
            };
        };

        const invoicesService = createInvoicesService();

        // 测试函数
        function displayResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        async function testBasicFunctionality() {
            const containerId = 'basic-test-results';
            document.getElementById(containerId).innerHTML = '';
            
            try {
                displayResult(containerId, '开始基础功能测试...', 'info');
                
                // 测试服务对象是否正确创建
                if (typeof invoicesService === 'object') {
                    displayResult(containerId, '✓ 服务对象创建成功', 'success');
                } else {
                    displayResult(containerId, '✗ 服务对象创建失败', 'error');
                    return;
                }
                
                // 测试所有预期函数是否存在
                const expectedFunctions = [
                    'queryInvoiceList',
                    'queryInvoiceDetail',
                    'getCompanyTaxInfo',
                    'queryGoodsSupplementItem'
                ];
                
                expectedFunctions.forEach(funcName => {
                    if (typeof invoicesService[funcName] === 'function') {
                        displayResult(containerId, `✓ 函数 ${funcName} 存在`, 'success');
                    } else {
                        displayResult(containerId, `✗ 函数 ${funcName} 不存在`, 'error');
                    }
                });
                
                displayResult(containerId, '基础功能测试完成', 'success');
                
            } catch (error) {
                displayResult(containerId, `测试失败: ${error.message}`, 'error');
            }
        }

        async function testApiCalls() {
            const containerId = 'api-test-results';
            document.getElementById(containerId).innerHTML = '';
            
            try {
                displayResult(containerId, '开始API调用测试...', 'info');
                
                // 测试查询发票列表
                const listResult = await invoicesService.queryInvoiceList({
                    page: 1,
                    size: 10,
                    orderId: 'TEST123'
                });
                
                if (listResult && listResult.data) {
                    displayResult(containerId, '✓ queryInvoiceList 调用成功', 'success');
                } else {
                    displayResult(containerId, '✗ queryInvoiceList 调用失败', 'error');
                }
                
                // 测试查询商品补差信息（包含URL编码）
                const itemResult = await invoicesService.queryGoodsSupplementItem({
                    itemNo: 'TEST/ITEM 001'
                });
                
                if (itemResult && itemResult.data) {
                    displayResult(containerId, '✓ queryGoodsSupplementItem 调用成功（含URL编码）', 'success');
                } else {
                    displayResult(containerId, '✗ queryGoodsSupplementItem 调用失败', 'error');
                }
                
                // 测试获取公司税务信息
                const taxResult = await invoicesService.getCompanyTaxInfo({
                    companyName: '测试公司'
                });
                
                if (taxResult && taxResult.data) {
                    displayResult(containerId, '✓ getCompanyTaxInfo 调用成功', 'success');
                } else {
                    displayResult(containerId, '✗ getCompanyTaxInfo 调用失败', 'error');
                }
                
                displayResult(containerId, 'API调用测试完成', 'success');
                
            } catch (error) {
                displayResult(containerId, `API调用测试失败: ${error.message}`, 'error');
            }
        }

        async function testErrorHandling() {
            const containerId = 'error-test-results';
            document.getElementById(containerId).innerHTML = '';
            
            try {
                displayResult(containerId, '开始错误处理测试...', 'info');
                
                // 创建一个会失败的服务实例
                const errorService = (() => {
                    const apiCall = async (config) => {
                        return await Promise.reject(new Error('模拟错误')).then(res => {
                            return res;
                        }).catch(err => {
                            return err; // 返回错误而不是抛出
                        });
                    };

                    return {
                        queryInvoiceList: async (data) => {
                            return apiCall({ url: '/error', data });
                        }
                    };
                })();
                
                const errorResult = await errorService.queryInvoiceList({ test: true });
                
                if (errorResult instanceof Error) {
                    displayResult(containerId, '✓ 错误被正确捕获并返回', 'success');
                } else {
                    displayResult(containerId, '✗ 错误处理不正确', 'error');
                }
                
                displayResult(containerId, '错误处理测试完成', 'success');
                
            } catch (error) {
                displayResult(containerId, `错误处理测试失败: ${error.message}`, 'error');
            }
        }

        async function testTypeSafety() {
            const containerId = 'type-test-results';
            document.getElementById(containerId).innerHTML = '';
            
            try {
                displayResult(containerId, '开始类型安全测试...', 'info');
                
                // 测试URL编码
                const testItemNo = 'TEST/ITEM 001';
                const encoded = encodeURIComponent(testItemNo);
                
                if (encoded === 'TEST%2FITEM%20001') {
                    displayResult(containerId, '✓ URL编码处理正确', 'success');
                } else {
                    displayResult(containerId, '✗ URL编码处理错误', 'error');
                }
                
                // 测试参数验证
                try {
                    await invoicesService.getCompanyTaxInfo({ companyName: '测试公司' });
                    displayResult(containerId, '✓ 参数类型验证通过', 'success');
                } catch (error) {
                    displayResult(containerId, '✗ 参数类型验证失败', 'error');
                }
                
                displayResult(containerId, '类型安全测试完成', 'success');
                
            } catch (error) {
                displayResult(containerId, `类型安全测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后显示初始信息
        window.onload = function() {
            console.log('Invoice Service 集成测试页面已加载');
            console.log('请点击各个测试按钮来验证优化后的代码功能');
        };
    </script>
</body>
</html>
