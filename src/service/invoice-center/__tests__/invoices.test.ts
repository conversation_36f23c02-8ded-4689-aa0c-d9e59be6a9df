import { describe, it, expect, vi, beforeEach } from 'vitest'
import invoicesService from '../invoices'

// Mock http module
vi.mock('@/utils/http', () => ({
  default: vi.fn()
}))

describe('invoicesService', () => {
  const mockHttp = vi.mocked(await import('@/utils/http')).default

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('queryInvoiceList', () => {
    it('should call http with correct parameters', async () => {
      const mockResponse = { data: { code: '0', data: [] } }
      mockHttp.mockResolvedValue(mockResponse)

      const params = {
        orderId: '123',
        page: 1,
        size: 10
      }

      const result = await invoicesService.queryInvoiceList(params)

      expect(mockHttp).toHaveBeenCalledWith({
        url: '/invoice/query/page',
        method: undefined,
        data: params
      })
      expect(result).toBe(mockResponse)
    })

    it('should handle errors correctly', async () => {
      const mockError = new Error('Network error')
      mockHttp.mockRejectedValue(mockError)

      const params = { orderId: '123' }
      const result = await invoicesService.queryInvoiceList(params)

      expect(result).toBe(mockError)
    })
  })

  describe('queryGoodsSupplementItem', () => {
    it('should encode itemNo correctly', async () => {
      const mockResponse = { data: { code: '0', data: {} } }
      mockHttp.mockResolvedValue(mockResponse)

      const params = { itemNo: 'test item/123' }
      await invoicesService.queryGoodsSupplementItem(params)

      expect(mockHttp).toHaveBeenCalledWith({
        url: '/invoice/getItemLineInfo?itemNo=test%20item%2F123',
        method: 'GET',
        data: undefined
      })
    })
  })

  describe('getCompanyTaxInfo', () => {
    it('should call GET method with company name in URL', async () => {
      const mockResponse = { data: { code: '0', data: {} } }
      mockHttp.mockResolvedValue(mockResponse)

      const params = { companyName: '测试公司' }
      await invoicesService.getCompanyTaxInfo(params)

      expect(mockHttp).toHaveBeenCalledWith({
        url: '/invoice/getCompanyTaxInfo?companyName=测试公司',
        method: 'GET',
        data: undefined
      })
    })
  })

  describe('POST operations', () => {
    const testCases = [
      { method: 'manualInvoicing', url: '/invoice/manual' },
      { method: 'syncMergeBlueInvoice', url: '/invoice/syncMergeBlueInvoice' },
      { method: 'redraft', url: '/invoice/redraft' },
      { method: 'cancel', url: '/invoice/cancel' }
    ]

    testCases.forEach(({ method, url }) => {
      it(`${method} should call POST method correctly`, async () => {
        const mockResponse = { data: { code: '0' } }
        mockHttp.mockResolvedValue(mockResponse)

        const params = { id: '123', reason: 'test' }
        await (invoicesService as any)[method](params)

        expect(mockHttp).toHaveBeenCalledWith({
          url,
          method: 'POST',
          data: params
        })
      })
    })
  })
})
