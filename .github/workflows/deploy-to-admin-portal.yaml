name: publish spa

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'dev,prod'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - prod

jobs:
  build-spa:
    uses: china-digital-hub/shared-workflows/.github/workflows/admin-portal-spa-ci.yaml@master
    with:
      node-version: '18.20.4'
      environment: ${{ inputs.environment }}
      app-name: 'orders-portal'
    secrets:
      RT_CDH_USERNAME: ${{ secrets.RT_CDH_USERNAME }}
      RT_CDH_TOKEN: ${{ secrets.RT_CDH_TOKEN }}
      NPM_TOKEN: ${{ secrets.IKEACN_NPM_TOKEN }}

  deploy-spa:
    uses: china-digital-hub/shared-workflows/.github/workflows/admin-portal-spa-cd.yaml@master
    needs: [build-spa]
    with:
      environment: ${{ inputs.environment }}
      upload-name: ${{ needs.build-spa.outputs.upload-name }}
      app-name: 'orders-portal'
    secrets:
      repo-token: ${{ secrets.ADMIN_PORTAL_STATIC_CD_TOKEN }}