const argv = require('yargs-parser')(process.argv.slice(2))
const AnyProxy = require('anyproxy');
const options = {
  port: 8001,
  rule: require(`../proxy/rules-${argv.mode}.js`),
  webInterface: {
    enable: true,
    webPort: 8002
  },
  throttle: 10000,
  forceProxyHttps: false,
  wsIntercept: false, // 不开启websocket代理
  silent: false
};
const proxyServer = new AnyProxy.ProxyServer(options);

proxyServer.on('ready', () => {
  console.log('anyproxy ready')
});

proxyServer.on('error', () => {
  console.log('anyproxy error')
  proxyServer.close();
});

proxyServer.start();

// when finished
// proxyServer.close();
