import express from 'express';
import { randomUUID } from 'node:crypto';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { isInitializeRequest } from '@modelcontextprotocol/sdk/types.js';
import { mcpAuthRouter } from '@modelcontextprotocol/sdk/server/auth/router.js';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { OAuth2Config, getOAuth2Config } from '../auth/index.js';
import { globalTokenCache } from '../auth/token-cache.js';
import { requireAdminAccess, requireReadAccess, oAuth2ErrorHandler } from '../auth/middleware.js';
import { globalOAuth2Logger } from '../auth/oauth-logger.js';
import { setRequestContext, createRequestContextFromExpress } from '../utils/request-context.js';

export class HttpMCPTransport {
  private app: express.Application;
  private server: any;
  private port: number;
  private transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};
  private mcpServer: Server | null = null;
  private oauth2Provider: ProxyOAuthServerProvider | null = null;
  private oauth2Config: OAuth2Config | null = null;

  constructor(port: number = 3000, oauth2Provider?: ProxyOAuthServerProvider) {
    this.port = port;
    this.oauth2Provider = oauth2Provider || null;
    this.oauth2Config = oauth2Provider ? getOAuth2Config() : null;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));

    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, mcp-session-id');
      if (req.method === 'OPTIONS') return res.sendStatus(200);
      next();
    });

    this.app.use((req, res, next) => {
      const sessionId = req.headers['mcp-session-id'];
      console.error(`🌐 ${req.method} ${req.path} ${sessionId ? `[${sessionId}]` : '[no-session]'}`);
      next();
    });

    // Add OAuth2 error handling middleware when OAuth2 is enabled
    if (this.oauth2Provider && this.oauth2Config) {
      this.app.use(oAuth2ErrorHandler(this.oauth2Config.server.baseUrl));
    }
  }

  private setupRoutes() {
    // Apply OAuth2 authentication to all MCP endpoints when OAuth2 is enabled
    if (this.oauth2Provider) {
      // MCP endpoints require read access at minimum
      this.app.post('/mcp',
        requireReadAccess(this.oauth2Provider, { enableLogging: true }),
        (req, res) => this.handleMCPPost(req, res)
      );
      this.app.get('/mcp',
        requireReadAccess(this.oauth2Provider, { enableLogging: true }),
        (req, res) => this.handleSessionRequest(req, res)
      );
      this.app.delete('/mcp',
        requireReadAccess(this.oauth2Provider, { enableLogging: true }),
        (req, res) => this.handleSessionRequest(req, res)
      );
    } else {
      // Fallback for when OAuth2 is disabled (development/testing)
      this.app.post('/mcp', (req, res) => this.handleMCPPost(req, res));
      this.app.get('/mcp', (req, res) => this.handleSessionRequest(req, res));
      this.app.delete('/mcp', (req, res) => this.handleSessionRequest(req, res));
    }

    this.app.get('/health', (_, res) => res.json({
      status: 'healthy',
      transport: 'streamable-http',
      timestamp: new Date().toISOString(),
      activeSessions: Object.keys(this.transports).length,
      sessions: Object.keys(this.transports)
    }));



    this.app.get('/mcp/capabilities', (_, res) => res.json({
      transport: 'streamable-http',
      streaming: true,
      protocols: ['jsonrpc-2.0'],
      version: '1.0.0',
      features: ['session-management', 'server-sent-events'],
      oauth2_enabled: !!this.oauth2Provider
    }));

    if (this.oauth2Provider && this.oauth2Config) {
      this.setupOAuth2Routes();
    }
  }

  private setupOAuth2Routes() {
    if (!this.oauth2Provider || !this.oauth2Config) return;

    const issuerUrl = new URL(this.oauth2Config.keycloak.issuer);
    const baseUrl = new URL(this.oauth2Config.server.baseUrl);
    const resourceServerUrl = new URL(`${this.oauth2Config.server.baseUrl}/mcp`);

    this.app.get('/.well-known/oauth-protected-resource', (_, res) => res.json({
      resource: resourceServerUrl.toString(),
      authorization_servers: [baseUrl.toString()],
      scopes_supported: this.oauth2Config!.client.scopes,
      resource_documentation: "https://modelcontextprotocol.io/"
    }));

    this.app.get('/.well-known/oauth-authorization-server', async (_, res) => {
      try {
        const response = await fetch(`${issuerUrl}/.well-known/openid-configuration`);
        const data = await response.json();
        const baseUrlStr = baseUrl.toString().replace(/\/$/, '');

        res.status(response.status).json({
          ...(data as object),
          registration_endpoint: `${baseUrlStr}/register`,
          scopes_supported: this.oauth2Config!.client.scopes,
        });
      } catch (error) {
        res.status(500).json({
          error: 'server_error',
          error_description: 'Failed to fetch authorization server metadata'
        });
      }
    });

    this.app.post('/register', async (req, res) => {
      try {
        res.status(201).json({
          client_id: this.oauth2Config!.client.id,
          client_secret: this.oauth2Config!.client.secret,
          client_id_issued_at: Math.floor(Date.now() / 1000),
          client_secret_expires_at: 0,
          redirect_uris: req.body.redirect_uris || [this.oauth2Config!.client.redirectUri],
          grant_types: req.body.grant_types || ['authorization_code', 'refresh_token'],
          response_types: req.body.response_types || ['code'],
          client_name: req.body.client_name || 'MCP Inspector',
          client_uri: req.body.client_uri || 'https://github.com/modelcontextprotocol/inspector',
          scope: this.oauth2Config!.client.scopes.join(' '),
          token_endpoint_auth_method: 'client_secret_post'
        });
      } catch (error) {
        res.status(500).json({
          error: 'server_error',
          error_description: 'Failed to return client configuration'
        });
      }
    });

    this.app.get('/authorize', (req, res) => {
      const keycloakAuthUrl = new URL(`${issuerUrl}/protocol/openid-connect/auth`);
      Object.entries(req.query).forEach(([key, value]) => {
        keycloakAuthUrl.searchParams.set(key, value as string);
      });
      res.redirect(keycloakAuthUrl.toString());
    });

    this.app.post('/token', async (req, res) => {
      try {
        const formData = new URLSearchParams();
        Object.entries(req.body).forEach(([key, value]) => {
          formData.append(key, value as string);
        });

        const response = await fetch(`${issuerUrl}/protocol/openid-connect/token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            ...(req.headers.authorization && { 'Authorization': req.headers.authorization })
          },
          body: formData.toString()
        });

        res.status(response.status).json(await response.json());
      } catch (error) {
        res.status(500).json({
          error: 'server_error',
          error_description: 'Token request failed'
        });
      }
    });

    this.app.post('/revoke', async (req, res) => {
      try {
        const response = await fetch(`${issuerUrl}/protocol/openid-connect/revoke`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            ...(req.headers.authorization && { 'Authorization': req.headers.authorization })
          },
          body: new URLSearchParams(req.body).toString()
        });

        res.status(response.status).send();
      } catch (error) {
        res.status(500).json({
          error: 'server_error',
          error_description: 'Token revocation failed'
        });
      }
    });

    // Protected cache endpoints with scope-based authorization
    // Token cache status endpoint (read access required)
    this.app.get('/auth/cache/status',
      requireReadAccess(this.oauth2Provider, { enableLogging: true }),
      (_, res) => {
        const stats = globalTokenCache.getStats();
        res.json({
          cache: {
            ...stats,
            oldestEntryAge: stats.oldestEntry ? Date.now() - stats.oldestEntry : null,
          },
          timestamp: new Date().toISOString(),
        });
      }
    );

    // Cache management endpoints (admin access required)
    this.app.post('/auth/cache/clear',
      requireAdminAccess(this.oauth2Provider, { enableLogging: true }),
      (_, res) => {
        globalTokenCache.clear();
        res.json({
          message: 'Token cache cleared successfully',
          timestamp: new Date().toISOString(),
        });
      }
    );

    this.app.delete('/auth/cache/:token',
      requireAdminAccess(this.oauth2Provider, { enableLogging: true }),
      (req, res) => {
        const { token } = req.params;
        const deleted = globalTokenCache.delete(token);
        res.json({
          message: deleted ? 'Token removed from cache' : 'Token not found in cache',
          deleted,
          timestamp: new Date().toISOString(),
        });
      }
    );

    // OAuth2 logging and monitoring endpoints (admin access required)
    this.app.get('/auth/logs/events',
      requireReadAccess(this.oauth2Provider, { enableLogging: true }),
      (req, res) => {
        const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;
        const events = globalOAuth2Logger.getEvents(limit);
        res.json({
          events,
          total: events.length,
          timestamp: new Date().toISOString(),
        });
      }
    );

    this.app.get('/auth/logs/metrics',
      requireReadAccess(this.oauth2Provider, { enableLogging: true }),
      (_, res) => {
        const metrics = globalOAuth2Logger.getMetrics();
        res.json({
          metrics,
          timestamp: new Date().toISOString(),
        });
      }
    );

    this.app.get('/auth/logs/events/:type',
      requireReadAccess(this.oauth2Provider, { enableLogging: true }),
      (req, res) => {
        const { type } = req.params;
        const events = globalOAuth2Logger.getEventsByType(type);
        res.json({
          events,
          eventType: type,
          total: events.length,
          timestamp: new Date().toISOString(),
        });
      }
    );

    this.app.get('/auth/logs/clients/:clientId',
      requireReadAccess(this.oauth2Provider, { enableLogging: true }),
      (req, res) => {
        const { clientId } = req.params;
        const events = globalOAuth2Logger.getEventsByClient(clientId);
        res.json({
          events,
          clientId,
          total: events.length,
          timestamp: new Date().toISOString(),
        });
      }
    );

    this.app.post('/auth/logs/clear',
      requireAdminAccess(this.oauth2Provider, { enableLogging: true }),
      (_, res) => {
        globalOAuth2Logger.clear();
        res.json({
          message: 'OAuth2 logs and metrics cleared successfully',
          timestamp: new Date().toISOString(),
        });
      }
    );

    // Mount the MCP SDK auth router (this will handle other OAuth2 endpoints)
    this.app.use(mcpAuthRouter({
      provider: this.oauth2Provider,
      issuerUrl,
      baseUrl,
      serviceDocumentationUrl: new URL('https://modelcontextprotocol.io/')
    }));


    console.error('� Manual override applied for /.well-known/oauth-protected-resource');
    console.error('📋 oauth-protected-resource response:');
    console.error(`   {`);
    console.error(`     "resource": "${resourceServerUrl}",`);
    console.error(`     "authorization_servers": ["${baseUrl}"],`);
    console.error(`     "scopes_supported": ${JSON.stringify(this.oauth2Config.client.scopes)},`);
    console.error(`     "resource_documentation": "https://modelcontextprotocol.io/"`);
    console.error(`   }`);
    console.error('');
    console.error('✅ Both resource and authorization_servers now point to MCP server');
    const baseUrlStr = baseUrl.toString().replace(/\/$/, '');
    console.error('🔍 OAuth2 proxy endpoints configured to avoid CORS issues:');
    console.error(`   📋 Protected Resource: ${baseUrlStr}/.well-known/oauth-protected-resource`);
    console.error(`   🔍 Authorization Server: ${baseUrlStr}/.well-known/oauth-authorization-server`);
    console.error(`   📝 Client Registration: ${baseUrlStr}/register (uses pre-configured client)`);
    console.error(`   🔐 Authorization: ${baseUrlStr}/authorize`);
    console.error(`   🎫 Token: ${baseUrlStr}/token`);
    console.error(`   🗑️ Revocation: ${baseUrlStr}/revoke`);
    console.error('');
    console.error('🔧 Using pre-configured client credentials from .env:');
    console.error(`   Client ID: ${this.oauth2Config.client.id}`);
    console.error(`   Client Secret: ${this.oauth2Config.client.secret ? '***SET***' : '***NOT SET***'}`);
    console.error(`   Redirect URI: ${this.oauth2Config.client.redirectUri}`);
    console.error(`   Scopes: ${this.oauth2Config.client.scopes.join(', ')}`);
    console.error('✅ MCP Inspector will use your existing Keycloak client configuration');
  }

  setMCPServer(server: Server) {
    this.mcpServer = server;
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, '0.0.0.0', () => {
        console.error(`🚀 HTTP MCP Transport on port ${this.port}`);
        if (this.oauth2Provider) console.error(`🔐 OAuth2 enabled`);
        resolve();
      });
      this.server.on('error', reject);
    });
  }

  async stop(): Promise<void> {
    if (this.server) {
      return new Promise((resolve) => this.server.close(resolve));
    }
  }







  /**
   * Extract user context from authenticated request
   */
  private extractUserContext(req: express.Request): { email?: string; clientId?: string; scopes?: string[]; token?: string } | null {
    const auth = (req as any).auth;
    if (!auth) return null;

    const token = req.headers.authorization?.replace('Bearer ', '');
    const userInfo = auth.userInfo;
    const email = userInfo?.email || userInfo?.preferred_username;

    return {
      email,
      clientId: auth.clientId,
      scopes: auth.scopes,
      token
    };
  }
  private async handleMCPPost(req: express.Request, res: express.Response) {
    try {
      const sessionId = req.headers['mcp-session-id'] as string | undefined;

      // Extract and set user context from authenticated request
      const userContext = this.extractUserContext(req);
      if (userContext) {
        console.error(`🔐 [MCP] Authenticated request from user: ${userContext.email || userContext.clientId}`);

        // Set request context for downstream service calls
        const requestContext = createRequestContextFromExpress(req);
        setRequestContext(requestContext);
      }

      let transport: StreamableHTTPServerTransport;

      if (sessionId && this.transports[sessionId]) {
        transport = this.transports[sessionId];
      } else if (!sessionId && isInitializeRequest(req.body)) {
        transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: () => randomUUID(),
          onsessioninitialized: (sessionId) => {
            this.transports[sessionId] = transport;
          },
          enableDnsRebindingProtection: false,
        });

        transport.onclose = () => {
          if (transport.sessionId) {
            delete this.transports[transport.sessionId];
          }
        };

        if (!this.mcpServer) throw new Error('MCP server not initialized');
        await this.mcpServer.connect(transport);
      } else {
        return res.status(400).json({
          jsonrpc: '2.0',
          error: { code: -32000, message: 'Bad Request: No valid session ID provided' },
          id: null,
        });
      }

      await transport.handleRequest(req, res, req.body);
    } catch (error) {
      if (!res.headersSent) {
        // Check if it's an authentication/authorization error
        if (error instanceof Error && (
          error.message.includes('unauthorized') ||
          error.message.includes('invalid_token') ||
          error.message.includes('insufficient_scope')
        )) {
          // OAuth2-compliant error response
          res.set('WWW-Authenticate', `Bearer realm="${this.oauth2Config?.server.baseUrl || 'MCP Server'}", resource_metadata="${this.oauth2Config?.server.baseUrl || 'http://localhost:3000'}/.well-known/oauth-protected-resource"`);
          res.status(401).json({
            jsonrpc: '2.0',
            error: {
              code: -32001,
              message: 'Authentication required',
              data: error.message
            },
            id: null,
          });
        } else {
          // Generic internal error
          res.status(500).json({
            jsonrpc: '2.0',
            error: {
              code: -32603,
              message: 'Internal error',
              data: error instanceof Error ? error.message : 'Unknown error'
            },
            id: null,
          });
        }
      }
    }
  }

  private async handleSessionRequest(req: express.Request, res: express.Response) {
    const sessionId = req.headers['mcp-session-id'] as string | undefined;

    if (!sessionId || !this.transports[sessionId]) {
      return res.status(400).send('Invalid or missing session ID');
    }

    try {
      await this.transports[sessionId].handleRequest(req, res);
    } catch (error) {
      if (!res.headersSent) {
        res.status(500).send('Internal server error');
      }
    }
  }

  async close(): Promise<void> {
    return new Promise((resolve) => {
      Object.values(this.transports).forEach(transport => {
        try {
          transport.close();
        } catch (error) {
          console.error('Error closing transport:', error);
        }
      });
      this.transports = {};

      if (this.server) {
        this.server.close(resolve);
      } else {
        resolve();
      }
    });
  }

  getActiveSessions(): string[] {
    return Object.keys(this.transports);
  }

  getSessionCount(): number {
    return Object.keys(this.transports).length;
  }

  getPort(): number {
    return this.port;
  }
}
