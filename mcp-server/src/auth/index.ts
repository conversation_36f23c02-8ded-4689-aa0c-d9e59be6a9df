import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { getOAuth2Config, validateOAuth2Config, logOAuth2Config } from './oauth-config.js';
import { createOIDCProvider } from './direct-oidc-provider.js';

/**
 * Initialize OAuth2 authentication for the MCP server
 * Returns the OAuth2 provider for use with HTTP transport
 */
export async function initializeOAuth2(_mcpServer: Server): Promise<{
  provider?: ProxyOAuthServerProvider;
}> {
  const config = getOAuth2Config();

  // Log configuration
  logOAuth2Config(config);

  if (!config.enabled) {
    console.error('🔐 OAuth2 is disabled, skipping initialization');
    return {};
  }

  // Validate configuration
  const validation = validateOAuth2Config(config);
  if (!validation.valid) {
    console.error('❌ OAuth2 configuration is invalid:');
    validation.errors.forEach(error => console.error(`  - ${error}`));
    throw new Error('Invalid OAuth2 configuration');
  }

  console.error('🔐 Initializing OAuth2 with OIDC provider...');
  console.error(`   OIDC Issuer: ${config.keycloak.issuer}`);
  console.error(`   Client ID: ${config.client.id}`);

  try {
    // Create OIDC provider using MCP SDK directly (no custom class needed!)
    console.error('🔧 Creating OIDC provider using MCP SDK directly...');
    const provider = createOIDCProvider(config);

    console.error('✅ OAuth2 provider initialized successfully');
    console.error('💡 OAuth2 middleware will be integrated with HTTP transport');

    return {
      provider,
    };
  } catch (error) {
    console.error('❌ Failed to initialize OAuth2:', error);
    throw error;
  }
}

// Note: Template and validation functions removed - configuration is now handled
// by environment variables and the getOAuth2Config() function provides all necessary
// configuration with proper validation through validateOAuth2Config()

// Export all OAuth2 components
export * from './oauth-config.js';
export * from './direct-oidc-provider.js';
export * from './middleware.js';
