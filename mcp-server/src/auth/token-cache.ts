import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';

/**
 * Token cache entry with expiration
 */
interface TokenCacheEntry {
  authInfo: AuthInfo;
  expires: number;
  createdAt: number;
}

/**
 * Token cache configuration
 */
export interface TokenCacheConfig {
  /** Cache TTL in milliseconds (default: 5 minutes) */
  ttl?: number;
  /** Maximum cache size (default: 1000 entries) */
  maxSize?: number;
  /** Cleanup interval in milliseconds (default: 1 minute) */
  cleanupInterval?: number;
}

/**
 * In-memory token cache with automatic cleanup
 * Reduces calls to Keycloak by caching valid token validation results
 */
export class TokenCache {
  private cache = new Map<string, TokenCacheEntry>();
  private cleanupTimer?: NodeJS.Timeout;
  private readonly config: Required<TokenCacheConfig>;

  constructor(config: TokenCacheConfig = {}) {
    this.config = {
      ttl: config.ttl ?? 5 * 60 * 1000, // 5 minutes
      maxSize: config.maxSize ?? 1000,
      cleanupInterval: config.cleanupInterval ?? 60 * 1000, // 1 minute
    };

    // Start automatic cleanup
    this.startCleanup();
  }

  /**
   * Get cached auth info for a token
   */
  get(token: string): AuthInfo | null {
    const entry = this.cache.get(token);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (entry.expires <= Date.now()) {
      this.cache.delete(token);
      return null;
    }

    return entry.authInfo;
  }

  /**
   * Cache auth info for a token
   */
  set(token: string, authInfo: AuthInfo, customTtl?: number): void {
    const now = Date.now();
    const ttl = customTtl ?? this.config.ttl;
    
    // Enforce max cache size by removing oldest entries
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest();
    }

    this.cache.set(token, {
      authInfo,
      expires: now + ttl,
      createdAt: now,
    });
  }

  /**
   * Remove a token from cache
   */
  delete(token: string): boolean {
    return this.cache.delete(token);
  }

  /**
   * Clear all cached tokens
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    oldestEntry?: number;
  } {
    let oldestEntry: number | undefined;
    
    if (this.cache.size > 0) {
      oldestEntry = Math.min(...Array.from(this.cache.values()).map(entry => entry.createdAt));
    }

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: this.hitCount / Math.max(this.requestCount, 1),
      oldestEntry,
    };
  }

  /**
   * Start automatic cleanup of expired entries
   */
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Stop automatic cleanup
   */
  stop(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }

  /**
   * Remove expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredTokens: string[] = [];

    for (const [token, entry] of this.cache.entries()) {
      if (entry.expires <= now) {
        expiredTokens.push(token);
      }
    }

    expiredTokens.forEach(token => this.cache.delete(token));

    if (expiredTokens.length > 0) {
      console.log(`🧹 [TokenCache] Cleaned up ${expiredTokens.length} expired tokens`);
    }
  }

  /**
   * Evict oldest entries when cache is full
   */
  private evictOldest(): void {
    if (this.cache.size === 0) return;

    // Find the oldest entry
    let oldestToken: string | null = null;
    let oldestTime = Infinity;

    for (const [token, entry] of this.cache.entries()) {
      if (entry.createdAt < oldestTime) {
        oldestTime = entry.createdAt;
        oldestToken = token;
      }
    }

    if (oldestToken) {
      this.cache.delete(oldestToken);
      console.log(`🗑️ [TokenCache] Evicted oldest token to make space`);
    }
  }

  // Statistics tracking
  private hitCount = 0;
  private requestCount = 0;

  /**
   * Track cache hit/miss for statistics
   */
  private trackRequest(hit: boolean): void {
    this.requestCount++;
    if (hit) {
      this.hitCount++;
    }
  }

  /**
   * Get with statistics tracking
   */
  getWithStats(token: string): AuthInfo | null {
    const result = this.get(token);
    this.trackRequest(result !== null);
    return result;
  }
}

/**
 * Global token cache instance
 */
export const globalTokenCache = new TokenCache();

/**
 * Create a cached token verification function with logging
 */
export function createCachedTokenVerifier(
  originalVerifier: (token: string) => Promise<AuthInfo>,
  cache: TokenCache = globalTokenCache
) {
  return async (token: string): Promise<AuthInfo> => {
    // Try cache first
    const cached = cache.getWithStats(token);
    if (cached) {
      console.log(`🎯 [TokenCache] Cache hit for token`);

      // Import logger dynamically to avoid circular dependency
      const { globalOAuth2Logger } = await import('./oauth-logger.js');
      globalOAuth2Logger.logCacheEvent(true, cached.clientId);

      return cached;
    }

    console.log(`🔍 [TokenCache] Cache miss, validating with Keycloak`);

    try {
      // Validate with original verifier
      const authInfo = await originalVerifier(token);

      // Cache the result
      cache.set(token, authInfo);

      console.log(`✅ [TokenCache] Token validated and cached`);

      // Import logger dynamically to avoid circular dependency
      const { globalOAuth2Logger } = await import('./oauth-logger.js');
      globalOAuth2Logger.logCacheEvent(false, authInfo.clientId);

      return authInfo;
    } catch (error) {
      console.log(`❌ [TokenCache] Token validation failed: ${error}`);

      // Import logger dynamically to avoid circular dependency
      const { globalOAuth2Logger } = await import('./oauth-logger.js');
      globalOAuth2Logger.logCacheEvent(false);

      throw error;
    }
  };
}
