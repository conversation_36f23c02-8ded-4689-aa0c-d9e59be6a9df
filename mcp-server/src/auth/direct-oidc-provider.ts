import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { OAuthClientInformationFull } from '@modelcontextprotocol/sdk/shared/auth.js';
import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { OAuth2Config } from './oauth-config.js';
import { createCachedTokenVerifier, globalTokenCache } from './token-cache.js';
import { globalOAuth2Logger } from './oauth-logger.js';

/**
 * Create a direct OIDC provider using only MCP SDK ProxyOAuthServerProvider
 * This eliminates the need for any custom provider class
 */
export function createOIDCProvider(config: OAuth2Config): ProxyOAuthServerProvider {
  console.error('🔧 Creating direct OIDC provider (no custom class needed):');
  console.error(`   OIDC Issuer: ${config.keycloak.issuer}`);
  console.error(`   Authorization URL: ${config.keycloak.authUrl}`);
  console.error(`   Token URL: ${config.keycloak.tokenUrl}`);
  console.error(`   UserInfo URL: ${config.keycloak.userInfoUrl}`);

  // Create original token verification function using JWT validation (without caching)
  const originalVerifyAccessToken = async (token: string): Promise<AuthInfo> => {
    const startTime = Date.now();
    try {
      console.error('🔍 [OIDC] JWT token validation started:', {
        issuer: config.keycloak.issuer,
        tokenLength: token.length
      });

      // Import JWT libraries
      const jwt = await import('jsonwebtoken');
      const jwksClient = await import('jwks-client') as any;

      // Create JWKS client with minimal configuration
      const client = jwksClient.default({
        jwksUri: config.keycloak.jwksUrl,
        cache: false, // Disable cache to avoid version issues
      });

      // Get signing key
      const getKey = (header: any, callback: any) => {
        client.getSigningKey(header.kid, (err: any, key: any) => {
          if (err) {
            callback(err);
            return;
          }
          const signingKey = key.getPublicKey();
          callback(null, signingKey);
        });
      };

      // Verify and decode JWT
      const payload = await new Promise<any>((resolve, reject) => {
        jwt.verify(token, getKey, {
          issuer: config.keycloak.issuer,
          algorithms: ['RS256'],
        }, (err: any, decoded: any) => {
          if (err) {
            reject(err);
          } else {
            resolve(decoded);
          }
        });
      });

      // Get user info from userinfo endpoint
      const userInfoResponse = await fetch(config.keycloak.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      const userInfo = userInfoResponse.ok ? await userInfoResponse.json() : null;

      const duration = Date.now() - startTime;
      const clientId = payload.azp || payload.client_id || payload.aud;
      const scopes = payload.scope ? payload.scope.split(' ') : (payload.scopes || []);

      console.error('🔍 [OIDC] JWT validation successful:', {
        clientId,
        scopes,
        sub: payload.sub,
        exp: payload.exp,
        duration: `${duration}ms`,
      });

      // Log successful token validation
      globalOAuth2Logger.logTokenValidation(clientId, true, false, duration);

      const authInfo: AuthInfo = {
        token,
        clientId,
        scopes,
        expiresAt: payload.exp,
        extra: {
          sub: payload.sub,
          iat: payload.iat,
          userInfo,
          jwtPayload: payload,
        },
      };

      return authInfo;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error('❌ [OIDC] Token validation failed:', error);

      // Log failed token validation
      globalOAuth2Logger.logTokenValidation('unknown', false, false, duration);

      throw new Error(`Invalid access token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Create cached token verification function
  const verifyAccessToken = createCachedTokenVerifier(originalVerifyAccessToken, globalTokenCache);

  // Create client information function
  const getClient = async (clientId: string): Promise<OAuthClientInformationFull | undefined> => {
    // Return the configured client if it matches
    if (clientId === config.client.id) {
      return {
        client_id: config.client.id,
        client_secret: config.client.secret,
        redirect_uris: [config.client.redirectUri],
        grant_types: ['authorization_code', 'refresh_token'],
        response_types: ['code'],
        scope: config.client.scopes.join(' '),
        token_endpoint_auth_method: 'client_secret_basic',
      };
    }
    
    return undefined;
  };

  // Create and return the MCP SDK ProxyOAuthServerProvider directly
  const provider = new ProxyOAuthServerProvider({
    endpoints: {
      authorizationUrl: config.keycloak.authUrl,
      tokenUrl: config.keycloak.tokenUrl,
      revocationUrl: config.keycloak.revocationUrl,
      registrationUrl: config.keycloak.registrationUrl,
    },
    verifyAccessToken,
    getClient,
    fetch: fetch,
  });

  // Let OIDC server handle PKCE validation
  provider.skipLocalPkceValidation = true;

  console.error('✅ Direct OIDC provider created using MCP SDK ProxyOAuthServerProvider');
  
  return provider;
}
