import { ProxyOAuthServerProvider } from '@modelcontextprotocol/sdk/server/auth/providers/proxyProvider.js';
import { OAuthClientInformationFull } from '@modelcontextprotocol/sdk/shared/auth.js';
import { AuthInfo } from '@modelcontextprotocol/sdk/server/auth/types.js';
import { OAuth2Config } from './oauth-config.js';
import { createCachedTokenVerifier, globalTokenCache } from './token-cache.js';
import { globalOAuth2Logger } from './oauth-logger.js';

/**
 * Create a direct OIDC provider using only MCP SDK ProxyOAuthServerProvider
 * This eliminates the need for any custom provider class
 */
export function createOIDCProvider(config: OAuth2Config): ProxyOAuthServerProvider {
  console.error('🔧 Creating direct OIDC provider (no custom class needed):');
  console.error(`   OIDC Issuer: ${config.keycloak.issuer}`);
  console.error(`   Authorization URL: ${config.keycloak.authUrl}`);
  console.error(`   Token URL: ${config.keycloak.tokenUrl}`);
  console.error(`   UserInfo URL: ${config.keycloak.userInfoUrl}`);

  // Create original token verification function (without caching)
  const originalVerifyAccessToken = async (token: string): Promise<AuthInfo> => {
    const startTime = Date.now();
    try {
      // Use OIDC token introspection endpoint (RFC 7662)
      const introspectionUrl = `${config.keycloak.issuer}/protocol/openid-connect/token/introspect`;

      console.error('🔍 [OIDC] Token introspection request:', {
        url: introspectionUrl,
        clientId: config.client.id,
        hasClientSecret: !!config.client.secret,
        tokenLength: token.length
      });

      const response = await fetch(introspectionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${config.client.id}:${config.client.secret}`).toString('base64')}`,
        },
        body: new URLSearchParams({
          token,
          token_type_hint: 'access_token',
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔍 [OIDC] Token introspection HTTP error:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Token introspection failed: ${response.status} - ${errorText}`);
      }

      const introspectionData = await response.json() as any;

      // Enhanced logging for debugging
      console.error('🔍 [OIDC] Token introspection response:', {
        active: introspectionData.active,
        client_id: introspectionData.client_id,
        scope: introspectionData.scope,
        exp: introspectionData.exp,
        iat: introspectionData.iat,
        token_type: introspectionData.token_type,
        // Don't log the full response to avoid sensitive data
        hasSubject: !!introspectionData.sub,
        hasAudience: !!introspectionData.aud
      });

      if (!introspectionData.active) {
        console.error('❌ [OIDC] Token marked as inactive by Keycloak');
        console.error('   Possible reasons:');
        console.error('   1. Token has expired');
        console.error('   2. Token was revoked');
        console.error('   3. Wrong client credentials');
        console.error('   4. Token not issued for this client');

        // Development bypass: Try to validate token directly with userinfo endpoint
        console.error('🔧 [OIDC] Attempting direct token validation via userinfo endpoint...');

        try {
          const userInfoResponse = await fetch(config.keycloak.userInfoUrl, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Accept': 'application/json',
            },
          });

          if (userInfoResponse.ok) {
            const userInfo = await userInfoResponse.json();
            console.error('✅ [OIDC] Token is valid via userinfo endpoint!');
            console.error('   This suggests a client configuration issue, not an invalid token');
            console.error('   UserInfo:', {
              sub: userInfo.sub,
              email: userInfo.email,
              preferred_username: userInfo.preferred_username,
              client_id: userInfo.azp || userInfo.client_id
            });

            // Create a synthetic AuthInfo for development
            const authInfo: AuthInfo = {
              token,
              clientId: userInfo.azp || userInfo.client_id || 'unknown',
              scopes: ['profile', 'email', 'roles'], // Default scopes for development
              expiresAt: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
              extra: {
                sub: userInfo.sub,
                userInfo,
                introspectionData: { active: false, note: 'bypassed via userinfo' },
              },
            };

            console.error('🔧 [OIDC] Using synthetic AuthInfo for development');
            return authInfo;
          } else {
            console.error('❌ [OIDC] Token also invalid via userinfo endpoint');
          }
        } catch (userInfoError) {
          console.error('❌ [OIDC] UserInfo validation failed:', userInfoError);
        }

        throw new Error('Token is not active');
      }

      // Get additional user info from userinfo endpoint
      const userInfoResponse = await fetch(config.keycloak.userInfoUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      const userInfo = userInfoResponse.ok ? await userInfoResponse.json() : null;

      const duration = Date.now() - startTime;
      const clientId = introspectionData.client_id || introspectionData.azp;
      const scopes = introspectionData.scope ? introspectionData.scope.split(' ') : [];

      console.error('🔍 [OIDC] Token validation successful:', {
        clientId,
        scopes,
        exp: introspectionData.exp,
        duration: `${duration}ms`,
      });

      // Log successful token validation
      globalOAuth2Logger.logTokenValidation(clientId, true, false, duration);

      const authInfo: AuthInfo = {
        token,
        clientId,
        scopes,
        expiresAt: introspectionData.exp,
        extra: {
          sub: introspectionData.sub,
          iat: introspectionData.iat,
          userInfo,
          introspectionData,
        },
      };

      return authInfo;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error('❌ [OIDC] Token validation failed:', error);

      // Log failed token validation
      globalOAuth2Logger.logTokenValidation('unknown', false, false, duration);

      throw new Error(`Invalid access token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Create cached token verification function
  const verifyAccessToken = createCachedTokenVerifier(originalVerifyAccessToken, globalTokenCache);

  // Create client information function
  const getClient = async (clientId: string): Promise<OAuthClientInformationFull | undefined> => {
    // Return the configured client if it matches
    if (clientId === config.client.id) {
      return {
        client_id: config.client.id,
        client_secret: config.client.secret,
        redirect_uris: [config.client.redirectUri],
        grant_types: ['authorization_code', 'refresh_token'],
        response_types: ['code'],
        scope: config.client.scopes.join(' '),
        token_endpoint_auth_method: 'client_secret_basic',
      };
    }
    
    return undefined;
  };

  // Create and return the MCP SDK ProxyOAuthServerProvider directly
  const provider = new ProxyOAuthServerProvider({
    endpoints: {
      authorizationUrl: config.keycloak.authUrl,
      tokenUrl: config.keycloak.tokenUrl,
      revocationUrl: config.keycloak.revocationUrl,
      registrationUrl: config.keycloak.registrationUrl,
    },
    verifyAccessToken,
    getClient,
    fetch: fetch,
  });

  // Let OIDC server handle PKCE validation
  provider.skipLocalPkceValidation = true;

  console.error('✅ Direct OIDC provider created using MCP SDK ProxyOAuthServerProvider');
  
  return provider;
}
