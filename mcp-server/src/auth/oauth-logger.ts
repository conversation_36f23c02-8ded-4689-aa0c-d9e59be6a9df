/**
 * Enhanced OAuth2 logging and monitoring system
 * Provides comprehensive logging for OAuth2 flows with structured data
 */

export interface OAuth2LogEvent {
  timestamp: string;
  event: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  data?: Record<string, any>;
  duration?: number;
  clientId?: string;
  userId?: string;
  sessionId?: string;
}

export interface OAuth2Metrics {
  totalRequests: number;
  successfulAuthorizations: number;
  failedAuthorizations: number;
  tokenValidations: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  errorsByType: Record<string, number>;
  clientActivity: Record<string, number>;
}

/**
 * OAuth2 Logger with metrics collection
 */
export class OAuth2Logger {
  private events: OAuth2LogEvent[] = [];
  private metrics: OAuth2Metrics = {
    totalRequests: 0,
    successfulAuthorizations: 0,
    failedAuthorizations: 0,
    tokenValidations: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageResponseTime: 0,
    errorsByType: {},
    clientActivity: {},
  };
  private responseTimes: number[] = [];
  private maxEvents: number;

  constructor(maxEvents: number = 1000) {
    this.maxEvents = maxEvents;
  }

  /**
   * Log an OAuth2 event
   */
  log(event: Omit<OAuth2LogEvent, 'timestamp'>): void {
    const logEvent: OAuth2LogEvent = {
      ...event,
      timestamp: new Date().toISOString(),
    };

    // Add to events array
    this.events.push(logEvent);
    
    // Maintain max events limit
    if (this.events.length > this.maxEvents) {
      this.events.shift();
    }

    // Update metrics
    this.updateMetrics(logEvent);

    // Console logging with structured format
    this.consoleLog(logEvent);
  }

  /**
   * Log authorization request
   */
  logAuthorizationRequest(clientId: string, scopes: string[], data?: Record<string, any>): void {
    this.log({
      event: 'authorization_request',
      level: 'info',
      clientId,
      data: {
        scopes,
        ...data,
      },
    });
  }

  /**
   * Log successful authorization
   */
  logAuthorizationSuccess(clientId: string, userId: string, scopes: string[], duration?: number): void {
    this.log({
      event: 'authorization_success',
      level: 'info',
      clientId,
      userId,
      duration,
      data: { scopes },
    });
  }

  /**
   * Log authorization failure
   */
  logAuthorizationFailure(clientId: string, error: string, data?: Record<string, any>): void {
    this.log({
      event: 'authorization_failure',
      level: 'error',
      clientId,
      data: {
        error,
        ...data,
      },
    });
  }

  /**
   * Log token validation
   */
  logTokenValidation(clientId: string, success: boolean, cached: boolean, duration?: number): void {
    this.log({
      event: 'token_validation',
      level: success ? 'info' : 'warn',
      clientId,
      duration,
      data: {
        success,
        cached,
      },
    });
  }

  /**
   * Log cache hit/miss
   */
  logCacheEvent(hit: boolean, clientId?: string): void {
    this.log({
      event: hit ? 'cache_hit' : 'cache_miss',
      level: 'debug',
      clientId,
      data: { hit },
    });
  }

  /**
   * Log scope validation
   */
  logScopeValidation(
    clientId: string,
    userScopes: string[],
    requiredScopes: string[],
    validationMode: string,
    success: boolean
  ): void {
    this.log({
      event: 'scope_validation',
      level: success ? 'info' : 'warn',
      clientId,
      data: {
        userScopes,
        requiredScopes,
        validationMode,
        success,
      },
    });
  }

  /**
   * Get recent events
   */
  getEvents(limit?: number): OAuth2LogEvent[] {
    const events = this.events.slice();
    return limit ? events.slice(-limit) : events;
  }

  /**
   * Get current metrics
   */
  getMetrics(): OAuth2Metrics {
    return {
      ...this.metrics,
      averageResponseTime: this.calculateAverageResponseTime(),
    };
  }

  /**
   * Get events by type
   */
  getEventsByType(eventType: string): OAuth2LogEvent[] {
    return this.events.filter(event => event.event === eventType);
  }

  /**
   * Get events by client
   */
  getEventsByClient(clientId: string): OAuth2LogEvent[] {
    return this.events.filter(event => event.clientId === clientId);
  }

  /**
   * Clear all events and reset metrics
   */
  clear(): void {
    this.events = [];
    this.responseTimes = [];
    this.metrics = {
      totalRequests: 0,
      successfulAuthorizations: 0,
      failedAuthorizations: 0,
      tokenValidations: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      errorsByType: {},
      clientActivity: {},
    };
  }

  /**
   * Update metrics based on log event
   */
  private updateMetrics(event: OAuth2LogEvent): void {
    this.metrics.totalRequests++;

    // Track response times
    if (event.duration) {
      this.responseTimes.push(event.duration);
      // Keep only last 100 response times for average calculation
      if (this.responseTimes.length > 100) {
        this.responseTimes.shift();
      }
    }

    // Track client activity
    if (event.clientId) {
      this.metrics.clientActivity[event.clientId] = 
        (this.metrics.clientActivity[event.clientId] || 0) + 1;
    }

    // Event-specific metrics
    switch (event.event) {
      case 'authorization_success':
        this.metrics.successfulAuthorizations++;
        break;
      case 'authorization_failure':
        this.metrics.failedAuthorizations++;
        if (event.data?.error) {
          this.metrics.errorsByType[event.data.error] = 
            (this.metrics.errorsByType[event.data.error] || 0) + 1;
        }
        break;
      case 'token_validation':
        this.metrics.tokenValidations++;
        break;
      case 'cache_hit':
        this.metrics.cacheHits++;
        break;
      case 'cache_miss':
        this.metrics.cacheMisses++;
        break;
    }
  }

  /**
   * Calculate average response time
   */
  private calculateAverageResponseTime(): number {
    if (this.responseTimes.length === 0) return 0;
    const sum = this.responseTimes.reduce((acc, time) => acc + time, 0);
    return Math.round(sum / this.responseTimes.length);
  }

  /**
   * Console logging with emoji and colors
   */
  private consoleLog(event: OAuth2LogEvent): void {
    const emoji = this.getEventEmoji(event.event);
    const level = event.level.toUpperCase().padEnd(5);
    const timestamp = event.timestamp.split('T')[1].split('.')[0];
    
    let message = `${emoji} [OAuth2][${level}] ${timestamp} ${event.event}`;
    
    if (event.clientId) {
      message += ` [${event.clientId}]`;
    }
    
    if (event.duration) {
      message += ` (${event.duration}ms)`;
    }

    console.error(message);
    
    // Log additional data if present
    if (event.data && Object.keys(event.data).length > 0) {
      console.error(`   Data:`, event.data);
    }
  }

  /**
   * Get emoji for event type
   */
  private getEventEmoji(eventType: string): string {
    const emojiMap: Record<string, string> = {
      authorization_request: '🔐',
      authorization_success: '✅',
      authorization_failure: '❌',
      token_validation: '🔍',
      cache_hit: '🎯',
      cache_miss: '🔍',
      scope_validation: '🛡️',
    };
    return emojiMap[eventType] || '📝';
  }
}

/**
 * Global OAuth2 logger instance
 */
export const globalOAuth2Logger = new OAuth2Logger();
