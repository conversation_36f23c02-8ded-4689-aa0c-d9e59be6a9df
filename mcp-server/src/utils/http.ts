import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { getEnvironmentConfig } from './env.js';

export interface HttpConfig extends AxiosRequestConfig {
  useKong?: boolean;
  cookies?: string;
  token?: string; // OAuth2 Bearer token for authentication
}

export default async function http(config: HttpConfig): Promise<AxiosResponse> {
  const env = getEnvironmentConfig();
  const timestamp = new Date().toISOString();

  // Choose base URL based on useKong flag
  const baseURL = config.useKong ? env.VITE_API_HOST_KONG : env.VITE_API_HOST;
  const fullUrl = `${baseURL}${config.url || ''}`;

  const defaultConfig: AxiosRequestConfig = {
    baseURL,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'orders-portal-mcp-server/1.0.0',
      'X-Custom-Referrer': env.X_CUSTOM_REFERRER, // 使用环境变量配置的 referrer
      'Connection': 'close', // 避免连接复用问题
    },
    timeout: config.timeout || 60000, // 增加到60秒，允许自定义
    withCredentials: true, // Enable cookie support
    // 网络适配配置
    maxRedirects: 5,
    validateStatus: (status) => status < 500, // 接受所有非5xx状态码
    // 禁用HTTP代理自动检测，避免代理问题
    proxy: false,
    // 添加重试和连接配置
    httpAgent: undefined, // 让axios使用默认agent
    httpsAgent: undefined,
    ...config,
  };

  // Add authentication headers
  if (config.token) {
    // Prefer OAuth2 Bearer token when available
    defaultConfig.headers = {
      ...defaultConfig.headers,
      'Authorization': `Bearer ${config.token}`,
    };
    console.error(`🔐 [HTTP] Using OAuth2 Bearer token for authentication`);
  } else if (config.cookies) {
    // Fallback to cookies for backward compatibility
    defaultConfig.headers = {
      ...defaultConfig.headers,
      'Cookie': config.cookies,
    };
    console.error(`🍪 [HTTP] Using cookies for authentication (fallback)`);
  }

  // Remove custom properties
  delete (defaultConfig as any).useKong;
  delete (defaultConfig as any).cookies;
  delete (defaultConfig as any).token;

  // Enhanced debug logging
  console.error(`\n🕐 [${timestamp}] ==================== HTTP REQUEST ====================`);
  console.error(`🌐 Method: ${defaultConfig.method?.toUpperCase()}`);
  console.error(`🌐 Full URL: ${fullUrl}`);
  console.error(`🌐 Base URL: ${baseURL}`);
  console.error(`🌐 Path: ${defaultConfig.url || '/'}`);
  console.error(`🌐 Using Kong: ${config.useKong ? 'YES' : 'NO'}`);
  console.error(`🌐 Timeout: ${defaultConfig.timeout}ms`);
  console.error(`📋 Headers:`, JSON.stringify(defaultConfig.headers, null, 2));

  if (defaultConfig.data) {
    console.error(`📦 Request Body:`, JSON.stringify(defaultConfig.data, null, 2));
  } else {
    console.error(`📦 Request Body: (empty)`);
  }

  console.error(`🔧 Environment Config:`, JSON.stringify(env, null, 2));
  console.error(`================================================================\n`);

  const startTime = Date.now();

  try {
    console.error(`⏳ Sending HTTP request...`);
    const response = await axios(defaultConfig);
    const duration = Date.now() - startTime;

    console.error(`\n✅ [${new Date().toISOString()}] ==================== HTTP SUCCESS ====================`);
    console.error(`✅ Status: ${response.status} ${response.statusText}`);
    console.error(`✅ Duration: ${duration}ms`);
    console.error(`📋 Response Headers:`, JSON.stringify(response.headers, null, 2));

    if (response.data) {
      console.error(`📦 Response Body:`, JSON.stringify(response.data, null, 2));
    } else {
      console.error(`📦 Response Body: (empty)`);
    }
    console.error(`================================================================\n`);

    return response;
  } catch (error: any) {
    const duration = Date.now() - startTime;

    console.error(`\n❌ [${new Date().toISOString()}] ==================== HTTP ERROR ====================`);
    console.error(`❌ Duration: ${duration}ms`);
    console.error(`❌ Error Type: ${error.constructor.name}`);
    console.error(`❌ Error Message: ${error.message}`);
    console.error(`❌ Error Code: ${error.code || 'N/A'}`);

    if (error.response) {
      // Server responded with error status
      console.error(`❌ Response Status: ${error.response.status} ${error.response.statusText}`);
      console.error(`📋 Response Headers:`, JSON.stringify(error.response.headers, null, 2));

      if (error.response.data) {
        console.error(`📦 Error Response Body:`, JSON.stringify(error.response.data, null, 2));
      } else {
        console.error(`📦 Error Response Body: (empty)`);
      }
    } else if (error.request) {
      // Request was made but no response received
      console.error(`❌ No response received`);
      console.error(`📡 Request details:`, {
        method: error.request.method,
        url: error.request.url,
        headers: error.request.headers,
      });
    } else {
      // Something else happened
      console.error(`❌ Request setup error`);
    }

    console.error(`🔍 Full error object:`, JSON.stringify({
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      stack: error.stack,
    }, null, 2));
    console.error(`================================================================\n`);

    throw error;
  }
}
