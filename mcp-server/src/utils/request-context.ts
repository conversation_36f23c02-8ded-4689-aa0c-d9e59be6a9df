/**
 * Request context management for MCP server
 * Provides a way to pass authentication and user information from HTTP transport to service functions
 */

export interface UserContext {
  email?: string;
  clientId?: string;
  scopes?: string[];
  token?: string;
  userInfo?: any;
}

export interface RequestContext {
  user?: UserContext;
  requestId?: string;
  timestamp?: string;
}

// Global context storage using AsyncLocalStorage for thread-safe context passing
import { AsyncLocalStorage } from 'async_hooks';

const requestContextStorage = new AsyncLocalStorage<RequestContext>();

/**
 * Set the current request context
 */
export function setRequestContext(context: RequestContext): void {
  requestContextStorage.enterWith(context);
}

/**
 * Get the current request context
 */
export function getRequestContext(): RequestContext | undefined {
  return requestContextStorage.getStore();
}

/**
 * Get the current user context
 */
export function getCurrentUser(): UserContext | undefined {
  const context = getRequestContext();
  return context?.user;
}

/**
 * Get the current user's token for downstream API calls
 */
export function getCurrentUserToken(): string | undefined {
  const user = getCurrentUser();
  return user?.token;
}

/**
 * Run a function with a specific request context
 */
export function runWithContext<T>(context: RequestContext, fn: () => T): T {
  return requestContextStorage.run(context, fn);
}

/**
 * Create a request context from Express request
 */
export function createRequestContextFromExpress(req: any): RequestContext {
  const auth = req.auth;
  const requestId = req.headers['x-request-id'] || Math.random().toString(36).substring(2, 15);
  
  let user: UserContext | undefined;
  
  if (auth) {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const userInfo = auth.userInfo;
    const email = userInfo?.email || userInfo?.preferred_username;
    
    user = {
      email,
      clientId: auth.clientId,
      scopes: auth.scopes,
      token,
      userInfo
    };
  }
  
  return {
    user,
    requestId,
    timestamp: new Date().toISOString()
  };
}
