/**
 * HTTP 适配器 - 为 Node.js 环境模拟浏览器环境
 * 
 * 这个适配器的作用：
 * 1. 模拟浏览器的全局对象（window, location, ENV等）
 * 2. 提供兼容的 HTTP 客户端，替换原始服务中的 axios 实例
 * 3. 处理认证和环境差异
 */

import { getEnvironmentConfig } from '../utils/env.js';

/**
 * 注入全局环境变量，模拟浏览器环境
 */
export function injectGlobalEnvironment() {
  const env = getEnvironmentConfig();
  
  // 模拟 window 对象
  if (typeof global !== 'undefined' && !(global as any).window) {
    (global as any).window = {
      location: {
        search: '',
        href: 'https://fe-dev-i.ingka-dt.cn/order-web',
        protocol: 'https:',
        host: 'fe-dev-i.ingka-dt.cn',
        pathname: '/order-web',
        origin: 'https://fe-dev-i.ingka-dt.cn',
      },
    };
  }

  // 模拟 location 对象
  if (typeof global !== 'undefined' && !(global as any).location) {
    (global as any).location = (global as any).window?.location || {
      search: '',
      href: 'https://fe-dev-i.ingka-dt.cn/order-web',
      protocol: 'https:',
      host: 'fe-dev-i.ingka-dt.cn',
      pathname: '/order-web',
      origin: 'https://fe-dev-i.ingka-dt.cn',
    };
  }

  // 模拟 ENV 对象 (来自 @/utils)
  if (typeof global !== 'undefined' && !(global as any).ENV) {
    (global as any).ENV = {
      VITE_API_HOST: env.VITE_API_HOST,
      VITE_API_HOST_KONG: env.VITE_API_HOST_KONG,
      VITE_MASTER_DATA_API_HOST: env.VITE_MASTER_DATA_API_HOST,
      VITE_MASTER_DATA_API_KEY: env.VITE_MASTER_DATA_API_KEY,
      MODE: env.NODE_ENV === 'production' ? 'prod' : 'dev',
      DEV: env.NODE_ENV !== 'production',
      PROD: env.NODE_ENV === 'production',
    };
  }

  console.error('🌍 [HttpAdapter] Global environment injected successfully');
  console.error(`   ENV.VITE_API_HOST: ${(global as any).ENV?.VITE_API_HOST}`);
  console.error(`   ENV.VITE_API_HOST_KONG: ${(global as any).ENV?.VITE_API_HOST_KONG}`);
  console.error(`   location.href: ${(global as any).location?.href}`);
}

/**
 * 创建兼容的 HTTP 客户端
 * 这个函数会被原始服务代码调用，替换它们的 axios 实例
 */
export async function createCompatibleHttpClient() {
  const { default: http } = await import('../utils/http.js');
  const env = getEnvironmentConfig();
  
  // 返回一个兼容的 HTTP 客户端函数
  return async function compatibleHttp(config: any) {
    // 转换配置格式，添加认证
    const mcpConfig = {
      ...config,
      useKong: true, // 默认使用 Kong
      cookies: env.AUTH_COOKIES,
    };
    
    console.error(`🌐 [HttpAdapter] Making HTTP request:`, {
      url: mcpConfig.url,
      method: mcpConfig.method || 'POST',
      useKong: mcpConfig.useKong,
      hasCookies: !!mcpConfig.cookies,
    });
    
    return await http(mcpConfig);
  };
}

/**
 * 安全地序列化对象，避免循环引用
 */
export function safeStringify(obj: any, space?: number): string {
  const seen = new WeakSet();

  try {
    return JSON.stringify(obj, (_key, value) => {
      // 处理循环引用
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular Reference]';
        }
        seen.add(value);

        // 检查是否是 HTTP 相关的对象
        if (value.constructor) {
          const constructorName = value.constructor.name;
          if (['ClientRequest', 'IncomingMessage', 'Socket', 'TLSSocket', 'AxiosError'].includes(constructorName)) {
            return `[${constructorName} Object]`;
          }
        }
      }

      return value;
    }, space);
  } catch (error: any) {
    // 最后的安全网
    return `[Serialization Error: ${error?.message || 'Unknown error'}]`;
  }
}

/**
 * 提取 axios 错误的安全信息
 */
export function extractSafeErrorInfo(error: any) {
  const safeError = {
    message: error.message || 'Unknown error',
    name: error.name || 'Error',
    code: error.code,
    stack: error.stack ? error.stack.split('\n').slice(0, 5).join('\n') : undefined,
  };

  // HTTP 响应信息
  if (error.response) {
    (safeError as any).response = {
      status: error.response.status,
      statusText: error.response.statusText,
      headers: error.response.headers,
      data: error.response.data,
    };
  }

  // HTTP 请求信息
  if (error.config) {
    (safeError as any).request = {
      url: error.config.url,
      method: error.config.method?.toUpperCase(),
      baseURL: error.config.baseURL,
      timeout: error.config.timeout,
      headers: error.config.headers,
    };
  }

  return safeError;
}
