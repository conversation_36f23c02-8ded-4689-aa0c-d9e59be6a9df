# Orders Portal MCP Server

A Model Context Protocol (MCP) server providing access to Orders Portal Web services through standardized MCP tools.

## 🚀 Quick Start

```bash
# 1. Install dependencies
npm install

# 2. Configure authentication
cp .env.example .env
# Edit .env and set AUTH_COOKIES

# 3. Build and start
npm run build
npm start

# 4. Test with MCP Inspector
npm run inspect
```

## 🛠️ Available Tools

### 📦 OMS (Order Management) - 6 Tools
- `oms_queryOrderLists` - Query orders with pagination/filters
- `oms_getOrderDetail` - Get detailed order information
- `oms_getLogisticsInfo` - Get order logistics information
- `oms_getAssemblingInfo` - Get order assembling information
- `oms_searchPermissionStore` - Search user permission stores
- `oms_getRetrievalData` - Get order retrieval data

### 🌐 Global Services - 1 Tool
- `global_getCurrentUser` - Get current user information

### 🧪 Testing Tools - 2 Tools
- `test_ping` - Quick connectivity test
- `test_echo` - Echo test for debugging

## 📋 Requirements

- **Node.js** 18+
- **Valid authentication cookies** in `.env` file

## 🔧 Development

```bash
# Development mode with auto-reload
npm run dev

# Run tests
npm run test:quick    # Quick health check
npm run test:auth     # Authentication test
npm run test:tools    # All tools test

# MCP Inspector (interactive testing)
npm run inspect       # Development mode
npm run inspect:build # Production build mode
```

## 📊 Authentication

Set authentication cookies in `.env`:

```bash
AUTH_COOKIES=session_id=abc123; auth_token=xyz789
```

Get cookies from your browser's developer tools when logged into Orders Portal Web.

## 🏗️ Project Structure

```
mcp-server/
├── src/
│   ├── adapters/     # Service implementations
│   ├── auth/         # OAuth2 authentication (optional)
│   ├── transport/    # HTTP/stdio transport
│   ├── utils/        # Utilities
│   └── server.ts     # MCP server setup
├── test/             # Test suite
├── docs/             # Documentation
└── dist/             # Compiled output
```

## 📚 Documentation

- [Testing Guide](docs/testing.md) - How to test the server
- [OAuth2 Setup](docs/oauth2.md) - OAuth2 authentication (optional)
- [API Reference](docs/api.md) - Tool parameters and responses

## 🚨 Troubleshooting

**401 Errors**: Update `AUTH_COOKIES` in `.env` file
**Tool Not Found**: Run `npm run build` first
**Slow Responses**: Check network connectivity to API endpoints

## 📄 License

MIT License - see LICENSE file for details.