# API Reference

Complete reference for all MCP tools and their parameters.

## OMS Tools

### `oms_queryOrderLists`
Query order lists with pagination and filters.

**Parameters:**
- `page` (number, default: 1) - Page number
- `pageSize` (number, default: 10) - Items per page (1-100)
- `orderStatus` (string, optional) - Filter by order status
- `storeCode` (string, optional) - Filter by store code
- `startDate` (string, optional) - Start date (YYYY-MM-DD)
- `endDate` (string, optional) - End date (YYYY-MM-DD)

### `oms_getOrderDetail`
Get detailed information for a specific order.

**Parameters:**
- `vid` (string, required) - Order VID
- `orderNO` (string, optional) - Order number
- `ikeaOrderNO` (string, optional) - IKEA order number

### `oms_getLogisticsInfo`
Get logistics information for an order.

**Parameters:**
- `vid` (string, required) - Order VID

### `oms_getAssemblingInfo`
Get assembling information for an order.

**Parameters:**
- `vid` (string, required) - Order VID

### `oms_searchPermissionStore`
Search permission stores for current user.

**Parameters:** None

### `oms_getRetrievalData`
Get retrieval data for an order.

**Parameters:**
- `vid` (string, required) - Order VID

## Global Tools

### `global_getCurrentUser`
Get current user information and permissions.

**Parameters:** None

## Test Tools

### `test_ping`
Quick connectivity test.

**Parameters:** None

### `test_echo`
Echo test for debugging.

**Parameters:**
- `message` (string, optional) - Message to echo
- `data` (any, optional) - Data to echo

## Response Format

All tools return responses in this format:

```json
{
  "success": true,
  "data": { ... },
  "meta": {
    "module": "oms",
    "function": "queryOrderLists",
    "requestId": "abc123",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## Error Handling

Errors return:

```json
{
  "success": false,
  "error": "Error message",
  "details": { ... },
  "meta": { ... }
}
```

Common error codes:
- **401**: Authentication required
- **403**: Insufficient permissions
- **404**: Resource not found
- **500**: Server error
