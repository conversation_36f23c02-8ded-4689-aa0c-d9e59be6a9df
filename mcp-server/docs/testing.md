# Testing Guide

This guide covers how to test the Orders Portal MCP Server.

## Quick Testing

```bash
# Quick health check (recommended)
npm run test:quick

# Test authentication
npm run test:auth

# Test all tools
npm run test:tools
```

## Test Files

- **`test/quick-test.js`** - Quick health check and response time test
- **`test/auth-cookies.js`** - Authentication verification test  
- **`test/all-tools.js`** - Comprehensive test of all MCP tools

## Requirements

- Valid `AUTH_COOKIES` in `.env` file
- Built server (`npm run build`)

## Expected Results

- **Fast tools** (ping/echo): < 100ms
- **Network tools** (API calls): 100-500ms  
- **Success rate**: 90-100% with valid auth

## Troubleshooting

- **401 Errors**: Update AUTH_COOKIES
- **Import Errors**: Run `npm run build`
- **Slow Responses**: Check network connectivity
