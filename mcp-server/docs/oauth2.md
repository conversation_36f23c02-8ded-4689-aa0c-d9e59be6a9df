# OAuth2 Authentication

OAuth2 authentication with Key<PERSON>loak (optional feature).

## Quick Setup

1. **Enable OAuth2** in `.env`:
```bash
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn/auth
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=your-client-id
OAUTH2_CLIENT_SECRET=your-client-secret
```

2. **Test OAuth2**:
```bash
npm run keycloak:test
```

3. **Start with OAuth2**:
```bash
npm run oauth2:dev
```

## Keycloak Client Setup

1. Login to Keycloak Admin Console
2. Create new client with your CLIENT_ID
3. Enable Client Authentication
4. Set Valid Redirect URIs: `http://localhost:3000/*`
5. Copy Client Secret to `.env`

## OAuth2 Scripts

- `keycloak:test` - Test Keycloak connectivity
- `keycloak:configure` - Configure Keycloak settings
- `oauth2:dev` - Start server with OAuth2 enabled

## Note

OAuth2 is optional. Cookie-based authentication works without Keycloak setup.
