# MCP Server Authentication Implementation

## Overview

This document describes the complete OAuth2 authentication implementation for the MCP server, following your design:

1. **[A] Client → [B] MCP Server**: Keycloak + OAuth2 for authentication
2. **[B] MCP Server → [C] Order Service**: Token forwarding for authorization  
3. **[C] Order Service**: Email-based permission decisions

## ✅ Implementation Status

All authentication components have been successfully implemented and tested:

- ✅ OAuth2 authentication on MCP endpoints
- ✅ Token extraction and forwarding to downstream services
- ✅ User context extraction for audit logging
- ✅ OAuth2-compliant error handling
- ✅ Comprehensive test suite

## Architecture

### Authentication Flow

```
[Client] --OAuth2--> [MCP Server] --Token--> [Order Service]
    |                      |                      |
    |                      |                      |
 Bearer Token         Validates &             Extracts Email
                      Forwards Token          for Permissions
```

### Key Components

1. **HTTP Transport** (`src/transport/http-transport.ts`)
   - Applies OAuth2 middleware to `/mcp` endpoints
   - Extracts user context from authenticated requests
   - Provides OAuth2-compliant error responses

2. **Request Context** (`src/utils/request-context.ts`)
   - Thread-safe context passing using AsyncLocalStorage
   - Stores user information and tokens for downstream use

3. **Service Adapter** (`src/adapters/service-adapter.ts`)
   - Automatically forwards OAuth2 tokens to order service APIs
   - Falls back to cookies when tokens unavailable
   - Enhanced audit logging with user context

4. **Authentication Middleware** (`src/auth/middleware.ts`)
   - Scope-based authorization (read, write, admin)
   - Token validation and caching
   - Comprehensive logging

## Security Features

### 1. MCP Endpoint Protection

All MCP endpoints now require authentication:

```typescript
// Protected endpoints
this.app.post('/mcp', requireReadAccess(this.oauth2Provider), handler);
this.app.get('/mcp', requireReadAccess(this.oauth2Provider), handler);  
this.app.delete('/mcp', requireReadAccess(this.oauth2Provider), handler);
```

### 2. Token Forwarding

Authenticated tokens are automatically forwarded to downstream services:

```typescript
// Prefer OAuth2 token, fallback to cookies
if (userToken) {
  return http({ url, method, data, useKong: true, token: userToken });
} else if (env.AUTH_COOKIES) {
  return http({ url, method, data, useKong: true, cookies: env.AUTH_COOKIES });
}
```

### 3. Audit Logging

Comprehensive logging includes user context:

```typescript
console.error(`🔧 Tool execution by user: ${user?.email || user?.clientId}`);
console.error(`   Scopes: ${user?.scopes?.join(', ')}`);
console.error(`   Request ID: ${requestId}`);
```

## Configuration

### Environment Variables

```bash
# Enable OAuth2 authentication
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=https://keycloak.ingka-dt.cn
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=your-client-id
OAUTH2_CLIENT_SECRET=your-client-secret

# Fallback authentication (optional)
AUTH_COOKIES=your-session-cookies
```

### Scopes

The system supports hierarchical scopes:

- `openid`: Required for all authenticated requests
- `read`: Read-only access to tools and resources
- `write`: Write access for data modification
- `admin`: Administrative access

## Usage Examples

### 1. Starting with OAuth2

```bash
# Development with OAuth2
OAUTH2_ENABLED=true npm run dev

# Production
OAUTH2_ENABLED=true npm start
```

### 2. Client Authentication

```bash
# Client must include Bearer token
curl -H "Authorization: Bearer your-token" \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc":"2.0","method":"tools/list","id":1}' \
     http://localhost:3000/mcp
```

### 3. Unauthorized Access

```bash
# Returns 401 with WWW-Authenticate header
curl -H "Content-Type: application/json" \
     -d '{"jsonrpc":"2.0","method":"tools/list","id":1}' \
     http://localhost:3000/mcp
```

## Testing

### Run Authentication Tests

```bash
# Test token forwarding logic
npm run test:token-forwarding

# Test authentication flow
npm run test:auth-flow

# Test with MCP Inspector
npm run inspect
```

### Test Results

All authentication tests pass:

```
🎉 All token forwarding tests passed!
✅ Request Context Creation
✅ Context Storage  
✅ Token Forwarding
✅ Unauthenticated Context Handling
Success Rate: 100.0%
```

## Benefits

### 1. Security
- **Zero unauthorized access**: All MCP endpoints protected
- **Token validation**: Proper OAuth2 token verification
- **Scope-based authorization**: Granular permission control

### 2. Auditability  
- **User tracking**: Every API call logged with user context
- **Request tracing**: Unique request IDs for debugging
- **Scope logging**: Track what permissions users have

### 3. Flexibility
- **Graceful fallback**: Supports cookies when tokens unavailable
- **Environment-aware**: Different configs for dev/prod
- **Standards-compliant**: Follows OAuth2.1 and MCP specifications

## Migration Path

### From Cookie-based Auth

1. **Enable OAuth2**: Set `OAUTH2_ENABLED=true`
2. **Keep cookies**: System automatically falls back to cookies
3. **Gradual migration**: Clients can migrate to OAuth2 over time
4. **Remove cookies**: Once all clients use OAuth2

### Future Enhancements

1. **Role-based access**: Map Keycloak roles to tool permissions
2. **Rate limiting**: Per-user rate limiting based on token claims
3. **Token refresh**: Automatic token refresh for long-running sessions
4. **Metrics**: OAuth2 authentication metrics and monitoring

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check token validity and scopes
2. **403 Forbidden**: Insufficient scopes for requested operation
3. **Token forwarding fails**: Verify request context is set
4. **Keycloak connection**: Check network and configuration

### Debug Logging

Enable detailed logging:

```bash
DEBUG_SERVICE_ADAPTER=true npm run dev
```

This provides comprehensive authentication flow visibility.
