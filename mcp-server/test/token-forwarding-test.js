#!/usr/bin/env node

/**
 * Token Forwarding Test
 * Tests that OAuth2 tokens are properly forwarded to downstream services
 */

import { createRequestContextFromExpress, setRequestContext, getCurrentUserToken, getCurrentUser } from '../src/utils/request-context.js';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logStep(step, message) {
  log(`\n🔧 [Step ${step}] ${message}`, 'cyan');
}

// Mock Express request with authentication data
function createMockAuthenticatedRequest(userEmail, token, scopes = ['read', 'openid']) {
  return {
    headers: {
      authorization: `Bearer ${token}`,
      'x-request-id': 'test-request-123'
    },
    auth: {
      clientId: 'test-client',
      scopes: scopes,
      userInfo: {
        email: userEmail,
        preferred_username: userEmail,
        sub: 'user-123'
      }
    }
  };
}

// Test functions
async function testRequestContextCreation() {
  logStep(1, 'Testing Request Context Creation');
  
  const mockReq = createMockAuthenticatedRequest('<EMAIL>', 'test-token-123');
  const context = createRequestContextFromExpress(mockReq);
  
  if (context.user && context.user.email === '<EMAIL>') {
    logSuccess('Request context created successfully');
    logSuccess(`User email: ${context.user.email}`);
    logSuccess(`Client ID: ${context.user.clientId}`);
    logSuccess(`Token: ${context.user.token ? 'Present' : 'Missing'}`);
    logSuccess(`Scopes: ${context.user.scopes?.join(', ')}`);
    return true;
  } else {
    logError('Failed to create request context');
    return false;
  }
}

async function testContextStorage() {
  logStep(2, 'Testing Context Storage and Retrieval');
  
  const mockReq = createMockAuthenticatedRequest('<EMAIL>', 'storage-token-456');
  const context = createRequestContextFromExpress(mockReq);
  
  // Set context
  setRequestContext(context);
  
  // Retrieve context
  const retrievedUser = getCurrentUser();
  const retrievedToken = getCurrentUserToken();
  
  if (retrievedUser && retrievedUser.email === '<EMAIL>') {
    logSuccess('Context storage and retrieval working');
    logSuccess(`Retrieved user: ${retrievedUser.email}`);
    logSuccess(`Retrieved token: ${retrievedToken ? 'Present' : 'Missing'}`);
    return true;
  } else {
    logError('Failed to store/retrieve context');
    return false;
  }
}

async function testTokenForwarding() {
  logStep(3, 'Testing Token Forwarding Logic');
  
  // Import the service adapter to test token forwarding
  try {
    const { getCurrentUserToken, getCurrentUser } = await import('../src/utils/request-context.js');
    
    // Mock a user context
    const mockReq = createMockAuthenticatedRequest('<EMAIL>', 'api-token-789', ['read', 'write', 'openid']);
    const context = createRequestContextFromExpress(mockReq);
    setRequestContext(context);
    
    // Test that the service adapter can access the token
    const token = getCurrentUserToken();
    const user = getCurrentUser();
    
    if (token === 'api-token-789' && user?.email === '<EMAIL>') {
      logSuccess('Token forwarding logic working correctly');
      logSuccess(`Token available for API calls: ${token}`);
      logSuccess(`User context available: ${user.email}`);
      logSuccess(`User scopes: ${user.scopes?.join(', ')}`);
      return true;
    } else {
      logError('Token forwarding logic failed');
      logError(`Expected token: api-token-789, got: ${token}`);
      logError(`Expected user: <EMAIL>, got: ${user?.email}`);
      return false;
    }
  } catch (error) {
    logError(`Token forwarding test failed: ${error.message}`);
    return false;
  }
}

async function testUnauthenticatedContext() {
  logStep(4, 'Testing Unauthenticated Context Handling');
  
  // Create mock request without authentication
  const mockReq = {
    headers: {
      'x-request-id': 'unauth-request-123'
    }
    // No auth property
  };
  
  const context = createRequestContextFromExpress(mockReq);
  setRequestContext(context);
  
  const user = getCurrentUser();
  const token = getCurrentUserToken();
  
  if (!user && !token) {
    logSuccess('Unauthenticated context handled correctly');
    logSuccess('No user or token available (as expected)');
    return true;
  } else {
    logError('Unauthenticated context not handled correctly');
    logError(`Unexpected user: ${user?.email}`);
    logError(`Unexpected token: ${token}`);
    return false;
  }
}

async function runAllTests() {
  log('\n🚀 Starting Token Forwarding Tests', 'cyan');
  log('=' .repeat(50), 'cyan');

  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    errors: []
  };

  const tests = [
    { name: 'Request Context Creation', fn: testRequestContextCreation },
    { name: 'Context Storage', fn: testContextStorage },
    { name: 'Token Forwarding', fn: testTokenForwarding },
    { name: 'Unauthenticated Context', fn: testUnauthenticatedContext }
  ];

  for (const test of tests) {
    try {
      results.total++;
      const success = await test.fn();
      if (success) {
        results.passed++;
      } else {
        results.failed++;
        results.errors.push(`${test.name} failed`);
      }
    } catch (error) {
      results.failed++;
      results.errors.push(`${test.name} error: ${error.message}`);
      logError(`Test ${test.name} threw error: ${error.message}`);
    }
  }

  // Summary
  log('\n📊 Test Results Summary', 'cyan');
  log('=' .repeat(50), 'cyan');
  log(`Total Tests: ${results.total}`);
  logSuccess(`Passed: ${results.passed}`);
  if (results.failed > 0) {
    logError(`Failed: ${results.failed}`);
    results.errors.forEach(error => logError(`  - ${error}`));
  }

  const successRate = ((results.passed / results.total) * 100).toFixed(1);
  log(`\nSuccess Rate: ${successRate}%`, successRate === '100.0' ? 'green' : 'yellow');

  if (results.failed === 0) {
    logSuccess('\n🎉 All token forwarding tests passed!');
    return true;
  } else {
    logError('\n💥 Some tests failed. Please check the implementation.');
    return false;
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().then((success) => {
    process.exit(success ? 0 : 1);
  }).catch((error) => {
    logError(`Test runner failed: ${error.message}`);
    process.exit(1);
  });
}

export { runAllTests };
