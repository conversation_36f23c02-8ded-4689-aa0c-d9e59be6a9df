#!/usr/bin/env node

/**
 * ⚡ Quick MCP Server Test
 *
 * Purpose:
 * 1. Test MCP server basic functionality
 * 2. Verify response times
 * 3. Provide quick health check
 */

import { SERVICE_MODULES } from '../dist/adapters/service-adapter.js';

/**
 * Test a single service function directly
 */
async function testServiceFunction(moduleName, functionName, args = {}, description = '') {
  console.log(`⚡ Testing: ${moduleName}_${functionName}`);

  const startTime = Date.now();

  try {
    const serviceModule = SERVICE_MODULES[moduleName];
    if (!serviceModule) {
      throw new Error(`Service module '${moduleName}' not found`);
    }

    const serviceFunction = serviceModule[functionName];
    if (!serviceFunction) {
      throw new Error(`Function '${functionName}' not found in module '${moduleName}'`);
    }

    const result = await serviceFunction(args);
    const duration = Date.now() - startTime;

    console.log(`✅ Success in ${duration}ms`);
    console.log(`📊 Result: ${result ? 'SUCCESS' : 'FAILED'}`);

    if (duration > 30000) {
      console.log(`⚠️  Slow response (${duration}ms) - may cause MCP timeout`);
    } else if (duration > 10000) {
      console.log(`🐌 Moderate response (${duration}ms) - acceptable but slow`);
    } else {
      console.log(`🚀 Fast response (${duration}ms) - excellent`);
    }

    return { success: true, duration, result: !!result };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.log(`❌ Failed in ${duration}ms`);
    console.log(`💥 Error: ${error.message}`);

    return { success: false, duration, error: error.message };
  }
}

/**
 * 主测试函数
 */
async function runQuickTest() {
  console.log('⚡ ==================== QUICK MCP RESPONSE TEST ====================');
  console.log('🎯 Purpose: Test MCP tool response times to diagnose timeout issues\n');
  
  const tests = [
    // Fast test tools (no network required)
    {
      module: 'test',
      function: 'ping',
      args: {},
      description: 'Quick ping test - should be instant'
    },
    {
      module: 'test',
      function: 'echo',
      args: { message: 'Hello MCP!', data: [1, 2, 3] },
      description: 'Echo test with data - should be instant'
    },

    // Network test tools (require HTTP requests)
    {
      module: 'global',
      function: 'getCurrentUser',
      args: {},
      description: 'Get current user - requires HTTP request'
    },
    {
      module: 'oms',
      function: 'searchPermissionStore',
      args: {},
      description: 'Search permission store - requires HTTP request'
    }
  ];
  
  console.log('📋 Test Plan:');
  tests.forEach((test, index) => {
    console.log(`   ${index + 1}. ${test.module}_${test.function} - ${test.description}`);
  });
  
  const results = [];
  
  // Run all tests
  for (const test of tests) {
    console.log(`\n📋 Test ${results.length + 1}/${tests.length}:`);

    const result = await testServiceFunction(
      test.module,
      test.function,
      test.args,
      test.description
    );

    results.push({
      name: `${test.module}_${test.function}`,
      ...result
    });

    // Brief delay
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // 生成报告
  console.log('\n🎯 ==================== SPEED ANALYSIS ====================');
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const durations = results.filter(r => r.success).map(r => r.duration);
  
  console.log(`📊 Test Summary:`);
  console.log(`   Total Tests: ${results.length}`);
  console.log(`   Successful: ${successful}`);
  console.log(`   Failed: ${failed}`);
  
  if (durations.length > 0) {
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const maxDuration = Math.max(...durations);
    const minDuration = Math.min(...durations);
    
    console.log(`\n⏱️  Performance Statistics:`);
    console.log(`   Average Response: ${avgDuration.toFixed(0)}ms`);
    console.log(`   Fastest Response: ${minDuration}ms`);
    console.log(`   Slowest Response: ${maxDuration}ms`);
    
    // 超时风险分析
    const slowTests = results.filter(r => r.success && r.duration > 30000);
    const moderateTests = results.filter(r => r.success && r.duration > 10000 && r.duration <= 30000);
    const fastTests = results.filter(r => r.success && r.duration <= 10000);
    
    console.log(`\n🚦 Response Time Categories:`);
    console.log(`   🚀 Fast (≤10s): ${fastTests.length} tests`);
    console.log(`   🐌 Moderate (10-30s): ${moderateTests.length} tests`);
    console.log(`   ⚠️  Slow (>30s): ${slowTests.length} tests`);
    
    if (slowTests.length > 0) {
      console.log(`\n⚠️  Slow Tests (MCP timeout risk):`);
      slowTests.forEach(test => {
        console.log(`   - ${test.name}: ${test.duration}ms`);
      });
    }
  }
  
  console.log(`\n📋 Detailed Results:`);
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const duration = result.success ? `${result.duration}ms` : 'N/A';
    const speed = result.success ? 
      (result.duration <= 10000 ? '🚀' : result.duration <= 30000 ? '🐌' : '⚠️') : '💥';
    
    console.log(`   ${status} ${speed} ${result.name.padEnd(25)} ${duration.padStart(8)}`);
  });
  
  // 诊断建议
  console.log(`\n💡 Timeout Diagnosis:`);
  
  const hasSlowTests = results.some(r => r.success && r.duration > 30000);
  const hasNetworkIssues = results.filter(r => !r.success && r.error?.includes('timeout')).length > 0;
  
  if (hasSlowTests) {
    console.log('🔍 Found slow responses (>30s):');
    console.log('   - This may cause MCP client timeouts');
    console.log('   - Consider optimizing slow operations');
    console.log('   - Check if API endpoints are responding slowly');
  }
  
  if (hasNetworkIssues) {
    console.log('🌐 Network timeout issues detected:');
    console.log('   - Check network connectivity');
    console.log('   - Verify API endpoints are accessible');
    console.log('   - Consider increasing HTTP timeout values');
  }
  
  const fastTestsOnly = results.filter(r => r.name.startsWith('test_'));
  const allFastTestsWork = fastTestsOnly.every(r => r.success && r.duration < 1000);
  
  if (allFastTestsWork && hasSlowTests) {
    console.log('🎯 Root Cause Analysis:');
    console.log('   - MCP server itself is responsive (fast tests work)');
    console.log('   - Issue is with HTTP API calls taking too long');
    console.log('   - This is likely a network/API performance issue');
  }
  
  if (!allFastTestsWork) {
    console.log('⚠️  MCP Server Issues:');
    console.log('   - Even fast tests are failing/slow');
    console.log('   - This indicates MCP server internal issues');
    console.log('   - Check server logs for errors');
  }
  
  console.log(`\n🔧 Recommendations:`);
  console.log('   1. Use fast test tools (test_ping, test_echo) to verify MCP connectivity');
  console.log('   2. If only network tools are slow, the issue is API performance');
  console.log('   3. If all tools are slow, check MCP server configuration');
  console.log('   4. Consider increasing MCP client timeout settings');
  
  console.log('\n🏁 Quick test completed!');
  
  return results;
}

// 运行快速测试
runQuickTest().catch(error => {
  console.error('\n💥 Quick test failed:', error.message);
  console.error('📋 Stack:', error.stack);
  process.exit(1);
});
