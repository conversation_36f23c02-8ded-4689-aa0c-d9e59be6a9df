#!/usr/bin/env node

/**
 * Debug Token Introspection
 * Helps debug why token introspection is failing
 */

import { config } from 'dotenv';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
config({ path: join(__dirname, '..', '.env') });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n🔧 [Step ${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function debugTokenIntrospection() {
  log('\n🔍 Token Introspection Debug Tool', 'cyan');
  log('=' .repeat(50), 'cyan');

  // Check environment configuration
  logStep(1, 'Checking Environment Configuration');
  
  const requiredEnvVars = [
    'KEYCLOAK_BASE_URL',
    'KEYCLOAK_REALM', 
    'OAUTH2_CLIENT_ID',
    'OAUTH2_CLIENT_SECRET'
  ];

  let configValid = true;
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      logSuccess(`${envVar}: ${envVar.includes('SECRET') ? '***SET***' : process.env[envVar]}`);
    } else {
      logError(`${envVar}: NOT SET`);
      configValid = false;
    }
  }

  if (!configValid) {
    logError('Environment configuration is incomplete. Please check your .env file.');
    return;
  }

  // Build configuration
  const keycloakBaseUrl = process.env.KEYCLOAK_BASE_URL;
  const keycloakRealm = process.env.KEYCLOAK_REALM;
  const clientId = process.env.OAUTH2_CLIENT_ID;
  const clientSecret = process.env.OAUTH2_CLIENT_SECRET;
  
  const realmUrl = `${keycloakBaseUrl}/realms/${keycloakRealm}`;
  const introspectionUrl = `${realmUrl}/protocol/openid-connect/token/introspect`;

  logStep(2, 'Testing Keycloak Connectivity');

  try {
    // Test realm accessibility with current URL
    log(`   Testing: ${realmUrl}/.well-known/openid_configuration`);
    const realmResponse = await fetch(`${realmUrl}/.well-known/openid_configuration`);

    if (realmResponse.ok) {
      logSuccess('Keycloak realm is accessible');
      const realmConfig = await realmResponse.json();
      log(`   Issuer: ${realmConfig.issuer}`);
      log(`   Token endpoint: ${realmConfig.token_endpoint}`);
      log(`   Introspection endpoint: ${realmConfig.introspection_endpoint}`);
    } else {
      logError(`Keycloak realm not accessible: ${realmResponse.status}`);

      // Try without /auth for modern Keycloak
      const alternativeUrl = keycloakBaseUrl.replace('/auth', '');
      const alternativeRealmUrl = `${alternativeUrl}/realms/${keycloakRealm}`;

      logWarning(`Trying alternative URL (modern Keycloak): ${alternativeRealmUrl}`);

      const altResponse = await fetch(`${alternativeRealmUrl}/.well-known/openid_configuration`);
      if (altResponse.ok) {
        logSuccess('Alternative URL works! Update your KEYCLOAK_BASE_URL');
        logWarning(`Change KEYCLOAK_BASE_URL from: ${keycloakBaseUrl}`);
        logWarning(`                            to: ${alternativeUrl}`);
        const altConfig = await altResponse.json();
        log(`   Issuer: ${altConfig.issuer}`);
        log(`   Token endpoint: ${altConfig.token_endpoint}`);
        log(`   Introspection endpoint: ${altConfig.introspection_endpoint}`);
      } else {
        logError(`Alternative URL also failed: ${altResponse.status}`);
        return;
      }
    }
  } catch (error) {
    logError(`Failed to connect to Keycloak: ${error.message}`);
    return;
  }

  logStep(3, 'Testing Client Credentials');
  
  try {
    // Test client credentials with a dummy token
    const dummyToken = 'dummy-token-for-testing';
    const authHeader = `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`;
    
    log(`   Introspection URL: ${introspectionUrl}`);
    log(`   Client ID: ${clientId}`);
    log(`   Auth Header: Basic ${Buffer.from(`${clientId}:***`).toString('base64')}`);
    
    const response = await fetch(introspectionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': authHeader,
      },
      body: new URLSearchParams({
        token: dummyToken,
        token_type_hint: 'access_token',
      }),
    });

    if (response.ok) {
      const data = await response.json();
      logSuccess('Client credentials are valid');
      log(`   Response: ${JSON.stringify(data, null, 2)}`);
      
      if (data.active === false) {
        logSuccess('Dummy token correctly identified as inactive (expected)');
      }
    } else {
      const errorText = await response.text();
      logError(`Client credentials test failed: ${response.status}`);
      log(`   Error: ${errorText}`);
      
      if (response.status === 401) {
        logError('   This suggests invalid client credentials');
      } else if (response.status === 400) {
        logWarning('   This might be a configuration issue');
      }
    }
  } catch (error) {
    logError(`Client credentials test failed: ${error.message}`);
  }

  logStep(4, 'Recommendations');
  
  log('\n📋 Common Issues and Solutions:');
  log('1. **Invalid Client Credentials**');
  log('   - Verify OAUTH2_CLIENT_ID matches Keycloak client');
  log('   - Verify OAUTH2_CLIENT_SECRET is correct');
  log('   - Check if client is enabled in Keycloak');
  
  log('\n2. **Token Issues**');
  log('   - Token might be expired');
  log('   - Token might be for different client');
  log('   - Token might be from different realm');
  
  log('\n3. **Network Issues**');
  log('   - Check if Keycloak is accessible');
  log('   - Verify realm name is correct');
  log('   - Check firewall/proxy settings');
  
  log('\n4. **Client Configuration in Keycloak**');
  log('   - Client must have "Service Accounts Enabled"');
  log('   - Client must have appropriate scopes');
  log('   - Client access type should be "confidential"');

  log('\n🔧 Next Steps:');
  log('1. Run this debug tool to verify configuration');
  log('2. Check Keycloak admin console for client settings');
  log('3. Try with a fresh token from MCP Inspector');
  log('4. Enable detailed logging in MCP server');
}

// Run the debug tool
debugTokenIntrospection().catch((error) => {
  logError(`Debug tool failed: ${error.message}`);
  process.exit(1);
});
