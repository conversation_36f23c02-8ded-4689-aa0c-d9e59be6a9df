#!/usr/bin/env node

/**
 * 🧪 Auth Cookies Test Script
 *
 * Purpose:
 * 1. Verify Auth Cookies are correctly transmitted in HTTP requests
 * 2. Test authentication mechanisms of various service tools
 * 3. Check detailed HTTP request and response logs
 * 4. Verify circular reference issues are resolved
 */

import { SERVICE_MODULES } from '../dist/adapters/service-adapter.js';
import { getEnvironmentConfig } from '../dist/utils/env.js';

/**
 * Safe JSON serialization to avoid circular references
 */
function safeStringify(obj, space = 2) {
  const seen = new WeakSet();
  return JSON.stringify(obj, (key, value) => {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) {
        return '[Circular Reference]';
      }
      seen.add(value);
    }
    return value;
  }, space);
}

/**
 * Test a single tool
 */
async function testTool(moduleName, functionName, args = {}, description = '') {
  console.log(`\n🔧 Testing: ${moduleName}_${functionName}`);
  console.log(`📝 Description: ${description}`);
  console.log(`📋 Arguments:`, safeStringify(args));

  try {
    const serviceModule = SERVICE_MODULES[moduleName];
    if (!serviceModule) {
      throw new Error(`Service module '${moduleName}' not found`);
    }

    const serviceFunction = serviceModule[functionName];
    if (!serviceFunction) {
      throw new Error(`Function '${functionName}' not found in module '${moduleName}'`);
    }

    const result = await serviceFunction(args);

    console.log(`✅ Success: ${moduleName}_${functionName}`);
    console.log(`📤 Result type: ${typeof result}`);
    console.log(`📤 Result keys: ${Object.keys(result || {}).join(', ')}`);

    // Only show result summary to avoid overly long output
    if (result && typeof result === 'object') {
      console.log(`📊 Data status: ${result.status || 'N/A'}`);
      console.log(`📊 Data code: ${result.data?.code || 'N/A'}`);
    }

    return { success: true, result };
  } catch (error) {
    console.log(`❌ Failed: ${moduleName}_${functionName}`);
    console.log(`💥 Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Main test function
 */
async function runAuthCookiesTest() {
  console.log('🧪 ==================== AUTH COOKIES TEST ====================');
  console.log('🎯 Purpose: Verify Auth Cookies are correctly transmitted in HTTP requests\n');

  // Check environment configuration
  const env = getEnvironmentConfig();
  console.log('🌍 Environment Configuration:');
  console.log(`   NODE_ENV: ${env.NODE_ENV}`);
  console.log(`   API Host: ${env.VITE_API_HOST}`);
  console.log(`   Kong Host: ${env.VITE_API_HOST_KONG}`);
  console.log(`   Auth Cookies: ${env.AUTH_COOKIES ? '✅ Configured' : '❌ Missing'}`);
  console.log(`   Debug Mode: ${env.DEBUG_SERVICE_ADAPTER ? '✅ Enabled' : '❌ Disabled'}`);

  if (!env.AUTH_COOKIES) {
    console.log('\n❌ AUTH_COOKIES not configured. Please set AUTH_COOKIES in .env file.');
    return;
  }
  
  const testResults = [];

  // Test 1: Get current user information
  console.log('\n📋 Test 1: Get Current User');
  const test1 = await testTool('global', 'getCurrentUser', {}, 'Get current user information');
  testResults.push({ name: 'getCurrentUser', ...test1 });

  // Test 2: Search permission store
  console.log('\n📋 Test 2: Search Permission Store');
  const test2 = await testTool('oms', 'searchPermissionStore', {}, 'Search permission stores for current user');
  testResults.push({ name: 'searchPermissionStore', ...test2 });

  // Test 3: Query order lists (with parameters)
  console.log('\n📋 Test 3: Query Order Lists');
  const test3 = await testTool('oms', 'queryOrderLists', {
    page: 1,
    pageSize: 10
  }, 'Query order lists from OMS system');
  testResults.push({ name: 'queryOrderLists', ...test3 });
  
  // 汇总测试结果
  console.log('\n🎯 ==================== TEST SUMMARY ====================');
  const successCount = testResults.filter(r => r.success).length;
  const totalCount = testResults.length;
  
  console.log(`📊 Total Tests: ${totalCount}`);
  console.log(`✅ Successful: ${successCount}`);
  console.log(`❌ Failed: ${totalCount - successCount}`);
  
  testResults.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`   ${status} ${result.name}: ${result.success ? 'PASS' : result.error}`);
  });
  
  // 结论
  console.log('\n🔍 ==================== CONCLUSIONS ====================');
  if (successCount > 0) {
    console.log('✅ Auth Cookies are being transmitted correctly in HTTP requests');
    console.log('✅ HTTP client is working properly');
    console.log('✅ Service adapter is functioning correctly');
  }
  
  if (totalCount - successCount > 0) {
    console.log('⚠️  Some tests failed - this may be due to:');
    console.log('   - Expired authentication cookies');
    console.log('   - Network connectivity issues');
    console.log('   - Server-side authentication requirements');
    console.log('   - API endpoint changes');
  }
  
  console.log('\n💡 Next Steps:');
  console.log('   1. If auth errors occur, update AUTH_COOKIES in .env file');
  console.log('   2. Check server logs for detailed HTTP request/response information');
  console.log('   3. Verify API endpoints are accessible');
  
  console.log('\n🏁 Test completed successfully!');
}

// 运行测试
runAuthCookiesTest().catch(error => {
  console.error('\n💥 Test suite failed:', error.message);
  console.error('📋 Stack:', error.stack);
  process.exit(1);
});
