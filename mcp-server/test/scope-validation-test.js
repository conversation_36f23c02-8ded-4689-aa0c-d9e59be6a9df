#!/usr/bin/env node

/**
 * Scope Validation Test
 * Tests that the updated scope validation works with Keycloak scopes
 */

// Mock the MCP SDK middleware to test scope validation logic
const mockScopeValidation = (userScopes, requiredScopes, validationMode = 'any') => {
  console.log(`🔍 Testing scope validation:`);
  console.log(`   User scopes: [${userScopes.join(', ')}]`);
  console.log(`   Required scopes: [${requiredScopes.join(', ')}]`);
  console.log(`   Validation mode: ${validationMode}`);

  let result;
  if (validationMode === 'all') {
    result = requiredScopes.every(scope => userScopes.includes(scope));
  } else if (validationMode === 'any') {
    result = requiredScopes.some(scope => userScopes.includes(scope));
  }

  console.log(`   Result: ${result ? '✅ PASS' : '❌ FAIL'}`);
  return result;
};

// Test cases
const testCases = [
  {
    name: 'Keycloak Token Scopes (Current Issue)',
    userScopes: ['profile', 'roles', 'email'],
    requiredScopes: ['read', 'profile', 'email'],
    validationMode: 'any',
    expectedResult: true
  },
  {
    name: 'Standard OAuth2 Scopes',
    userScopes: ['read', 'openid'],
    requiredScopes: ['read', 'profile', 'email'],
    validationMode: 'any',
    expectedResult: true
  },
  {
    name: 'Admin Access with Roles',
    userScopes: ['profile', 'roles', 'email'],
    requiredScopes: ['admin', 'roles'],
    validationMode: 'any',
    expectedResult: true
  },
  {
    name: 'No Valid Scopes',
    userScopes: ['invalid', 'test'],
    requiredScopes: ['read', 'profile', 'email'],
    validationMode: 'any',
    expectedResult: false
  },
  {
    name: 'All Scopes Required (Old Logic)',
    userScopes: ['profile', 'roles', 'email'],
    requiredScopes: ['read', 'openid'],
    validationMode: 'all',
    expectedResult: false
  }
];

console.log('🚀 Starting Scope Validation Tests');
console.log('=' .repeat(50));

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  console.log(`\n🔧 [Test ${index + 1}] ${testCase.name}`);
  
  const result = mockScopeValidation(
    testCase.userScopes,
    testCase.requiredScopes,
    testCase.validationMode
  );
  
  if (result === testCase.expectedResult) {
    console.log(`✅ Test passed`);
    passed++;
  } else {
    console.log(`❌ Test failed - Expected: ${testCase.expectedResult}, Got: ${result}`);
    failed++;
  }
});

console.log('\n📊 Test Results Summary');
console.log('=' .repeat(50));
console.log(`Total Tests: ${testCases.length}`);
console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${failed}`);

const successRate = ((passed / testCases.length) * 100).toFixed(1);
console.log(`\nSuccess Rate: ${successRate}%`);

if (failed === 0) {
  console.log('\n🎉 All scope validation tests passed!');
  console.log('✅ The updated middleware should work with Keycloak scopes');
} else {
  console.log('\n💥 Some tests failed. Check the scope validation logic.');
}

// Additional explanation
console.log('\n📋 Key Changes Made:');
console.log('1. Changed validation mode from "all" to "any"');
console.log('2. Updated required scopes to include OIDC scopes');
console.log('3. Read access now accepts: [read, profile, email]');
console.log('4. Admin access now accepts: [admin, roles]');
console.log('\n🔧 Your Keycloak token with [profile, roles, email] should now work!');

process.exit(failed === 0 ? 0 : 1);
