#!/bin/bash
set -e

echo "🚀 Local Keycloak Setup for MCP Server"

# Configuration
KEYCLOAK_VERSION="16.1.1"
KEYCLOAK_PORT="8080"
KEYCLOAK_ADMIN_USER="admin"
KEYCLOAK_ADMIN_PASS="admin123"
CONTAINER_NAME="mcp-keycloak"

# Check if Docker is available
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is not installed. Please install Docker first."
        exit 1
    fi
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running. Please start Docker."
        exit 1
    fi
    echo "✅ Docker is available"
}

check_port() {
    if lsof -Pi :$KEYCLOAK_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        if docker ps --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
            echo "✅ Keycloak container is already running"
            return 0
        else
            echo "❌ Port $KEYCLOAK_PORT is in use. Please stop the process first."
            exit 1
        fi
    fi
    echo "✅ Port $KEYCLOAK_PORT is available"
}

stop_existing() {
    if docker ps -a --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        docker stop $CONTAINER_NAME >/dev/null 2>&1 || true
        docker rm $CONTAINER_NAME >/dev/null 2>&1 || true
        echo "✅ Removed existing container"
    fi
}

start_keycloak() {
    echo "🚀 Starting Keycloak container..."

    IMAGES=("jboss/keycloak:$KEYCLOAK_VERSION" "quay.io/keycloak/keycloak:legacy")

    for IMAGE in "${IMAGES[@]}"; do
        if docker run -d --name $CONTAINER_NAME -p $KEYCLOAK_PORT:8080 \
            -e KEYCLOAK_USER=$KEYCLOAK_ADMIN_USER \
            -e KEYCLOAK_PASSWORD=$KEYCLOAK_ADMIN_PASS \
            -e DB_VENDOR=H2 $IMAGE >/dev/null 2>&1; then
            echo "✅ Started Keycloak with image: $IMAGE"
            return 0
        fi
    done

    echo "❌ Failed to start Keycloak"
    return 1
}

wait_for_keycloak() {
    echo "⏳ Waiting for Keycloak to start..."

    for i in {1..60}; do
        if curl -s -f "http://localhost:$KEYCLOAK_PORT/auth/realms/master" >/dev/null 2>&1; then
            echo "✅ Keycloak is ready!"
            return 0
        fi
        [ $((i % 10)) -eq 0 ] && echo "Still waiting... ($i/60)"
        sleep 5
    done
    echo "❌ Keycloak failed to start"
    docker logs $CONTAINER_NAME --tail 10
    return 1
}

test_keycloak() {
    echo "🧪 Testing Keycloak endpoints..."

    local realm_url="http://localhost:$KEYCLOAK_PORT/auth/realms/master"

    if curl -s -f "$realm_url" >/dev/null && \
       curl -s -f "$realm_url/.well-known/openid-configuration" >/dev/null; then
        echo "✅ Keycloak endpoints are working"
    else
        echo "❌ Keycloak endpoints are not accessible"
        return 1
    fi
}

generate_config() {
    echo "📝 Generating configuration..."

    cat > ".env.keycloak.local" << EOF
# Local Keycloak Configuration
OAUTH2_ENABLED=true
KEYCLOAK_BASE_URL=http://localhost:$KEYCLOAK_PORT/auth
KEYCLOAK_REALM=master
OAUTH2_CLIENT_ID=mcp-server
OAUTH2_CLIENT_SECRET=
MCP_SERVER_PORT=6300
TRANSPORT=http
NODE_ENV=development
DEBUG_SERVICE_ADAPTER=true

# Admin Console: http://localhost:$KEYCLOAK_PORT/auth/admin
# Username: $KEYCLOAK_ADMIN_USER
# Password: $KEYCLOAK_ADMIN_PASS

EOF
    echo "✅ Configuration saved to .env.keycloak.local"
}

print_instructions() {
    echo ""
    echo "🎉 Keycloak Setup Complete!"
    echo "Admin Console: http://localhost:$KEYCLOAK_PORT/auth/admin"
    echo "Username: $KEYCLOAK_ADMIN_USER | Password: $KEYCLOAK_ADMIN_PASS"
    echo ""
    echo "Next steps:"
    echo "1. Configure OAuth2 client in Keycloak"
    echo "2. Copy config: cp .env.keycloak.local .env"
    echo "3. Start MCP server: npm run dev"
    echo ""
}

main() {
    case "${1:-start}" in
        "start")
            check_docker && check_port && stop_existing
            if start_keycloak && wait_for_keycloak && test_keycloak; then
                generate_config && print_instructions
            else
                echo "❌ Failed to start Keycloak"
                exit 1
            fi
            ;;
        "stop")
            docker stop $CONTAINER_NAME >/dev/null 2>&1 || true
            docker rm $CONTAINER_NAME >/dev/null 2>&1 || true
            echo "✅ Keycloak stopped"
            ;;
        "status")
            if docker ps --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
                echo "✅ Keycloak is running - http://localhost:$KEYCLOAK_PORT/auth/admin"
            else
                echo "❌ Keycloak is not running"
            fi
            ;;
        "logs")
            docker logs -f $CONTAINER_NAME
            ;;
        *)
            echo "Usage: $0 {start|stop|status|logs}"
            exit 1
            ;;
    esac
}

main "$@"
