#!/usr/bin/env node

/**
 * 🔐 Keycloak Test and Configuration Script
 * 
 * This script:
 * 1. Tests if Keycloak is available (local or remote)
 * 2. Validates OAuth2 configuration
 * 3. Sets up environment for MCP server
 * 4. Tests OAuth2 endpoints
 */

import fs from 'fs';
import path from 'path';

// Use built-in fetch (Node.js 18+) or provide a simple alternative
const fetchFn = globalThis.fetch || (async (url, options = {}) => {
  const https = await import('https');
  const http = await import('http');
  const { URL } = await import('url');

  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const client = parsedUrl.protocol === 'https:' ? https : http;

    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 5000,
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.end();
  });
});

const KEYCLOAK_CONFIGS = [
  {
    name: 'Local Keycloak (Docker)',
    baseUrl: 'http://localhost:8080/auth',
    realm: 'mcp-realm',
    clientId: 'mcp-server-client',
    clientSecret: 'mcp-server-secret-123',
    testUser: { username: 'testuser', password: 'testpass123' }
  },
  {
    name: 'Local Keycloak (Standalone)',
    baseUrl: 'http://localhost:8080/auth',
    realm: 'master',
    clientId: 'mcp-server',
    clientSecret: '',
    testUser: { username: 'admin', password: 'admin123' }
  },
  {
    name: 'Remote Keycloak (ingka-dt.cn)',
    baseUrl: 'https://keycloak.ingka-dt.cn/auth',
    realm: 'master',
    clientId: 'orders-portal-mcp-server',
    clientSecret: process.env.OAUTH2_CLIENT_SECRET || '',
    testUser: null
  }
];

async function testKeycloakAvailability(config) {
  try {
    console.log(`🔍 Testing ${config.name}...`);
    
    const realmUrl = `${config.baseUrl}/realms/${config.realm}`;
    const response = await fetchFn(realmUrl, {
      timeout: 5000,
      headers: { 'Accept': 'application/json' }
    });
    
    if (response.ok) {
      const realmInfo = await response.json();
      console.log(`✅ ${config.name} is available`);
      console.log(`   Realm: ${realmInfo.realm}`);
      console.log(`   Issuer: ${realmInfo.issuer || realmUrl}`);
      return { available: true, config, realmInfo };
    } else {
      console.log(`❌ ${config.name} returned ${response.status}`);
      return { available: false, config, error: `HTTP ${response.status}` };
    }
  } catch (error) {
    console.log(`❌ ${config.name} is not available: ${error.message}`);
    return { available: false, config, error: error.message };
  }
}

async function testOAuth2Endpoints(config) {
  try {
    console.log(`🔐 Testing OAuth2 endpoints for ${config.name}...`);
    
    const realmUrl = `${config.baseUrl}/realms/${config.realm}`;
    const endpoints = {
      'OpenID Configuration': `${realmUrl}/.well-known/openid-configuration`,
      'Authorization': `${realmUrl}/protocol/openid-connect/auth`,
      'Token': `${realmUrl}/protocol/openid-connect/token`,
      'UserInfo': `${realmUrl}/protocol/openid-connect/userinfo`,
      'JWKS': `${realmUrl}/protocol/openid-connect/certs`
    };
    
    const results = {};
    
    for (const [name, url] of Object.entries(endpoints)) {
      try {
        const response = await fetchFn(url, {
          timeout: 3000,
          headers: { 'Accept': 'application/json' }
        });
        results[name] = {
          status: response.status,
          available: response.ok,
          url
        };
        console.log(`   ${name}: ${response.ok ? '✅' : '❌'} (${response.status})`);
      } catch (error) {
        results[name] = {
          status: 'error',
          available: false,
          error: error.message,
          url
        };
        console.log(`   ${name}: ❌ (${error.message})`);
      }
    }
    
    return results;
  } catch (error) {
    console.log(`❌ OAuth2 endpoint testing failed: ${error.message}`);
    return {};
  }
}

function generateEnvConfig(config) {
  return `# 🔐 Generated Keycloak Configuration for MCP Server
# Generated on: ${new Date().toISOString()}
# Configuration: ${config.name}

# ================================
# OAuth2 Configuration
# ================================
OAUTH2_ENABLED=true

# ================================
# Keycloak Server Configuration
# ================================
KEYCLOAK_BASE_URL=${config.baseUrl}
KEYCLOAK_REALM=${config.realm}
OAUTH2_CLIENT_ID=${config.clientId}
OAUTH2_CLIENT_SECRET=${config.clientSecret}

# ================================
# MCP Server Configuration
# ================================
MCP_SERVER_PORT=6300
TRANSPORT=http

# ================================
# Development Settings
# ================================
NODE_ENV=development
DEBUG_SERVICE_ADAPTER=true

# ================================
# Test User Credentials (if available)
# ================================
${config.testUser ? `# Username: ${config.testUser.username}
# Password: ${config.testUser.password}` : '# No test user configured'}

# ================================
# Usage Instructions
# ================================
# 1. Start MCP server: npm run dev
# 2. Test OAuth2: curl http://localhost:6300/.well-known/oauth-protected-resource
# 3. Start inspector: npm run inspect:http
# 4. Admin console: ${config.baseUrl}/admin
`;
}

async function main() {
  console.log('🚀 Keycloak Test and Configuration Script');
  console.log('==========================================\n');
  
  // Test all Keycloak configurations
  const results = [];
  for (const config of KEYCLOAK_CONFIGS) {
    const result = await testKeycloakAvailability(config);
    if (result.available) {
      result.oauth2Tests = await testOAuth2Endpoints(config);
    }
    results.push(result);
    console.log('');
  }
  
  // Find the best available configuration
  const availableConfigs = results.filter(r => r.available);
  
  if (availableConfigs.length === 0) {
    console.log('❌ No Keycloak instances are available!');
    console.log('\n🔧 To set up local Keycloak:');
    console.log('   1. Install Docker');
    console.log('   2. Run: docker run -d --name mcp-keycloak -p 8080:8080 \\');
    console.log('           -e KEYCLOAK_USER=admin -e KEYCLOAK_PASSWORD=admin123 \\');
    console.log('           -e DB_VENDOR=H2 jboss/keycloak:16.1.1');
    console.log('   3. Wait 60 seconds for startup');
    console.log('   4. Run this script again');
    console.log('\n📖 See KEYCLOAK_SETUP.md for detailed instructions');
    process.exit(1);
  }
  
  // Use the first available configuration
  const selectedConfig = availableConfigs[0];
  console.log(`🎯 Selected configuration: ${selectedConfig.config.name}`);
  
  // Generate environment configuration
  const envContent = generateEnvConfig(selectedConfig.config);
  const envPath = path.join(process.cwd(), '.env.keycloak.generated');
  
  fs.writeFileSync(envPath, envContent);
  console.log(`✅ Generated environment configuration: ${envPath}`);
  
  // Optionally copy to .env
  const args = process.argv.slice(2);
  if (args.includes('--apply') || args.includes('-a')) {
    const mainEnvPath = path.join(process.cwd(), '.env');
    fs.writeFileSync(mainEnvPath, envContent);
    console.log(`✅ Applied configuration to: ${mainEnvPath}`);
  } else {
    console.log('\n💡 To apply this configuration:');
    console.log(`   cp ${envPath} .env`);
    console.log('   OR run this script with --apply flag');
  }
  
  // Test MCP server OAuth2 endpoints (if running)
  console.log('\n🧪 Testing MCP Server OAuth2 endpoints...');
  try {
    const mcpResponse = await fetchFn('http://localhost:6300/.well-known/oauth-protected-resource', {
      timeout: 3000
    });
    
    if (mcpResponse.ok) {
      const metadata = await mcpResponse.json();
      console.log('✅ MCP Server OAuth2 metadata is available:');
      console.log(`   Resource: ${metadata.resource}`);
      console.log(`   Authorization servers: ${metadata.authorization_servers?.join(', ')}`);
      console.log(`   Scopes: ${metadata.scopes_supported?.join(', ')}`);
    } else {
      console.log(`❌ MCP Server OAuth2 metadata returned ${mcpResponse.status}`);
    }
  } catch (error) {
    console.log('❌ MCP Server is not running or OAuth2 is not configured');
    console.log('   Start with: npm run dev');
  }
  
  console.log('\n🎉 Configuration complete!');
  console.log('\n📋 Next steps:');
  console.log('   1. Copy generated config: cp .env.keycloak.generated .env');
  console.log('   2. Start MCP server: npm run dev');
  console.log('   3. Test OAuth2: curl http://localhost:6300/.well-known/oauth-protected-resource');
  console.log('   4. Start inspector: npm run inspect:http');
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('🔐 Keycloak Test and Configuration Script');
  console.log('');
  console.log('Usage: node scripts/test-keycloak.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --apply, -a    Apply configuration to .env file');
  console.log('  --help, -h     Show this help message');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/test-keycloak.js           # Test and generate config');
  console.log('  node scripts/test-keycloak.js --apply   # Test and apply config');
  process.exit(0);
}

main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
