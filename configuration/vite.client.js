import vue from '@vitejs/plugin-vue'

/**
 * Build configuration for client code, executed in the browser
 */
export default {
  plugins: [vue()],

  build: {
    lib: {
      entry: 'src/index.js'
    },
    rollupOptions: {
      external: [
        'vue', 
        'vuex', 
        'vue-router', 
        // 'ant-design-vue', 
        '@ant-design/icons-vue'
      ]
    },
    external: [
      'vue', 
      'vuex', 
      'vue-router', 
      // 'ant-design-vue', 
      '@ant-design/icons-vue'
    ],
    minify: 'eslint'
  }
}
