# HTTP 配置
server {
    listen 80;
    server_name admin-local.ingka-dt.cn;
    
    # 将所有 HTTP 请求重定向到 HTTPS
    return 301 https://$host$request_uri;
}

# HTTPS 配置
server {
    listen 443 ssl;
    server_name admin-local.ingka-dt.cn;

    ssl_certificate /etc/nginx/ssl/admin-local.ingka-dt.cn.crt;
    ssl_certificate_key /etc/nginx/ssl/admin-local.ingka-dt.cn.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

    location = /404.html {
        root /usr/share/nginx/html;
        internal;
    }

    location = /50x.html {
        root /usr/share/nginx/html;
        internal;
    }

    location / {
        proxy_pass http://host.docker.internal:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_http_version 1.1;          # 使用 HTTP/1.1
        proxy_set_header Upgrade $http_upgrade; # 升级到 WebSocket
        proxy_set_header Connection "upgrade";  # 保持连接
        proxy_cache_bypass $http_upgrade;
    }
}