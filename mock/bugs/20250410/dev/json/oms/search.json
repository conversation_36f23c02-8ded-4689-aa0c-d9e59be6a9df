{"code": "0", "data": {"totalCount": 1, "result": [{"orderNO": "2504091338000177357", "ikeaOrderNO": "278277354", "itemCount": 1.0, "serviceCount": 1, "amount": 5155.0, "applyTime": 1744177089057, "paymentTime": 1744177218000, "deliveryOption": "HOME_DELIVERY", "customerName": "贾烨", "customerPhone": "13972895866", "orderStatus": "DELIVERED", "sysStatus": "CREATED", "orderSource": "ONLINE", "hasPayment": true, "hasRefund": false, "paidByWechatLiveStream": false, "itemLines": [{"itemId": "121002203", "itemName": "我设计的帕克思PAX", "itemType": "VTP", "itemQuantity": 1.0}], "paymentPlatform": "Ping++", "paymentDescMap": {"753247931552944129": "银联支付"}, "orderChannel": "ONLINE", "isIbOrder": false, "storeCode": "549", "clientSystem": "SH_APP", "subClientSystem": "", "customerId": "E9488D51-2AFD-11eb-BFCA-005056931D59", "isDeleted": "N", "vid": "278277354"}]}}