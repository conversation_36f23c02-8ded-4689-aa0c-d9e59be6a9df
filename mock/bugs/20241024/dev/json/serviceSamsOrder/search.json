{"code": "0", "data": {"total": 105043, "data": [{"id": 104788, "orderNo": "2410200914000048528", "originIkeaOrderNo": "*********", "sacNo": "26151907", "sacCreateDate": 1729690768000, "refundStatus": "INIT", "refundAmount": 444.1, "amount": 459.0, "thirdPartyRefund": "N", "sacTitle": "241023退货CCD SN包裹 改", "sacComment": "工单内容：10/25上门收货，提前联系\r\n售后原因:   补充描述：尺寸不合适\r\n产品编号&数量:\r\n货号: 20212204, 数量: 1\r\n货号: 60217233, 数量: 2\r\n货号: 70556369, 数量: 1\r\nP++退款444.1元（已扣运费14.9\r\nRGTT&购买日期：  ，商场：549  ，宜家送货：  ，ISELL单号：*********  ，承运商:  ，产品包装:  完好，包裹包装:  完好，是否拍照:  ，上门时间：  \r\n-3127"}, {"id": 104811, "orderNo": "2410191945000080540", "originIkeaOrderNo": "*********", "sacNo": "26151921", "sacCreateDate": 1729690684000, "refundStatus": "COMPLETED", "refundAmount": 150.0, "amount": 567.0, "refundEndTime": 1729729585208, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240826171700001, "merchantRefundReason": "EXT", "sacTitle": "241023  3rd", "sacComment": "3Rd     *********"}, {"id": 104761, "orderNo": "2409230051000027807", "originIkeaOrderNo": "*********", "sacNo": "26151916", "sacCreateDate": 1729690563000, "refundStatus": "INIT", "refundAmount": 90.08, "amount": 109.88, "thirdPartyRefund": "N", "sacTitle": "241023退货CCD SN包裹 改", "sacComment": "工单内容：10/25上门收货，提前联系\r\n售后原因:   补充描述：枕头太硬\r\n产品编号&数量:\r\n货号: 90560365, 数量: 2\r\nP++退款90.08元（已扣运费9.9\r\nRGTT&购买日期：  ，商场：549  ，宜家送货：  ，ISELL单号：*********  ，承运商:  ，产品包装: 完好 ，包裹包装: 完好 ，是否拍照:  ，上门时间：  \r\n-3127"}, {"id": 104952, "orderNo": "2410191855000071431", "originIkeaOrderNo": "*********", "sacNo": "26151877", "sacCreateDate": 1729689722000, "refundStatus": "INIT", "refundAmount": 284.1, "amount": 299.0, "thirdPartyRefund": "N", "sacTitle": "241023退货CCD SN包裹 改", "sacComment": "10/25上门收货，提前联系\r\n售后原因:   补充描述：尺寸太大了\r\n产品编号&数量:\r\n货号: 00575069, 数量: 1\r\nP++退款284.10元（已扣运费14.9）\r\nRGTT&购买日期：  ，商场：549  ，宜家送货：  ，ISELL单号：*********  ，承运商:  ，产品包装:  完好，包裹包装: 完好，是否拍照:  ，上门时间：  \r\n-3127"}, {"id": 104832, "orderNo": "2410162102000116396", "originIkeaOrderNo": "*********", "sacNo": "26151830", "sacCreateDate": 1729689096000, "refundStatus": "INIT", "refundAmount": 20.08, "amount": 100.9, "thirdPartyRefund": "N", "sacTitle": "241023退货SN包裹改", "sacComment": "chat :*********  退20576925*2  孩子不喜欢 扣除运费9.9元，P+退20.08元\r\nRGTT&购买日期：  ，商场：549  ，宜家送货：送货自装  ，ISELL单号:  ，承运商:  ，产品包装：完好  ，包裹包装：完好  ，是否拍照:  ，上门时间：  \r\n3509"}, {"id": 104866, "orderNo": "2410162014000043263", "originIkeaOrderNo": "*********", "sacNo": "26151822", "sacCreateDate": 1729688961000, "refundStatus": "COMPLETED", "refundAmount": 139.98, "amount": 331.86, "refundEndTime": 1729729610500, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240826487400001, "merchantRefundReason": "EXT", "sacTitle": "241023 外部退货", "sacComment": "050103     顾客原因- 款式不合适退货\r\n外部退货"}, {"id": 104669, "orderNo": "2403150648000022122", "originIkeaOrderNo": "*********", "sacNo": "26151796", "sacCreateDate": 1729688620000, "refundStatus": "COMPLETED", "refundAmount": 249.0, "amount": 249.0, "refundEndTime": 1729729643477, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240827219300001, "merchantRefundReason": "EXT", "sacTitle": "241023退货3RD", "sacComment": "050107 顾客原因- 买错了，非目标商品​，顾客在401，549购买，所以549退款，401收货"}, {"id": 104863, "orderNo": "2404210036000054744", "originIkeaOrderNo": "*********", "sacNo": "26151718", "sacCreateDate": 1729688548000, "refundStatus": "COMPLETED", "refundAmount": 59.98, "amount": 399.96, "refundEndTime": 1729729763987, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240829225600001, "merchantRefundReason": "EXT", "sacTitle": "241023 外部退货", "sacComment": "050103     顾客原因- 款式不合适退货\r\n外部退货"}, {"id": 104854, "orderNo": "2410020352000014744", "originIkeaOrderNo": "*********", "sacNo": "26151718", "sacCreateDate": 1729688548000, "refundStatus": "COMPLETED", "refundAmount": 358.74, "amount": 2944.87, "refundEndTime": 1729729772536, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240829305600001, "merchantRefundReason": "EXT", "sacTitle": "241023 外部退货", "sacComment": "050103     顾客原因- 款式不合适退货\r\n外部退货"}, {"id": 104672, "orderNo": "2410162348000127487", "originIkeaOrderNo": "*********", "sacNo": "26151784", "sacCreateDate": 1729688443000, "refundStatus": "COMPLETED", "refundAmount": 279.96, "amount": 279.96, "refundEndTime": 1729729888267, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240831255600001, "merchantRefundReason": "EXT", "sacTitle": "20241023 网上购买现场退 3RD ", "sacComment": "网上购买。 订单号 *********  \r\n线下有活动。 退了重买 未开发票， P++ 退款 "}, {"id": 104981, "orderNo": "2410152351000035465", "originIkeaOrderNo": "*********", "sacNo": "26151773", "sacCreateDate": 1729688251000, "refundStatus": "COMPLETED", "refundAmount": 8.5, "amount": 8.5, "refundStartTime": 1729728063319, "refundEndTime": 1729732048037, "modifier": "SAC AUTO", "assignee": "SAC AUTO", "thirdPartyRefund": "Y", "serviceOrderId": 2410240907047700001, "merchantRefundReason": "EXT", "sacTitle": "241023退货CCD SN包裹 无货返货  ： ", "sacComment": "工单内容：无货返货\r\nchat： *********  70321615*1 夹子夹不住，要退  549退款8.5元\r\nRGTT&购买日期：  ，商场：549  ，宜家送货：送货自装  ，ISELL单号：*********  ，承运商:  ，产品包装：完好  ，包裹包装：完好  ，是否拍照:  ，上门时间：  \r\n8404"}, {"id": 104994, "orderNo": "2408071421000049319", "originIkeaOrderNo": "*********", "sacNo": "26151772", "sacCreateDate": 1729688246000, "refundStatus": "COMPLETED", "refundAmount": 174.0, "amount": 174.0, "refundEndTime": 1729729924467, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240832021700001, "merchantRefundReason": "EXT", "sacTitle": "241023退货3RD", "sacComment": "050107 顾客原因- 买错了，非目标商品​，顾客在401，549购买，所以549退款，401收货"}, {"id": 105021, "orderNo": "2405271834000051591", "originIkeaOrderNo": "*********", "sacNo": "26151767", "sacCreateDate": 1729688147000, "refundStatus": "COMPLETED", "refundAmount": 149.0, "amount": 456.8, "refundStartTime": 1729728063387, "refundEndTime": 1729732049672, "modifier": "SAC AUTO", "assignee": "SAC AUTO", "thirdPartyRefund": "Y", "serviceOrderId": 2410240907207700001, "merchantRefundReason": "EXT", "sacTitle": "2401023退货 ccd SN包裹 无货返货", "sacComment": "工单内容：无货返货\r\nchat：*********   50232955*1  把手松动无法正常使用，换货无库存了，查看UTG，建议退款，549退款149元\r\nRGTT&购买日期：  ，商场：549  ，宜家送货：送货自装  ，ISELL单号：*********  ，承运商:  ，产品包装：完好  ，包裹包装：完好  ，是否拍照:  ，上门时间：  \r\n8404"}, {"id": 104959, "orderNo": "2409221208000045455", "originIkeaOrderNo": "*********", "sacNo": "26151757", "sacCreateDate": 1729687936000, "refundStatus": "COMPLETED", "refundAmount": 811.0, "amount": 900.0, "refundEndTime": 1729729965559, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240832381700001, "merchantRefundReason": "EXT", "sacTitle": "********** 3rd", "sacComment": "********* 549 P+811\r\n"}, {"id": 104849, "orderNo": "2410181352000105785", "originIkeaOrderNo": "*********", "sacNo": "26151713", "sacCreateDate": 1729687632000, "refundStatus": "COMPLETED", "refundAmount": 576.95, "amount": 812.82, "refundEndTime": 1729730117213, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240834547400001, "merchantRefundReason": "EXT", "sacTitle": "P++ 在线退款，3RD 885收货", "sacComment": "P++在线退款\r\n885收货"}, {"id": 104680, "orderNo": "2408021039000137727", "originIkeaOrderNo": "*********", "sacNo": "26151661", "sacCreateDate": 1729686771000, "refundStatus": "COMPLETED", "refundAmount": 3619.0, "amount": 9367.99, "refundEndTime": 1729730196282, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240836347400001, "merchantRefundReason": "EXT", "sacTitle": "20241023 退货 3RD", "sacComment": "网上订单：549-*********                                         \r\n05-02-01 顾客买多，顾客带商品到商场退货 顾客自述未开发票 \r\n418商场退换处已收到货  YSZ\r\n"}, {"id": 105015, "orderNo": "2410201546000066276", "originIkeaOrderNo": "*********", "sacNo": "26151642", "sacCreateDate": 1729686415000, "refundStatus": "COMPLETED", "refundAmount": 399.0, "amount": 617.98, "refundEndTime": 1729730275018, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240837527700001, "merchantRefundReason": "EXT", "sacTitle": "241023  退货 3RD", "sacComment": "05 不稳"}, {"id": 104923, "orderNo": "2408192216000035355", "originIkeaOrderNo": "*********", "sacNo": "26151602", "sacCreateDate": 1729685928000, "refundStatus": "COMPLETED", "refundAmount": 499.0, "amount": 499.0, "refundEndTime": 1729730301054, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240838197700001, "merchantRefundReason": "EXT", "sacTitle": "20241023退货3rd", "sacComment": "*********，网上退货\r\n顾客在线下买的便宜，线上买的退掉\r\np++退款499元\r\n2006"}, {"id": 104910, "sacNo": "26151618", "sacCreateDate": 1729685837000, "refundStatus": "CANCELED", "refundAmount": 56.0, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "sacTitle": "241023退组装费 ", "sacComment": "CHAT  ：  *********    20351879*1 ，60351877* 2  师傅没有组装，自己退货，  P++   退部分组装费 56元 \r\n3119 "}, {"id": 105000, "orderNo": "2406182258000038518", "originIkeaOrderNo": "*********", "sacNo": "26151534", "sacCreateDate": 1729685015000, "refundStatus": "COMPLETED", "refundAmount": 35.06, "amount": 2559.99, "refundEndTime": 1729730498265, "modifier": "<PERSON>", "assignee": "<PERSON>", "thirdPartyRefund": "Y", "serviceOrderId": 2410240841345600001, "merchantRefundReason": "EXT", "sacTitle": "241023 外退3rd", "sacComment": "*********\r\n买多了\r\np++ 35.06\r\n2024/10/23 20:05 - 向珈瑶"}], "currentTimeStamp": 1729749454590}}