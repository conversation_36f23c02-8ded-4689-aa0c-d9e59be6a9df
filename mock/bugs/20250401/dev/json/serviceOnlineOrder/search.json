{"code": "0", "data": {"total": 1, "data": [{"id": 2503080001447200002, "ikeaOrderNo": "276981419", "orderNo": "2503072357000031421", "serviceType": "RPA_CANCEL", "amount": 1227.0, "actualRefundAmount": 999.0, "customerId": "CIAMfcd5-e49d-4f4d-a51b-2b4114f327e5", "status": "REFUND_FAILED", "sacId": "autorefund", "merchantRefundReason": "INT", "gmtCreate": 1741363304491, "gmtModified": 1741363434125, "refundStartTime": 1741363427421, "refundEndTime": 1741363434125, "orderSource": "ONLINE", "refundMethod": "ORIGIN", "refundList": [{"id": 2503080003478200001, "serviceOrderRef": 2503080001447200002, "orderNo": "2503072357000031421", "refundId": "741446530312392704", "refundType": "AUTO_REFUND_INT", "refundAmount": 228.0, "status": "REFUND_FAILED", "errorCode": "refund_failed", "errorMsg": "退款失败，来自 upacp 渠道的失败信息：交易金额超限[2020003]", "gmtCreate": 1741363427749, "gmtModified": 1741363434118, "paymentGatewayReferenceId": "741445421012729857"}, {"id": 2503080003471600002, "serviceOrderRef": 2503080001447200002, "orderNo": "2503072357000031421", "refundId": "741446531781042176", "refundType": "AUTO_REFUND_INT", "refundAmount": 999.0, "status": "REFUND_COMPLETED", "completeTime": 1741363430408, "gmtCreate": 1741363427749, "gmtModified": 1741363430409, "paymentGatewayReferenceId": "741444853991690241"}], "orderAmount": 1227.0, "isIbOrder": false, "orderChannel": "ONLINE", "actualSubsidyRefundAmount": 149.85}], "currentTimeStamp": 1743057323610}}