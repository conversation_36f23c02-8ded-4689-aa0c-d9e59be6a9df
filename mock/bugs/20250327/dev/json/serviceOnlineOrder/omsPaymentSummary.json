{"code": "0", "data": {"id": 2503051058047200816, "orderNO": "2503072357000031421", "ikeaOrderNO": "276981419", "deliveryAmount": 89.0, "totalAmount": 1227.0, "totalRefundedAmount": 999.0, "rewardList": [], "invoice": {"ikeaOrderNo": "276981419", "invoiceStatus": "0", "invoiceType": "", "invoiceURL": ""}, "omsServiceBriefOrderVOList": [{"refundStartTime": 1741363427421, "refundEndTime": 1741363434125, "actualRefundAmount": 999.0, "status": "REFUND_FAILED", "merchantRefundReason": "INT", "actualSubsidyRefundAmount": 149.85}], "paymentDetailVOList": [{"paymentGatewayReferenceId": "741445421012729857", "paymentAmount": 228.0, "refundedAmount": 0, "paymentBrand": "upacp", "paymentPlatform": "银联支付", "currentRefundAmount": 228.0, "currentRefundStatus": "REFUND_FAILED"}, {"paymentGatewayReferenceId": "741444853991690241", "paymentAmount": 999.0, "refundedAmount": 999.0, "paymentBrand": "upacp", "paymentPlatform": "银联支付", "currentRefundAmount": 999.0, "currentRefundStatus": "REFUND_COMPLETED", "subsidyAmount": 149.85, "subContext": "SUB_CONTEXT_1"}], "userRefundQuota": 5000, "refundApplyShowVo": {"sacId": "autorefund", "refundAmount": 1227.0, "merchantRefundReason": "INT", "refundMethod": "ORIGIN"}, "storeCode": "549", "refundApplyAuditTag": true, "isSubsidy": true, "useCountrySubsidy": true}}