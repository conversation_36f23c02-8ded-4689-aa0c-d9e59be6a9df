{"code": "0", "data": {"total": 8, "data": [{"id": 2307181604500900001, "orderNo": "2306071349000010470", "serviceType": "CM_AUTO_CANCEL", "amount": 69.98, "actualRefundAmount": 0.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "status": "REFUND_PROCESSING", "sacId": "557571326", "merchantRefundReason": "INT", "gmtCreate": 1689667490221, "gmtModified": 1689667490221, "refundStartTime": 1689667490205, "creator": "CM_AUTO", "modifier": "CM_AUTO", "orderSource": "CSC", "refundMethod": "ONE_SOLUTION_AUTO_REFUND", "refundList": [{"id": 2307181604500900002, "serviceOrderRef": 2307181604500900001, "orderNo": "2306071349000010470", "refundId": "3917508202307184000", "refundType": "CM_AUTO_REFUND_INT", "refundAmount": 69.98, "status": "REFUND_PROCESSING", "errorCode": "", "errorMsg": "", "gmtCreate": 1689667490546, "gmtModified": 1689667490546, "paymentGatewayReferenceId": "6071349557093819"}]}, {"id": 2307181428170900001, "orderNo": "2306290326000010470", "serviceType": "CM_AUTO_CANCEL", "amount": 158.9, "actualRefundAmount": 158.9, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "status": "REFUND_COMPLETED", "sacId": "557572011", "merchantRefundReason": "INT", "gmtCreate": 1689661697369, "gmtModified": 1689662586494, "refundStartTime": 1689661697355, "refundEndTime": 1689662586489, "creator": "CM_AUTO", "modifier": "CM_AUTO", "orderSource": "ONLINE", "refundMethod": "ONE_SOLUTION_AUTO_REFUND", "refundList": [{"id": 2307181428170900002, "serviceOrderRef": 2307181428170900001, "orderNo": "2306290326000010470", "refundId": "3917508202307182000", "refundType": "CM_AUTO_REFUND_INT", "refundAmount": 158.9, "status": "REFUND_COMPLETED", "errorCode": "", "errorMsg": "", "completeTime": 1689662586461, "gmtCreate": 1689661697706, "gmtModified": 1689662586633, "paymentGatewayReferenceId": "6290326179124341"}]}, {"id": 2307181331076800001, "orderNo": "2307041529000010470", "serviceType": "CM_AUTO_CANCEL", "amount": 2219.95, "actualRefundAmount": 0.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "status": "REFUND_FAILED", "sacId": "557572167", "merchantRefundReason": "INT", "gmtCreate": 1689658267599, "gmtModified": 1689658673991, "refundStartTime": 1689658267591, "refundEndTime": 1689658673816, "creator": "CM_AUTO", "modifier": "CM_AUTO", "orderSource": "ONLINE", "refundMethod": "ONE_SOLUTION_AUTO_REFUND", "refundList": [{"id": 2307181331076800002, "serviceOrderRef": 2307181331076800001, "orderNo": "2307041529000010470", "refundId": "3917508202307181000", "refundType": "CM_AUTO_REFUND_INT", "refundAmount": 2219.95, "status": "REFUND_FAILED", "errorCode": "error_110110", "errorMsg": "error error error", "completeTime": 1689658673779, "gmtCreate": 1689658267918, "gmtModified": 1689658673794, "paymentGatewayReferenceId": "7041529296733987"}]}, {"id": 2307171754269800001, "orderNo": "2307071526000012402", "serviceType": "CM_AUTO_CANCEL", "amount": 7237.95, "actualRefundAmount": 0.0, "customerId": "3DA9AC6A-94A0-46A8-81B3-D16408B0D196", "status": "REFUND_PROCESSING", "sacId": "557572193", "merchantRefundReason": "INT", "gmtCreate": 1689587666948, "gmtModified": 1689587666948, "refundStartTime": 1689587666947, "creator": "CM_AUTO", "modifier": "CM_AUTO", "orderSource": "ONLINE", "refundMethod": "ONE_SOLUTION_AUTO_REFUND", "refundList": [{"id": 2307171754269800002, "serviceOrderRef": 2307171754269800001, "orderNo": "2307071526000012402", "refundId": "3917508202307172000", "refundType": "CM_AUTO_REFUND_INT", "refundAmount": 7237.95, "status": "REFUND_PROCESSING", "errorCode": "", "errorMsg": "", "gmtCreate": 1689587668056, "gmtModified": 1689587668056, "paymentGatewayReferenceId": "7071526014473124"}]}, {"id": 2307171736530900001, "orderNo": "2307071526000012402", "serviceType": "CM_AUTO_CANCEL", "amount": 7237.95, "actualRefundAmount": 0.0, "customerId": "3DA9AC6A-94A0-46A8-81B3-D16408B0D196", "status": "REFUND_PROCESSING", "sacId": "557572193", "merchantRefundReason": "INT", "gmtCreate": 1689586613383, "gmtModified": 1689586613383, "refundStartTime": 1689586613382, "creator": "CM_AUTO", "modifier": "CM_AUTO", "orderSource": "ONLINE", "refundMethod": "ONE_SOLUTION_AUTO_REFUND", "refundList": [{"id": 2307171736530900002, "serviceOrderRef": 2307171736530900001, "orderNo": "2307071526000012402", "refundId": "3917508202307172000", "refundType": "CM_AUTO_REFUND_INT", "refundAmount": 7237.95, "status": "REFUND_PROCESSING", "errorCode": "", "errorMsg": "", "gmtCreate": 1689586615236, "gmtModified": 1689586615236, "paymentGatewayReferenceId": "7071526014473124"}]}, {"id": 2307171634586800001, "orderNo": "2307071530000012402", "serviceType": "CM_AUTO_CANCEL", "amount": 7237.95, "actualRefundAmount": 7237.95, "customerId": "3DA9AC6A-94A0-46A8-81B3-D16408B0D196", "status": "REFUND_COMPLETED", "sacId": "557572196", "merchantRefundReason": "INT", "gmtCreate": 1689582898643, "gmtModified": 1689583329743, "refundStartTime": 1689582898478, "refundEndTime": 1689583329737, "creator": "CM_AUTO", "modifier": "CM_AUTO", "orderSource": "ONLINE", "refundMethod": "ONE_SOLUTION_AUTO_REFUND", "refundList": [{"id": 2307171634586800002, "serviceOrderRef": 2307171634586800001, "orderNo": "2307071530000012402", "refundId": "3917508202307171000", "refundType": "CM_AUTO_REFUND_INT", "refundAmount": 7237.95, "status": "REFUND_COMPLETED", "errorCode": "", "errorMsg": "", "completeTime": 1689583329706, "gmtCreate": 1689582900327, "gmtModified": 1689583330009, "paymentGatewayReferenceId": "7071530211793063"}]}, {"id": 2307171607469800001, "orderNo": "2307071530000012402", "serviceType": "CM_AUTO_CANCEL", "amount": 7237.95, "actualRefundAmount": 0.0, "customerId": "3DA9AC6A-94A0-46A8-81B3-D16408B0D196", "status": "REFUND_FAILED", "sacId": "557572196", "merchantRefundReason": "INT", "gmtCreate": 1689581267006, "gmtModified": 1689581268098, "refundStartTime": 1689581267006, "refundEndTime": 1689581268096, "creator": "CM_AUTO", "modifier": "CM_AUTO", "orderSource": "ONLINE", "refundMethod": "ONE_SOLUTION_AUTO_REFUND", "refundList": [{"id": 2307171607479800001, "serviceOrderRef": 2307171607469800001, "orderNo": "2307071530000012402", "refundType": "CM_AUTO_REFUND_INT", "refundAmount": 7237.95, "status": "REFUND_FAILED", "errorCode": "error_refund_system_error", "errorMsg": "error_refund_system_error", "gmtCreate": 1689581268093, "gmtModified": 1689581268093, "paymentGatewayReferenceId": "7071530211793063"}]}, {"id": 2307171607299800001, "orderNo": "2307071530000012402", "serviceType": "CM_AUTO_CANCEL", "amount": 7237.95, "actualRefundAmount": 0.0, "customerId": "3DA9AC6A-94A0-46A8-81B3-D16408B0D196", "status": "REFUND_FAILED", "sacId": "557572196", "merchantRefundReason": "INT", "gmtCreate": 1689581249666, "gmtModified": 1689581251022, "refundStartTime": 1689581249526, "refundEndTime": 1689581250968, "creator": "CM_AUTO", "modifier": "CM_AUTO", "orderSource": "ONLINE", "refundMethod": "ONE_SOLUTION_AUTO_REFUND", "refundList": [{"id": 2307171607299800002, "serviceOrderRef": 2307171607299800001, "orderNo": "2307071530000012402", "refundType": "CM_AUTO_REFUND_INT", "refundAmount": 7237.95, "status": "REFUND_FAILED", "errorCode": "error_refund_system_error", "errorMsg": "error_refund_system_error", "gmtCreate": 1689581250906, "gmtModified": 1689581250906, "paymentGatewayReferenceId": "7071530211793063"}]}], "currentTimeStamp": 1722936321612}}