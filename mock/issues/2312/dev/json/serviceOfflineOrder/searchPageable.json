{"code": "0", "data": {"total": 68, "data": [{"id": 2309121531126800001, "serviceType": "MANUAL_REFUND", "orderNo": "2309121530000010470", "amount": 684.86, "actualRefundAmount": 105.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "656435421111", "refundStartTime": "2023-09-12T07:31:12.160+00:00", "applier": "<PERSON><PERSON>", "applierId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approver": "", "approverId": "", "approvalStatus": "WAITING_FOR_APPROVAL", "refundMethods": ["ORIGIN", "REFUND_CARD"], "receiptBarcode": "9900621002100061230917", "orderChannel": "OFFLINE"}, {"id": 2309121520183600001, "serviceType": "MANUAL_REFUND", "orderNo": "2309121519000010470", "amount": 684.86, "actualRefundAmount": 105.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "*************", "refundStartTime": "2023-09-12T07:20:18.940+00:00", "refundStatus": "REFUND_FAILED", "applier": "<PERSON><PERSON>", "applierId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approver": "", "approverId": "", "approvalStatus": "APPROVED", "refundMethods": ["REFUND_CARD", "ORIGIN"], "refundErrorMessages": ["merchant settle account is not found on bucode 621-STORE_NORMAL"], "receiptBarcode": "9900621002100061230916", "orderChannel": "OFFLINE"}, {"id": 2309121141383600001, "serviceType": "MANUAL_REFUND", "orderNo": "2309121140000010470", "amount": 684.86, "actualRefundAmount": 110.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "************", "refundStartTime": "2023-09-12T03:41:38.214+00:00", "refundStatus": "REFUND_FAILED", "applier": "<PERSON><PERSON>", "applierId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approver": "", "approverId": "", "approvalStatus": "APPROVED", "refundMethods": ["REFUND_CARD", "ORIGIN"], "refundErrorMessages": ["merchant settle account is not found on bucode 621-STORE_NORMAL"], "receiptBarcode": "9900621002100061230914", "orderChannel": "OFFLINE"}, {"id": 2309121125023600001, "serviceType": "MANUAL_REFUND", "orderNo": "2309121123000010470", "amount": 684.86, "actualRefundAmount": 110.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "***************", "refundStartTime": "2023-09-12T03:25:02.246+00:00", "refundStatus": "REFUND_PROCESSING", "applier": "<PERSON><PERSON>", "applierId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approver": "", "approverId": "", "approvalStatus": "APPROVED", "refundMethods": ["REFUND_CARD", "ORIGIN"], "receiptBarcode": "9900621002100061230913", "orderChannel": "OFFLINE"}, {"id": 2309121108336800001, "serviceType": "MANUAL_REFUND", "orderNo": "2309111529000010470", "amount": 684.86, "actualRefundAmount": 110.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "*************", "refundStartTime": "2023-09-12T03:08:33.509+00:00", "applier": "<PERSON><PERSON>", "applierId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approver": "", "approverId": "", "approvalStatus": "WAITING_FOR_APPROVAL", "refundMethods": ["ORIGIN", "REFUND_CARD"], "receiptBarcode": "9900621002100061230912", "orderChannel": "OFFLINE"}, {"id": 2309111546596800001, "serviceType": "MANUAL_REFUND", "orderNo": "2309111528000010470", "amount": 684.86, "actualRefundAmount": 2.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "1", "refundStartTime": "2023-09-11T07:46:59.036+00:00", "refundEndTime": "2023-09-11T08:26:57.127+00:00", "refundStatus": "REFUND_COMPLETED", "applier": "Faye <PERSON>", "applierId": "f06cfbfc-6add-4ed5-828a-076e79ac763f", "approvalStatus": "APPROVED", "refundMethods": ["REFUND_CARD", "ORIGIN"], "refundErrorMessages": ["merchant settle account is not found on bucode 621-STORE_NORMAL"], "receiptBarcode": "9900621002100061230911", "orderChannel": "OFFLINE"}, {"id": 2309011241286800001, "serviceType": "MANUAL_REFUND", "orderNo": "2309011034000010470", "amount": 393.89, "actualRefundAmount": 50.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "***************", "refundStartTime": "2023-09-01T04:41:28.310+00:00", "applier": "<PERSON><PERSON>", "applierId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approver": "<PERSON>", "approverId": "3cbe0e59-ebe4-4d35-bb2b-4de025878ecb", "approvalStatus": "REJECTED", "refundMethods": ["REFUND_CARD"], "receiptBarcode": "9900833008100081230804", "orderChannel": "OFFLINE"}, {"id": 2309071056473600001, "serviceType": "MANUAL_REFUND", "orderNo": "2309061841000010470", "amount": 708.89, "actualRefundAmount": 250.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "***********", "refundStartTime": "2023-09-07T02:56:47.755+00:00", "refundStatus": "REFUND_COMPLETED", "applier": "<PERSON><PERSON>", "applierId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approver": "Qingbo Meng", "approverId": "0626d3e6-c545-4b68-8d3e-a223b4c9763d", "approvalStatus": "APPROVED", "refundMethods": ["REFUND_CARD"], "receiptBarcode": "9900621001000204230907", "orderChannel": "OFFLINE"}, {"id": 2309061834026800001, "serviceType": "MANUAL_REFUND", "orderNo": "2309061832000010470", "amount": 708.89, "actualRefundAmount": 5.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "5738472344233", "refundStartTime": "2023-09-06T10:34:40.611+00:00", "refundStatus": "REFUND_COMPLETED", "applier": "<PERSON><PERSON>", "applierId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approver": "Qingbo Meng", "approverId": "0626d3e6-c545-4b68-8d3e-a223b4c9763d", "approvalStatus": "APPROVED", "refundMethods": ["REFUND_CARD"], "receiptBarcode": "9900621001000204230906", "orderChannel": "OFFLINE"}, {"id": 2309061815453600001, "serviceType": "MANUAL_REFUND", "orderNo": "2309061815000010470", "amount": 424.65, "actualRefundAmount": 150.0, "customerId": "CIAM775a-fc12-402b-8666-2300f0fa2f57", "sacId": "48395849385", "refundStartTime": "2023-09-06T10:16:53.210+00:00", "applier": "Qingbo Meng", "applierId": "0626d3e6-c545-4b68-8d3e-a223b4c9763d", "approver": "<PERSON><PERSON>", "approverId": "7ab0506c-0677-45d5-8d4e-af0c6c4e1c46", "approvalStatus": "REJECTED", "refundMethods": ["ORIGIN"], "receiptBarcode": "9900673000200131230906", "orderChannel": "OFFLINE"}]}}