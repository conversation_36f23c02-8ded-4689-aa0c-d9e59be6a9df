{"code": "0", "data": {"total": 78, "invoiceDetails": [{"id": "2506271440453700001", "applyId": "327fe1aabc3d4fa5894de58b8a895b45", "orderId": "************", "storeCode": "1228", "invoiceStatus": "FAIL", "invoiceStatusDesc": "开票失败", "invoiceTitleType": "PERSON", "invoiceType": "GENERAL_DIGITAL_INVOICE", "issueType": "BLUE", "amountWithTax": 0.03, "amountWithoutTax": 0.03, "taxAmount": 0.0, "buyerName": "个人", "clientSystem": "JD_MANUAL", "remark": "商品补差发票测试", "isShowBuyerBank": false, "isShowSellerBank": false, "gmtCreate": "2025-06-27T14:40:45.821", "gmtCreateStamp": *************, "gmtModified": "2025-06-27T14:41:57.871", "gmtModifiedStamp": *************, "creator": "Lei JIA<PERSON>", "modifier": "Lei JIA<PERSON>", "hasRedInfo": false, "failureMsg": "订单含税编为空的商品,开票失败", "isellId": ""}, {"id": "2506271347492900001", "applyId": "**********", "orderId": "************", "storeCode": "1228", "invoiceStatus": "IN_PROGRESS", "invoiceStatusDesc": "开票中", "invoiceTitleType": "PERSON", "invoiceType": "GENERAL_DIGITAL_INVOICE", "issueType": "BLUE", "amountWithTax": 39.99, "amountWithoutTax": 35.39, "taxAmount": 4.6, "buyerName": "个人", "phone": "***********", "clientSystem": "JD", "invoiceNo": "25317000001359070420", "invoiceTime": "2025-06-27T16:10:00", "invoiceTimeStamp": *************, "invoiceDeadline": "2025-07-07T14:20:09.622", "invoiceDeadlineStamp": *************, "gmtCreate": "2025-06-27T13:47:50.057", "gmtCreateStamp": *************, "gmtModified": "2025-06-27T16:10:01.181", "gmtModifiedStamp": 1751011801181, "creator": "JD", "modifier": "JD", "hasRedInfo": false, "isellId": "*********"}, {"id": "2506271324034800001", "applyId": "2506261319033700126", "orderId": "318023909498", "storeCode": "1228", "invoiceStatus": "SUCCESS", "invoiceStatusDesc": "开票成功", "invoiceTitleType": "PERSON", "invoiceType": "GENERAL_DIGITAL_INVOICE", "issueType": "RED", "uploadState": "2", "amountWithTax": 109.0, "amountWithoutTax": 96.46, "taxAmount": 12.54, "buyerName": "余红娇", "clientSystem": "JD", "invoicePdf": "https://etaxpro.bizapps.cn/p/uac7YleX2Uk#1mY71", "invoicePdfView": "https://mpp-fe-i.ingka-internal.cn/shangying/v/p/uac7YleX2Uk#1mY71", "invoiceNo": "25317000001349101492", "originInvoiceNo": "25317000001349099559", "invoiceTime": "2025-06-27T13:25:09.711", "invoiceTimeStamp": 1751001909711, "gmtCreate": "2025-06-27T13:24:03.758", "gmtCreateStamp": 1751001843758, "gmtModified": "2025-06-27T13:30:02.75", "gmtModifiedStamp": 1751002202750, "hasRedInfo": false, "isellId": "*********"}, {"id": "2506271145031400001", "applyId": "2506271013173700001", "orderId": "************", "storeCode": "1228", "invoiceStatus": "SUCCESS", "invoiceStatusDesc": "开票成功", "invoiceTitleType": "ENTERPRISE", "invoiceType": "SPECIAL_DIGITAL_INVOICE", "issueType": "RED", "uploadState": "2", "amountWithTax": 39.64, "amountWithoutTax": 35.08, "taxAmount": 4.56, "buyerName": "商应信息科技（上海）有限公司", "buyerAddress": "上海市杨浦区隆昌路619号8号楼中区A05-07室", "buyerTaxNo": "91310110MA1G89E6XH", "bankName": "招商银行股份有限公司上海长阳支行", "bankAccount": "***************", "phone": "***********", "clientSystem": "JD_MANUAL", "invoicePdf": "https://etaxpro.bizapps.cn/p/uac7YleX2Vg#1mY71", "invoicePdfView": "https://mpp-fe-i.ingka-internal.cn/shangying/v/p/uac7YleX2Vg#1mY71", "invoiceNo": "25317000001349101550", "originInvoiceNo": "25317000001349101269", "invoiceTime": "2025-06-27T14:15:07.394", "invoiceTimeStamp": *************, "gmtCreate": "2025-06-27T11:45:03.543", "gmtCreateStamp": *************, "gmtModified": "2025-06-27T14:19:32.404", "gmtModifiedStamp": *************, "hasRedInfo": false, "isellId": "*********"}, {"id": "2506271020003200001", "applyId": "**********", "orderId": "************", "storeCode": "1228", "invoiceStatus": "PENDING", "invoiceStatusDesc": "待开票", "invoiceTitleType": "PERSON", "invoiceType": "GENERAL_DIGITAL_INVOICE", "issueType": "BLUE", "amountWithTax": 1.0, "amountWithoutTax": 0.88, "taxAmount": 0.12, "buyerName": "个人", "phone": "***********", "clientSystem": "JD", "gmtCreate": "2025-06-27T10:20:00.901", "gmtCreateStamp": 1750990800901, "gmtModified": "2025-06-27T10:20:00.901", "gmtModifiedStamp": 1750990800901, "creator": "JD", "modifier": "JD", "hasRedInfo": false, "isellId": ""}, {"id": "2506271013173700001", "applyId": "8e34db655f104e53900c3d1f77f0c5fd", "orderId": "************", "storeCode": "1228", "invoiceStatus": "INVALID", "invoiceStatusDesc": "已红冲", "invoiceTitleType": "ENTERPRISE", "invoiceType": "SPECIAL_DIGITAL_INVOICE", "issueType": "BLUE", "invoiceUse": "1", "billingState": "01", "isSendMail": false, "uploadState": "2", "amountWithTax": 39.64, "amountWithoutTax": 35.08, "taxAmount": 4.56, "buyerName": "商应信息科技（上海）有限公司", "buyerAddress": "上海市杨浦区隆昌路619号8号楼中区A05-07室", "buyerTaxNo": "91310110MA1G89E6XH", "bankName": "招商银行股份有限公司上海长阳支行", "bankAccount": "***************", "phone": "***********", "clientSystem": "JD_MANUAL", "invoicePdf": "https://etaxpro.bizapps.cn/p/uac7YleX2QJ#1mY71", "invoicePdfView": "https://mpp-fe-i.ingka-internal.cn/shangying/v/p/uac7YleX2QJ#1mY71", "invoiceNo": "25317000001349101269", "invoiceTime": "2025-06-27T10:13:27.053", "invoiceTimeStamp": *************, "isShowBuyerBank": false, "isShowSellerBank": false, "gmtCreate": "2025-06-27T10:13:17.63", "gmtCreateStamp": *************, "gmtModified": "2025-06-27T14:19:32.398", "gmtModifiedStamp": *************, "creator": "<PERSON><PERSON><PERSON><PERSON>", "modifier": "<PERSON><PERSON><PERSON><PERSON>", "hasRedInfo": true, "isellId": "*********"}, {"id": "2506270940131400001", "applyId": "a28625d9857e4407806be091a57de168", "orderId": "************", "storeCode": "1228", "invoiceStatus": "PENDING", "invoiceStatusDesc": "待开票", "invoiceTitleType": "PERSON", "invoiceType": "GENERAL_DIGITAL_INVOICE", "issueType": "BLUE", "amountWithTax": 1.0, "amountWithoutTax": 0.88, "taxAmount": 0.12, "buyerName": "个人", "clientSystem": "JD_MANUAL", "remark": "补差商品开票测试", "isShowBuyerBank": false, "isShowSellerBank": false, "gmtCreate": "2025-06-27T09:40:13.555", "gmtCreateStamp": *************, "gmtModified": "2025-06-27T09:40:13.555", "gmtModifiedStamp": *************, "creator": "Lei JIA<PERSON>", "modifier": "Lei JIA<PERSON>", "hasRedInfo": false, "isellId": ""}, {"id": "2506261635032900001", "applyId": "2506261417252900001", "orderId": "************", "storeCode": "1228", "invoiceStatus": "SUCCESS", "invoiceStatusDesc": "开票成功", "invoiceTitleType": "ENTERPRISE", "invoiceType": "SPECIAL_DIGITAL_INVOICE", "issueType": "RED", "uploadState": "2", "amountWithTax": 297.1, "amountWithoutTax": 262.92, "taxAmount": 34.18, "buyerName": "商应信息科技（上海）有限公司", "buyerAddress": "上海市杨浦区隆昌路619号8号楼中区A05-07室", "buyerTaxNo": "91310110MA1G89E6XH", "bankName": "招商银行股份有限公司上海长阳支行", "bankAccount": "***************", "phone": "***********", "clientSystem": "JD_MANUAL", "invoicePdf": "https://etaxpro.bizapps.cn/p/uac7YleX2tA#1mY7e", "invoicePdfView": "https://mpp-fe-i.ingka-internal.cn/shangying/v/p/uac7YleX2tA#1mY7e", "invoiceNo": "25317000001349099834", "originInvoiceNo": "25317000001349099622", "invoiceTime": "2025-06-26T16:40:02.812", "invoiceTimeStamp": *************, "gmtCreate": "2025-06-26T16:35:03.424", "gmtCreateStamp": *************, "gmtModified": "2025-06-26T16:45:00.959", "gmtModifiedStamp": *************, "hasRedInfo": false, "isellId": "*********"}, {"id": "2506261500002900001", "applyId": "**********", "orderId": "************", "storeCode": "1228", "invoiceStatus": "SUCCESS", "invoiceStatusDesc": "开票成功", "invoiceTitleType": "PERSON", "invoiceType": "GENERAL_DIGITAL_INVOICE", "issueType": "BLUE", "invoiceUse": "0", "billingState": "01", "isSendMail": false, "uploadState": "2", "amountWithTax": 1.0, "amountWithoutTax": 0.88, "taxAmount": 0.12, "buyerName": "个人", "phone": "***********", "clientSystem": "JD", "invoicePdf": "https://etaxpro.bizapps.cn/p/uac7YleX2PV#1mY71", "invoicePdfView": "https://mpp-fe-i.ingka-internal.cn/shangying/v/p/uac7YleX2PV#1mY71", "invoiceNo": "25317000001349101219", "invoiceTime": "2025-06-27T09:42:52.322", "invoiceTimeStamp": 1750988572322, "gmtCreate": "2025-06-26T15:00:00.867", "gmtCreateStamp": 1750921200867, "gmtModified": "2025-06-27T09:55:02.139", "gmtModifiedStamp": 1750989302139, "creator": "JD", "modifier": "JD", "hasRedInfo": false, "isellId": ""}, {"id": "2506261450001400001", "applyId": "4037354368", "orderId": "************", "storeCode": "1228", "invoiceStatus": "FAIL", "invoiceStatusDesc": "开票失败", "invoiceTitleType": "PERSON", "invoiceType": "GENERAL_DIGITAL_INVOICE", "issueType": "BLUE", "amountWithTax": 1.0, "amountWithoutTax": 0.88, "taxAmount": 0.12, "buyerName": "个人", "phone": "***********", "email": "", "clientSystem": "JD", "gmtCreate": "2025-06-26T14:50:00.62", "gmtCreateStamp": 1750920600620, "gmtModified": "2025-06-26T14:57:23.101", "gmtModifiedStamp": 1750921043101, "creator": "JD", "modifier": "JD", "hasRedInfo": false, "failureMsg": "{0}", "isellId": ""}]}}